package com.hukapp.service.auth.common.cache;

import static org.junit.jupiter.api.Assertions.*;

import java.lang.reflect.Field;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ExpiringMapBeanTest {

    private InMemoryJsidCache expiringMapBean;

    @BeforeEach
    void setUp() {
        expiringMapBean = new InMemoryJsidCache();
    }

    @Test
    void testPutAndGet() {
        // Given
        String key = "testKey";
        String value = "testValue";
        
        // When
        expiringMapBean.put(key, value);
        
        // Then
        assertEquals(value, expiringMapBean.get(key));
    }

    @Test
    void testRemove() {
        // Given
        String key = "testKey";
        String value = "testValue";
        expiringMapBean.put(key, value);
        
        // When
        String removedValue = expiringMapBean.remove(key);
        
        // Then
        assertEquals(value, removedValue);
        assertNull(expiringMapBean.get(key));
    }

    @Test
    void testContainsKey() {
        // Given
        String key = "testKey";
        String value = "testValue";
        
        // When
        expiringMapBean.put(key, value);
        
        // Then
        assertTrue(expiringMapBean.containsKey(key));
        assertFalse(expiringMapBean.containsKey("nonExistentKey"));
    }

    @Test
    void testClear() {
        // Given
        expiringMapBean.put("key1", "value1");
        expiringMapBean.put("key2", "value2");
        
        // When
        expiringMapBean.clear();
        
        // Then
        assertEquals(0, expiringMapBean.size());
    }

    @Test
    void testExpiration() throws Exception {
        // Given
        String key = "expiringKey";
        String value = "expiringValue";
        expiringMapBean.put(key, value);
        
        // Access the cache map using reflection
        Field cacheField = InMemoryJsidCache.class.getDeclaredField("cache");
        cacheField.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> cache = (Map<String, Object>) cacheField.get(expiringMapBean);
        Object cacheEntry = cache.get(key);
        
        // Set the expiration time to the past using reflection
        Field expirationTimeField = cacheEntry.getClass().getDeclaredField("expirationTime");
        expirationTimeField.setAccessible(true);
        expirationTimeField.set(cacheEntry, Instant.now().minus(1, ChronoUnit.HOURS));
        
        // When & Then
        assertNull(expiringMapBean.get(key), "Entry should be expired and return null");
        assertFalse(expiringMapBean.containsKey(key), "containsKey should return false for expired entry");
    }

    @Test
    void testCleanupExpiredEntries() throws Exception {
        // Given
        expiringMapBean.put("key1", "value1");
        expiringMapBean.put("key2", "value2");
        expiringMapBean.put("expiredKey", "expiredValue");
        
        // Access the cache map using reflection
        Field cacheField = InMemoryJsidCache.class.getDeclaredField("cache");
        cacheField.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> cache = (Map<String, Object>) cacheField.get(expiringMapBean);
        Object cacheEntry = cache.get("expiredKey");
        
        // Set the expiration time to the past using reflection
        Field expirationTimeField = cacheEntry.getClass().getDeclaredField("expirationTime");
        expirationTimeField.setAccessible(true);
        expirationTimeField.set(cacheEntry, Instant.now().minus(1, ChronoUnit.HOURS));
        
        // When
        expiringMapBean.cleanupExpiredEntries();
        
        // Then
        assertEquals(2, expiringMapBean.size());
        assertTrue(expiringMapBean.containsKey("key1"));
        assertTrue(expiringMapBean.containsKey("key2"));
        assertFalse(expiringMapBean.containsKey("expiredKey"));
    }
}
