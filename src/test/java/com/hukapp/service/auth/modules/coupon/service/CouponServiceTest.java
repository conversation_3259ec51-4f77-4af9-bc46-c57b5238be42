package com.hukapp.service.auth.modules.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.coupon.dto.request.CreateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.response.CouponResponseDto;
import com.hukapp.service.auth.modules.coupon.entity.Coupon;
import com.hukapp.service.auth.modules.coupon.enums.DiscountType;
import com.hukapp.service.auth.modules.coupon.exception.CouponExpiredException;
import com.hukapp.service.auth.modules.coupon.exception.CouponUsageLimitExceededException;
import com.hukapp.service.auth.modules.coupon.mapper.CouponMapper;
import com.hukapp.service.auth.modules.coupon.repository.CouponRepository;
import com.hukapp.service.auth.modules.coupon.service.impl.CouponServiceImpl;

@ExtendWith(MockitoExtension.class)
class CouponServiceTest {

    @Mock
    private CouponRepository couponRepository;

    @Mock
    private CouponMapper couponMapper;

    @InjectMocks
    private CouponServiceImpl couponService;

    private CreateCouponDto createCouponDto;
    private Coupon coupon;
    private CouponResponseDto couponResponseDto;

    @BeforeEach
    void setUp() {
        createCouponDto = CreateCouponDto.builder()
                .code("SUMMER2024")
                .description("Summer discount")
                .discountType(DiscountType.PERCENTAGE)
                .discountValue(new BigDecimal("10.00"))
                .usageLimit(100)
                .expirationDate(LocalDateTime.now().plusDays(30))
                .active(true)
                .build();

        coupon = Coupon.builder()
                .id(1L)
                .code("SUMMER2024")
                .description("Summer discount")
                .discountType(DiscountType.PERCENTAGE)
                .discountValue(new BigDecimal("10.00"))
                .usageLimit(100)
                .currentUsageCount(0)
                .expirationDate(LocalDateTime.now().plusDays(30))
                .active(true)
                .build();

        couponResponseDto = CouponResponseDto.builder()
                .id(1L)
                .code("SUMMER2024")
                .description("Summer discount")
                .discountType(DiscountType.PERCENTAGE)
                .discountValue(new BigDecimal("10.00"))
                .usageLimit(100)
                .currentUsageCount(0)
                .expirationDate(LocalDateTime.now().plusDays(30))
                .active(true)
                .isValid(true)
                .isExpired(false)
                .isUsageLimitExceeded(false)
                .build();
    }

    @Test
    void createCoupon_Success() {
        // Given
        when(couponRepository.existsByCode("SUMMER2024")).thenReturn(false);
        when(couponMapper.toEntity(createCouponDto)).thenReturn(coupon);
        when(couponRepository.save(coupon)).thenReturn(coupon);
        when(couponMapper.toResponseDto(coupon)).thenReturn(couponResponseDto);

        // When
        CouponResponseDto result = couponService.createCoupon(createCouponDto);

        // Then
        assertNotNull(result);
        assertEquals("SUMMER2024", result.getCode());
        assertEquals(DiscountType.PERCENTAGE, result.getDiscountType());
        assertEquals(new BigDecimal("10.00"), result.getDiscountValue());
        verify(couponRepository).existsByCode("SUMMER2024");
        verify(couponRepository).save(coupon);
    }

    @Test
    void createCoupon_DuplicateCode_ThrowsException() {
        // Given
        when(couponRepository.existsByCode("SUMMER2024")).thenReturn(true);

        // When & Then
        assertThrows(ResourceAlreadyExistsException.class, 
                () -> couponService.createCoupon(createCouponDto));
        verify(couponRepository).existsByCode("SUMMER2024");
        verify(couponRepository, never()).save(any());
    }

    @Test
    void validateAndCalculateDiscount_PercentageDiscount_Success() {
        // Given
        BigDecimal originalAmount = new BigDecimal("100.00");
        when(couponRepository.findByCode("SUMMER2024")).thenReturn(Optional.of(coupon));

        // When
        BigDecimal discount = couponService.validateAndCalculateDiscount("SUMMER2024", originalAmount);

        // Then
        assertEquals(new BigDecimal("10.00"), discount);
    }

    @Test
    void validateAndCalculateDiscount_FixedAmountDiscount_Success() {
        // Given
        coupon.setDiscountType(DiscountType.FIXED_AMOUNT);
        coupon.setDiscountValue(new BigDecimal("25.00"));
        BigDecimal originalAmount = new BigDecimal("100.00");
        when(couponRepository.findByCode("SUMMER2024")).thenReturn(Optional.of(coupon));

        // When
        BigDecimal discount = couponService.validateAndCalculateDiscount("SUMMER2024", originalAmount);

        // Then
        assertEquals(new BigDecimal("25.00"), discount);
    }

    @Test
    void validateAndCalculateDiscount_ExpiredCoupon_ThrowsException() {
        // Given
        coupon.setExpirationDate(LocalDateTime.now().minusDays(1));
        when(couponRepository.findByCode("SUMMER2024")).thenReturn(Optional.of(coupon));

        // When & Then
        assertThrows(CouponExpiredException.class, 
                () -> couponService.validateAndCalculateDiscount("SUMMER2024", new BigDecimal("100.00")));
    }

    @Test
    void validateAndCalculateDiscount_UsageLimitExceeded_ThrowsException() {
        // Given
        coupon.setCurrentUsageCount(100); // Same as usage limit
        when(couponRepository.findByCode("SUMMER2024")).thenReturn(Optional.of(coupon));

        // When & Then
        assertThrows(CouponUsageLimitExceededException.class, 
                () -> couponService.validateAndCalculateDiscount("SUMMER2024", new BigDecimal("100.00")));
    }

    @Test
    void validateAndCalculateDiscount_CouponNotFound_ThrowsException() {
        // Given
        when(couponRepository.findByCode("INVALID")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> couponService.validateAndCalculateDiscount("INVALID", new BigDecimal("100.00")));
    }

    @Test
    void applyCouponUsage_Success() {
        // Given
        when(couponRepository.findByCodeWithLock("SUMMER2024")).thenReturn(Optional.of(coupon));
        when(couponRepository.save(coupon)).thenReturn(coupon);

        // When
        couponService.applyCouponUsage("SUMMER2024");

        // Then
        assertEquals(1, coupon.getCurrentUsageCount());
        verify(couponRepository).save(coupon);
    }

    @Test
    void getCouponById_Success() {
        // Given
        when(couponRepository.findById(1L)).thenReturn(Optional.of(coupon));
        when(couponMapper.toResponseDto(coupon)).thenReturn(couponResponseDto);

        // When
        CouponResponseDto result = couponService.getCouponById(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("SUMMER2024", result.getCode());
    }

    @Test
    void getCouponById_NotFound_ThrowsException() {
        // Given
        when(couponRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> couponService.getCouponById(1L));
    }
}
