package com.hukapp.service.auth.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition(
    info = @Info(title = "AVAS Service API", version = "v1"),
    servers = {
        @Server(url = "/", description = "Default Server URL"),
        @Server(url = "http://localhost:4244", description = "Local Development Server"),
        @Server(url = "https://api.hukapp.com", description = "Production Server"),
        @Server(url = "http://*************:4244", description = "Development Server")
        // Add more servers as needed
    }
)
@SecurityScheme(
    name = "bearerAuth",
    type = SecuritySchemeType.HTTP,
    bearerFormat = "JWT",
    scheme = "bearer"
)
public class OpenApiConfig {
}