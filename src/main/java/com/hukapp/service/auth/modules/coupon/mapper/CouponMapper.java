package com.hukapp.service.auth.modules.coupon.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.coupon.dto.request.CreateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.request.UpdateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.response.CouponResponseDto;
import com.hukapp.service.auth.modules.coupon.entity.Coupon;

/**
 * MapStruct mapper for Coupon entity and DTOs
 */
@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true),
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface CouponMapper {

    /**
     * Convert CreateCouponDto to Coupon entity
     * @param createDto the create DTO
     * @return Coupon entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "currentUsageCount", constant = "0")
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    Coupon toEntity(CreateCouponDto createDto);

    /**
     * Convert Coupon entity to CouponResponseDto
     * @param coupon the coupon entity
     * @return CouponResponseDto
     */
    @Mapping(target = "isValid", expression = "java(coupon.isValid())")
    @Mapping(target = "isExpired", expression = "java(coupon.isExpired())")
    @Mapping(target = "isUsageLimitExceeded", expression = "java(coupon.isUsageLimitExceeded())")
    CouponResponseDto toResponseDto(Coupon coupon);

    /**
     * Update existing Coupon entity with UpdateCouponDto
     * @param updateDto the update DTO
     * @param coupon the existing coupon entity to update
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true) // Code cannot be updated
    @Mapping(target = "currentUsageCount", ignore = true) // Usage count cannot be updated via this method
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntityFromDto(UpdateCouponDto updateDto, @MappingTarget Coupon coupon);
}
