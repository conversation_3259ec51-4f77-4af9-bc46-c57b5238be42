package com.hukapp.service.auth.modules.app.service;

import java.util.List;

import com.hukapp.service.auth.modules.app.dto.request.CaseNoteRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseNoteResponse;

public interface CaseNoteService {
    
    // Create a new case note
    CaseNoteResponse createCaseNote(CaseNoteRequest caseNoteRequest, String userEmail);
    
    // Get all notes for a specific case
    List<CaseNoteResponse> getCaseNotesByCaseNumber(String caseNumber, String userEmail);
    
    // Get all notes for the authenticated user
    List<CaseNoteResponse> getAllCaseNotes(String userEmail);
    
    // Get a specific note by ID
    CaseNoteResponse getCaseNoteById(Long id, String userEmail);
    
    // Update an existing note
    CaseNoteResponse updateCaseNote(Long id, CaseNoteRequest caseNoteRequest, String userEmail);
    
    // Delete a note
    void deleteCaseNote(Long id, String userEmail);
}
