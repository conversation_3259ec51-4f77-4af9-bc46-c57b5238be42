package com.hukapp.service.auth.modules.reminder.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.hukapp.service.auth.modules.reminder.entity.Reminder;

public interface ReminderRepository extends JpaRepository<Reminder, Long> {

    Optional<Reminder> findByIdAndOwnerId(Long id, Long ownerId);

    List<Reminder> findAllByOwnerId(Long id);
}