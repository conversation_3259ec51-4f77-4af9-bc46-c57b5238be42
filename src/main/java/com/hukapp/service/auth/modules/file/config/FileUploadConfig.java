package com.hukapp.service.auth.modules.file.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import jakarta.servlet.MultipartConfigElement;

/**
 * Configuration for file upload settings
 */
@Configuration
public class FileUploadConfig {

    /**
     * Configure multipart file upload settings
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // Set maximum file size (50MB)
        factory.setMaxFileSize(DataSize.ofMegabytes(50));
        
        // Set maximum request size (60MB to allow for form data)
        factory.setMaxRequestSize(DataSize.ofMegabytes(60));
        
        // Set file size threshold for writing to disk
        factory.setFileSizeThreshold(DataSize.ofKilobytes(512));
        
        return factory.createMultipartConfig();
    }

    /**
     * Configure multipart resolver
     */
    @Bean
    public MultipartResolver multipartResolver() {
        return new StandardServletMultipartResolver();
    }
}
