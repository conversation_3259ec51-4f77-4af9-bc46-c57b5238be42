package com.hukapp.service.auth.modules.app.dto.request;

import java.math.BigDecimal;

import com.hukapp.service.auth.modules.app.enums.CaseType;
import com.hukapp.service.auth.modules.app.enums.CrimeType;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Request object for creating or updating case details")
public class CaseDetailsRequest {

    @Schema(description = "Case number", example = "2023/123")
    @NotBlank(message = "Dosya numarası boş olamaz")
    private String caseNumber;

    @Schema(description = "Case type", example = "CEZA_DAVASI")
    @NotNull(message = "Dava türü boş olamaz")
    private CaseType caseType;

    @Schema(description = "Crime type", example = "DOLANDIRICILIK")
    private CrimeType crimeType;

    @Schema(description = "Whether the case is ongoing (derdest)", example = "true")
    @NotNull(message = "Derdest durumu belirtilmelidir")
    private Boolean derdest;

    @Schema(description = "Case value", example = "50000.00")
    @NotNull(message = "Dava değeri boş olamaz")
    @Positive(message = "Dava değeri pozitif olmalıdır")
    private BigDecimal caseValue;

    @Schema(description = "Case reason", example = "Taraflar arasındaki anlaşmazlık...")
    private String caseReason;

    @Schema(description = "Case title", example = "Ahmet Yılmaz vs. XYZ Şirketi")
    @NotBlank(message = "Dava başlığı boş olamaz")
    private String caseTitle;
}
