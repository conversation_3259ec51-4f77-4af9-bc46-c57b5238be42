package com.hukapp.service.auth.modules.app.controller;

import java.util.Arrays;
import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.app.dto.request.CaseDetailsRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseDetailsResponse;
import com.hukapp.service.auth.modules.app.dto.response.CaseTypeOptionsResponse;
import com.hukapp.service.auth.modules.app.dto.response.CaseTypeOptionsResponse.TypeOption;
import com.hukapp.service.auth.modules.app.enums.CaseType;
import com.hukapp.service.auth.modules.app.enums.CrimeType;
import com.hukapp.service.auth.modules.app.service.CaseDetailsService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/case-details")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Case Details", description = "API for managing case details with case type, crime type, derdest status, case value, reason, and title")
public class CaseDetailsController {

    private final CaseDetailsService caseDetailsService;

    @PostMapping
    @Operation(
        summary = "Create new case details",
        description = """
            Creates a new case details record with the provided information.

            Available Case Types: CEZA_DAVASI (Ceza Davası), HUKUK_DAVASI (Hukuk Davası), IDARI_DAVA (İdari Dava),
            ICRA_TAKIBI (İcra Takibi), ARABULUCULUK (Arabuluculuk), TAHKIM (Tahkim), DIGER (Diğer)

            Available Crime Types: DOLANDIRICILIK (Dolandırıcılık), HIRSIZLIK (Hırsızlık), KASTEN_YARALAMA (Kasten Yaralama),
            TAKSIRLE_YARALAMA (Taksirle Yaralama), KASTEN_OLDURME (Kasten Öldürme), TAKSIRLE_OLDURME (Taksirle Öldürme),
            HAKARET (Hakaret), TEHDIT (Tehdit), UYUSTURUCU (Uyuşturucu Madde Ticareti), CINSEL_SUC (Cinsel Suçlar),
            ZIMMET (Zimmet), RUSVET (Rüşvet), SAHTECILIK (Sahtecilik), VERGI_SUCU (Vergi Suçları), DIGER (Diğer)
            """,
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Case details created successfully",
                content = @Content(schema = @Schema(implementation = CaseDetailsResponse.class))
            )
        }
    )
    public ResponseEntity<CaseDetailsResponse> createCaseDetails(
            @Valid @RequestBody CaseDetailsRequest caseDetailsRequest,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Creating case details for case number: {}", caseDetailsRequest.getCaseNumber());
        CaseDetailsResponse response = caseDetailsService.createCaseDetails(caseDetailsRequest, authentication.getName());
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping
    @Operation(
        summary = "Get all case details",
        description = "Retrieves all case details for the authenticated user",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Case details retrieved successfully"
            )
        }
    )
    public ResponseEntity<List<CaseDetailsResponse>> getAllCaseDetails(
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Retrieving all case details for user: {}", authentication.getName());
        List<CaseDetailsResponse> caseDetailsList = caseDetailsService.getAllCaseDetails(authentication.getName());
        return ResponseEntity.ok(caseDetailsList);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get case details by ID",
        description = "Retrieves case details by its ID",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Case details retrieved successfully",
                content = @Content(schema = @Schema(implementation = CaseDetailsResponse.class))
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Case details not found"
            )
        }
    )
    public ResponseEntity<CaseDetailsResponse> getCaseDetailsById(
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Retrieving case details with ID: {}", id);
        CaseDetailsResponse caseDetails = caseDetailsService.getCaseDetailsById(id, authentication.getName());
        return ResponseEntity.ok(caseDetails);
    }

    @GetMapping("/by-case-number")
    @Operation(
        summary = "Get case details by case number",
        description = "Retrieves case details by its case number",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Case details retrieved successfully",
                content = @Content(schema = @Schema(implementation = CaseDetailsResponse.class))
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Case details not found"
            )
        }
    )
    public ResponseEntity<CaseDetailsResponse> getCaseDetailsByCaseNumber(
            @RequestParam String caseNumber,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Retrieving case details for case number: {}", caseNumber);
        CaseDetailsResponse caseDetails = caseDetailsService.getCaseDetailsByCaseNumber(caseNumber, authentication.getName());
        return ResponseEntity.ok(caseDetails);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update case details",
        description = "Updates an existing case details record",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Case details updated successfully",
                content = @Content(schema = @Schema(implementation = CaseDetailsResponse.class))
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Case details not found"
            )
        }
    )
    public ResponseEntity<CaseDetailsResponse> updateCaseDetails(
            @PathVariable Long id,
            @Valid @RequestBody CaseDetailsRequest caseDetailsRequest,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Updating case details with ID: {}", id);
        CaseDetailsResponse response = caseDetailsService.updateCaseDetails(id, caseDetailsRequest, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete case details",
        description = "Deletes an existing case details record",
        responses = {
            @ApiResponse(
                responseCode = "204",
                description = "Case details deleted successfully"
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Case details not found"
            )
        }
    )
    public ResponseEntity<Void> deleteCaseDetails(
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Deleting case details with ID: {}", id);
        caseDetailsService.deleteCaseDetails(id, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/types")
    @Operation(
        summary = "Get available case types and crime types",
        description = "Returns all available case types and crime types with their display names",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Types retrieved successfully",
                content = @Content(schema = @Schema(implementation = CaseTypeOptionsResponse.class))
            )
        }
    )
    public ResponseEntity<CaseTypeOptionsResponse> getAvailableTypes() {
        log.debug("Retrieving available case types and crime types");

        List<TypeOption> caseTypeOptions = Arrays.stream(CaseType.values())
                .map(type -> new TypeOption(type.name(), type.getDisplayName()))
                .toList();

        List<TypeOption> crimeTypeOptions = Arrays.stream(CrimeType.values())
                .map(type -> new TypeOption(type.name(), type.getDisplayName()))
                .toList();

        CaseTypeOptionsResponse response = CaseTypeOptionsResponse.builder()
                .caseTypes(caseTypeOptions)
                .crimeTypes(crimeTypeOptions)
                .build();

        return ResponseEntity.ok(response);
    }
}
