package com.hukapp.service.auth.modules.compensation.entity;

import com.hukapp.service.auth.common.entity.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompensationParameter extends BaseEntity {

    @Column(nullable = false, unique = true)
    private String parameterName;

    @Column(nullable = false)
    private double parameterValue;

    @Column(columnDefinition = "text")
    private String parameterDescription;
    
}
