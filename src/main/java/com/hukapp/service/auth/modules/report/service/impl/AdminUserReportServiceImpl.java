package com.hukapp.service.auth.modules.report.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.modules.payment.entity.AvasPayment;
import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;
import com.hukapp.service.auth.modules.payment.enums.ProductType;
import com.hukapp.service.auth.modules.payment.repository.PaymentRepository;
import com.hukapp.service.auth.modules.payment.service.SubscriptionValidationService;
import com.hukapp.service.auth.modules.person.entity.AvasRole;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.report.dto.response.AdminReportAboutUser;
import com.hukapp.service.auth.modules.report.service.AdminUserReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of AdminUserReportService for generating comprehensive admin reports about users
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AdminUserReportServiceImpl implements AdminUserReportService {

    private final PersonService personService;
    private final PaymentRepository paymentRepository;
    private final SubscriptionValidationService subscriptionValidationService;

    @Override
    public List<AdminReportAboutUser> generateAllUserReports() {
        log.debug("Generating admin reports for all users");

        // Get all users from the system
        List<Person> allUsers = personService.getAllPersons();
        
        log.info("Generating admin reports for {} users", allUsers.size());

        // Generate report for each user
        List<AdminReportAboutUser> reports = allUsers.stream()
                .map(this::generateUserReport)
                .toList();

        log.info("Successfully generated {} admin user reports", reports.size());
        return reports;
    }

    /**
     * Generates an admin report for a single user
     * 
     * @param person The user to generate the report for
     * @return AdminReportAboutUser containing the user's information
     */
    private AdminReportAboutUser generateUserReport(Person person) {
        log.debug("Generating admin report for user: {}", person.getEmail());

        try {
            // Get user roles
            Set<String> roles = person.getRoles().stream()
                    .map(AvasRole::getRoleName)
                    .collect(Collectors.toSet());

            // Get subscription level
            ProductType subscriptionLevel = subscriptionValidationService.getHighestSubscriptionLevel(person);

            // Calculate total payment amount
            BigDecimal totalPaymentAmount = calculateTotalPaymentAmount(person);

            // Build and return the admin report
            AdminReportAboutUser report = AdminReportAboutUser.builder()
                    .userId(person.getId())
                    .createdAt(person.getCreatedAt())
                    .isNewUser(person.isNewUser())
                    .roles(roles)
                    .subscriptionLevel(subscriptionLevel)
                    .totalPaymentAmount(totalPaymentAmount)
                    .build();

            log.debug("Generated admin report for user: {} with subscription level: {} and total payments: {}", 
                    person.getEmail(), subscriptionLevel, totalPaymentAmount);

            return report;

        } catch (Exception e) {
            log.error("Error generating admin report for user: {}", person.getEmail(), e);
            
            // Return a basic report with error handling
            return AdminReportAboutUser.builder()
                    .userId(person.getId())
                    .createdAt(person.getCreatedAt())
                    .isNewUser(person.isNewUser())
                    .roles(Set.of()) // Empty set on error
                    .subscriptionLevel(null) // Null on error
                    .totalPaymentAmount(BigDecimal.ZERO) // Zero on error
                    .build();
        }
    }

    /**
     * Calculates the total payment amount for a user from all successful payments
     * 
     * @param person The user to calculate payments for
     * @return Total amount of successful payments
     */
    private BigDecimal calculateTotalPaymentAmount(Person person) {
        log.debug("Calculating total payment amount for user: {}", person.getEmail());

        try {
            // Get all successful payments for the user
            List<AvasPayment> successfulPayments = paymentRepository.findAllByStatusAndOwner(PaymentStatus.SUCCESS, person);

            // Calculate total amount from successful payments
            BigDecimal totalAmount = successfulPayments.stream()
                    .map(AvasPayment::getPaidPrice) // Use paidPrice as it reflects the actual amount paid
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("User {} has {} successful payments totaling: {}", 
                    person.getEmail(), successfulPayments.size(), totalAmount);

            return totalAmount;

        } catch (Exception e) {
            log.error("Error calculating total payment amount for user: {}", person.getEmail(), e);
            return BigDecimal.ZERO;
        }
    }
}
