package com.hukapp.service.auth.modules.office.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Request object for creating or updating a client note")
public class ClientNoteRequest {

    @Schema(description = "Title of the note", example = "Meeting Notes")
    @NotBlank(message = "Not başlığı boş olamaz")
    private String noteTitle;
    
    @Schema(description = "Content of the note", example = "Client meeting notes and important details.")
    @NotBlank(message = "Not içeriği boş olamaz")
    private String noteContent;
}
