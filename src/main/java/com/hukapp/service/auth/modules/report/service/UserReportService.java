package com.hukapp.service.auth.modules.report.service;

import com.hukapp.service.auth.modules.report.dto.response.UserReportResponse;

public interface UserReportService {

    /**
     * Generates a comprehensive report for the authenticated user
     *
     * @param userEmail The email of the authenticated user
     * @param includePhoto Whether to include the user's photo in the report
     * @return A detailed report containing all available user information
     */
    UserReportResponse generateUserReport(String userEmail, boolean includePhoto);
}
