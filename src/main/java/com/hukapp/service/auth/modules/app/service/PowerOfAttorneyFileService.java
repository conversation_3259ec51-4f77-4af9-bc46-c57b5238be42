package com.hukapp.service.auth.modules.app.service;

import com.hukapp.service.auth.modules.app.dto.request.PoaFileUploadDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileDownloadDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileListDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileResponseDto;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Service interface for Power of Attorney file management operations
 */
public interface PowerOfAttorneyFileService {

    /**
     * Upload a file for a Power of Attorney
     * 
     * @param poaFileUploadDto the file upload request
     * @param uploaderEmail the email of the user uploading the file
     * @return the uploaded file response
     */
    PoaFileResponseDto uploadFile(PoaFileUploadDto poaFileUploadDto, String uploaderEmail);

    /**
     * Download a file by ID
     * 
     * @param fileId the file ID
     * @param userEmail the email of the user requesting the download
     * @return the file download data
     */
    PoaFileDownloadDto downloadFile(Long fileId, String userEmail);

    /**
     * Get file metadata for download (without content)
     * 
     * @param fileId the file ID
     * @param userEmail the email of the user requesting the metadata
     * @return the file download metadata
     */
    PoaFileDownloadDto getFileMetadata(Long fileId, String userEmail);

    /**
     * List all files for a specific Power of Attorney
     *
     * @param powerOfAttorneyId the Power of Attorney ID
     * @param userEmail the email of the user requesting the list
     * @return list of files for the Power of Attorney
     */
    List<PoaFileListDto> listFilesByPowerOfAttorney(Long powerOfAttorneyId, String userEmail);

    /**
     * List all files for the authenticated user
     *
     * @param userEmail the email of the user requesting the list
     * @return list of all user's Power of Attorney files
     */
    List<PoaFileListDto> listUserFiles(String userEmail);

    /**
     * Delete a file by ID (hard delete)
     * 
     * @param fileId the file ID
     * @param userEmail the email of the user requesting deletion
     */
    void deleteFile(Long fileId, String userEmail);

    /**
     * Get file details by ID
     * 
     * @param fileId the file ID
     * @param userEmail the email of the user requesting the details
     * @return the file details
     */
    PoaFileResponseDto getFileDetails(Long fileId, String userEmail);

    /**
     * Validate file upload
     * 
     * @param file the multipart file to validate
     * @throws IllegalArgumentException if validation fails
     */
    void validateFileUpload(MultipartFile file);

    /**
     * Search files by filename
     * 
     * @param filename the filename to search for
     * @param userEmail the email of the user performing the search
     * @return list of matching files
     */
    List<PoaFileListDto> searchFilesByFilename(String filename, String userEmail);

    /**
     * Search files by tags
     * 
     * @param tag the tag to search for
     * @param userEmail the email of the user performing the search
     * @return list of matching files
     */
    List<PoaFileListDto> searchFilesByTag(String tag, String userEmail);

    /**
     * Get file count for a specific Power of Attorney
     * 
     * @param powerOfAttorneyId the Power of Attorney ID
     * @param userEmail the email of the user requesting the count
     * @return the number of files
     */
    long getFileCountByPowerOfAttorney(Long powerOfAttorneyId, String userEmail);

    /**
     * Get total file count for the user
     * 
     * @param userEmail the email of the user requesting the count
     * @return the total number of files
     */
    long getTotalFileCount(String userEmail);
}
