package com.hukapp.service.auth.modules.reminder.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.person.entity.Person;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class Reminder extends BaseEntity{

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String title;

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String description;

    private Instant dueDate;

    private long repeatIntervalInHours;

    @ManyToOne
    private Person owner;

    @Enumerated(EnumType.STRING)
    private Priority priority;

    public enum Priority {
        LOW, MEDIUM, HIGH, CRITICAL
    }
}