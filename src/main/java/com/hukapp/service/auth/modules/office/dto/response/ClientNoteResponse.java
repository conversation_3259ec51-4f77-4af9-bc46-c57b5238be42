package com.hukapp.service.auth.modules.office.dto.response;

import java.time.Instant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response object for client note operations")
public class ClientNoteResponse {
    
    @Schema(description = "Note ID", example = "1")
    private Long id;
    
    @Schema(description = "Title of the note", example = "Meeting Notes")
    private String noteTitle;
    
    @Schema(description = "Content of the note", example = "Client meeting notes and important details.")
    private String noteContent;
    
    @Schema(description = "Client ID", example = "1")
    private Long clientId;
    
    @Schema(description = "Client name", example = "John Doe")
    private String clientName;
    
    @Schema(description = "Creation timestamp")
    private Instant createdAt;
    
    @Schema(description = "Last update timestamp")
    private Instant updatedAt;
    
    @Schema(description = "Owner ID", example = "1")
    private Long ownerId;
    
    @Schema(description = "Owner name", example = "<PERSON> Smith")
    private String ownerName;
    
    @Schema(description = "Owner email", example = "<EMAIL>")
    private String ownerEmail;
}
