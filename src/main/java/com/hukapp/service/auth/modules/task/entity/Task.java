package com.hukapp.service.auth.modules.task.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.Instant;
import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.office.entity.Client;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class Task extends BaseEntity {

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String title;

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String description;

    @Enumerated(EnumType.STRING)
    private Priority priority;

    private Instant startDate;

    @Column(nullable = false)
    private Instant dueDate;

    private String caseNumber;

    @ManyToOne
    private Client client;

    @ManyToOne
    private Person reporter;

    @Enumerated(EnumType.STRING)
    private Status status;

    @OneToMany(mappedBy = "task", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    @JsonManagedReference
    private List<Note> notes = Collections.emptyList();

    @Builder.Default
    private TaskType taskType = TaskType.OTHER;

    public enum Priority {
        LOW, MEDIUM, HIGH, CRITICAL
    }

    public enum Status {
        OPEN, IN_PROGRESS, COMPLETED, CANCELLED
    }

    public enum TaskType {
        TASK, TRIAL, MEETING, REMINDER, OTHER
    }
}