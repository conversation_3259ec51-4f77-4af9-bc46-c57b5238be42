package com.hukapp.service.auth.modules.payment.dto;

import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Payment status response")
public class PaymentStatusResponse {

    @Schema(description = "Payment ID", example = "12345678")
    private String paymentId;

    @Schema(description = "Payment status", example = "SUCCESS")
    private PaymentStatus status;

    @Schema(description = "Is payment successful", example = "true")
    private boolean successful;

    @Schema(description = "Error code if payment failed", example = "123")
    private String errorCode;

    @Schema(description = "Error message if payment failed", example = "Payment failed")
    private String errorMessage;
}
