package com.hukapp.service.auth.modules.app.dto.response;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response containing available case types and crime types")
public class CaseTypeOptionsResponse {
    
    @Schema(description = "List of available case types with their display names")
    private List<TypeOption> caseTypes;
    
    @Schema(description = "List of available crime types with their display names")
    private List<TypeOption> crimeTypes;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Type option with code and display name")
    public static class TypeOption {
        
        @Schema(description = "The enum code value to be used in requests", example = "CEZA_DAVASI")
        private String code;
        
        @Schema(description = "The human-readable display name", example = "Ceza Davası")
        private String displayName;
    }
}
