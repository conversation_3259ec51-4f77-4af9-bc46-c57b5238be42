package com.hukapp.service.auth.modules.task.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.task.dto.NoteRequest;
import com.hukapp.service.auth.modules.task.dto.NoteResponse;
import com.hukapp.service.auth.modules.task.entity.Note;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true),
    imports = {Person.class}
)
public interface NoteMapper {
    

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    Note toEntity(NoteRequest noteRequest);

    NoteResponse toDTO(Note note);

}
