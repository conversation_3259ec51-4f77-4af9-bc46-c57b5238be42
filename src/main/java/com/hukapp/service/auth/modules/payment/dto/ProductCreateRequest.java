package com.hukapp.service.auth.modules.payment.dto;

import java.math.BigDecimal;

import com.hukapp.service.auth.modules.payment.enums.ProductType;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Product creation request")
public class ProductCreateRequest {

    @NotBlank(message = "Product name is required")
    @Schema(description = "Product name", example = "Premium Subscription")
    private String name;

    @NotNull(message = "Product price is required")
    @DecimalMin(value = "0.01", message = "Price must be greater than 0")
    @Schema(description = "Product price", example = "99.99")
    private BigDecimal price;

    @NotNull(message = "Validity period is required")
    @Min(value = 1, message = "Validity period must be at least 1 month")
    @Max(value = 12, message = "Validity period cannot exceed 12 months (1 year)")
    @Schema(description = "Product validity period in months after purchase", example = "1")
    private Integer validityPeriodInMonths;

    @NotNull(message = "Product type is required")
    private ProductType type;

}
