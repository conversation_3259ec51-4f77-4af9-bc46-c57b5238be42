package com.hukapp.service.auth.modules.payment.service;

import java.util.List;

import com.hukapp.service.auth.modules.payment.dto.ProductCreateRequest;
import com.hukapp.service.auth.modules.payment.dto.ProductResponse;
import com.hukapp.service.auth.modules.payment.dto.ProductUpdateRequest;
import com.hukapp.service.auth.modules.payment.entity.Product;

public interface ProductService {

    /**
     * Get all products
     * @return List of all products
     */
    List<ProductResponse> getAllProducts();

    /**
     * Get product by ID
     * @param productId Product ID
     * @return Product response
     */
    ProductResponse getProductById(Long productId);

    /**
     * Get product entity by ID or throw exception
     * @param productId Product ID
     * @return Product entity
     */
    Product getProductByIdOrElseThrow(Long productId);

    /**
     * Create a new product
     * @param request Product creation request
     * @return Created product response
     */
    ProductResponse createProduct(ProductCreateRequest request);

    /**
     * Update an existing product
     * @param productId Product ID to update
     * @param request Product update request
     * @return Updated product response
     */
    ProductResponse updateProduct(Long productId, ProductUpdateRequest request);

    /**
     * Delete a product by ID
     * @param productId Product ID to delete
     */
    void deleteProduct(Long productId);

}
