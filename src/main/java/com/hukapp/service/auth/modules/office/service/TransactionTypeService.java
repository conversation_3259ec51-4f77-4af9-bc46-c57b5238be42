package com.hukapp.service.auth.modules.office.service;

import java.util.List;

import com.hukapp.service.auth.modules.office.dto.TransactionTypeRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionTypeResponse;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;

public interface TransactionTypeService {

        TransactionTypeResponse createTransactionType(TransactionTypeRequest entity);

        TransactionTypeResponse updateTransactionType(Long id, TransactionTypeRequest entity);

        void deleteTransactionType(Long id);

        List<TransactionTypeResponse> getAllTransactionTypes(TransactionCategory category);

}
