package com.hukapp.service.auth.modules.audit.controller;

import com.hukapp.service.auth.modules.audit.dto.response.AuditLogResponse;
import com.hukapp.service.auth.modules.audit.dto.response.AuditLogStatisticsResponse;
import com.hukapp.service.auth.modules.audit.service.AuditLogService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

/**
 * Admin controller for managing and viewing audit logs
 * Provides comprehensive audit log management capabilities for administrators
 */
@RestController
@RequestMapping("/api/admin/audit-logs")
@RequiredArgsConstructor
@Slf4j
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Admin Audit Logs", description = "Admin API for managing and viewing user activity audit logs")
@PreAuthorize("hasRole('ADMIN')")
public class AdminAuditLogController {

    private final AuditLogService auditLogService;
    private final PersonService personService;

    @GetMapping("/user/{userId}")
    @Operation(
        summary = "Get audit logs by user id",
        description = "Retrieve all audit logs for a specific user. Useful for investigating user activity patterns and security incidents."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User audit logs retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getAuditLogsByUser(
            @Parameter(description = "User id to filter audit logs", example = "21")
            @PathVariable Long userId) {
        
        Person person = personService.getPersonByIdOrElseThrow(userId);
        String userEmail = person.getEmail();
        log.info("Admin request to retrieve audit logs for user: {}", userEmail);
        List<AuditLogResponse> auditLogs = auditLogService.getAuditLogsByUser(userEmail);
        log.info("Retrieved {} audit logs for user: {}", auditLogs.size(), userEmail);
        return ResponseEntity.ok(auditLogs);
    }

    @GetMapping("/statistics")
    @Operation(
        summary = "Get audit log statistics",
        description = "Retrieve comprehensive audit log statistics including request counts, user activity, performance metrics, and error rates for the specified date range."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully",
                    content = @Content(schema = @Schema(implementation = AuditLogStatisticsResponse.class))),
        @ApiResponse(responseCode = "400", description = "Invalid date range"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<AuditLogStatisticsResponse> getAuditStatistics(
            @Parameter(description = "Start date (YYYY-MM-DD)", example = "2024-01-01")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date (YYYY-MM-DD)", example = "2024-01-31")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("Admin request to retrieve audit statistics between {} and {}", startDate, endDate);
        
        Instant startInstant = startDate.atStartOfDay().toInstant(ZoneOffset.UTC);
        Instant endInstant = endDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC);
        
        AuditLogStatisticsResponse statistics = auditLogService.getAuditStatistics(startInstant, endInstant);
        log.info("Retrieved audit statistics for date range");
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/user/{userEmail}/date-range")
    @Operation(
        summary = "Get audit logs by user and date range",
        description = "Retrieve audit logs for a specific user within a date range. Useful for detailed investigation of user activity during specific periods."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User audit logs retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid date range"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getAuditLogsByUserAndDateRange(
            @Parameter(description = "User email to filter audit logs", example = "<EMAIL>")
            @PathVariable String userEmail,
            @Parameter(description = "Start date (YYYY-MM-DD)", example = "2024-01-01")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date (YYYY-MM-DD)", example = "2024-01-31")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("Admin request to retrieve audit logs for user: {} between {} and {}", userEmail, startDate, endDate);
        
        Instant startInstant = startDate.atStartOfDay().toInstant(ZoneOffset.UTC);
        Instant endInstant = endDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC);
        
        List<AuditLogResponse> auditLogs = auditLogService.getAuditLogsByUserAndDateRange(userEmail, startInstant, endInstant);
        log.info("Retrieved {} audit logs for user: {} in date range", auditLogs.size(), userEmail);
        return ResponseEntity.ok(auditLogs);
    }

    @GetMapping("/endpoint")
    @Operation(
        summary = "Get audit logs by endpoint",
        description = "Retrieve all audit logs for a specific API endpoint. Useful for analyzing endpoint usage patterns and performance."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Endpoint audit logs retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getAuditLogsByEndpoint(
            @Parameter(description = "Endpoint URL to filter audit logs", example = "/api/user/profile")
            @RequestParam String endpointUrl) {
        log.info("Admin request to retrieve audit logs for endpoint: {}", endpointUrl);
        List<AuditLogResponse> auditLogs = auditLogService.getAuditLogsByEndpoint(endpointUrl);
        log.info("Retrieved {} audit logs for endpoint: {}", auditLogs.size(), endpointUrl);
        return ResponseEntity.ok(auditLogs);
    }

    @GetMapping("/failed-requests")
    @Operation(
        summary = "Get failed requests",
        description = "Retrieve all audit logs for failed requests (HTTP status >= 400). Useful for identifying and investigating system errors and security incidents."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Failed requests retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getFailedRequests() {
        log.info("Admin request to retrieve failed requests");
        List<AuditLogResponse> auditLogs = auditLogService.getFailedRequests();
        log.info("Retrieved {} failed requests", auditLogs.size());
        return ResponseEntity.ok(auditLogs);
    }

    @GetMapping("/slow-requests")
    @Operation(
        summary = "Get slow requests",
        description = "Retrieve all audit logs for slow requests exceeding the specified threshold. Useful for performance monitoring and optimization."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Slow requests retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getSlowRequests(
            @Parameter(description = "Processing time threshold in milliseconds", example = "1000")
            @RequestParam(defaultValue = "1000") Long thresholdMs) {
        log.info("Admin request to retrieve slow requests with threshold: {}ms", thresholdMs);
        List<AuditLogResponse> auditLogs = auditLogService.getSlowRequests(thresholdMs);
        log.info("Retrieved {} slow requests", auditLogs.size());
        return ResponseEntity.ok(auditLogs);
    }

    @GetMapping("/recent")
    @Operation(
        summary = "Get recent audit logs",
        description = "Retrieve the most recent audit logs for quick administrative overview. Limited to recent activity for performance."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Recent audit logs retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getRecentAuditLogs() {
        log.info("Admin request to retrieve recent audit logs");
        List<AuditLogResponse> auditLogs = auditLogService.getRecentAuditLogs();
        log.info("Retrieved {} recent audit logs", auditLogs.size());
        return ResponseEntity.ok(auditLogs);
    }

    @GetMapping("/errors")
    @Operation(
        summary = "Get audit logs with errors",
        description = "Retrieve all audit logs that contain error messages. Useful for system troubleshooting and error analysis."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Error audit logs retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getAuditLogsWithErrors() {
        log.info("Admin request to retrieve audit logs with errors");
        List<AuditLogResponse> auditLogs = auditLogService.getAuditLogsWithErrors();
        log.info("Retrieved {} audit logs with errors", auditLogs.size());
        return ResponseEntity.ok(auditLogs);
    }

    @GetMapping("/client-ip/{clientIp}")
    @Operation(
        summary = "Get audit logs by client IP",
        description = "Retrieve all audit logs for a specific client IP address. Useful for investigating suspicious activity from specific sources."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Client IP audit logs retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getAuditLogsByClientIp(
            @Parameter(description = "Client IP address to filter audit logs", example = "***********")
            @PathVariable String clientIp) {
        log.info("Admin request to retrieve audit logs for client IP: {}", clientIp);
        List<AuditLogResponse> auditLogs = auditLogService.getAuditLogsByClientIp(clientIp);
        log.info("Retrieved {} audit logs for client IP: {}", auditLogs.size(), clientIp);
        return ResponseEntity.ok(auditLogs);
    }

    @DeleteMapping("/cleanup")
    @Operation(
        summary = "Cleanup old audit logs",
        description = "Manually trigger cleanup of old audit logs before the specified cutoff date. Use with caution as this permanently deletes data."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Cleanup completed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid cutoff date"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<String> cleanupOldAuditLogs(
            @Parameter(description = "Cutoff date - logs before this date will be deleted (YYYY-MM-DD)", example = "2023-01-01")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate cutoffDate) {
        
        log.warn("Admin request to cleanup audit logs before: {}", cutoffDate);
        
        Instant cutoffInstant = cutoffDate.atStartOfDay().toInstant(ZoneOffset.UTC);
        auditLogService.cleanupOldAuditLogs(cutoffInstant);
        
        String message = "Successfully cleaned up audit logs before " + cutoffDate;
        log.warn("Completed audit log cleanup before: {}", cutoffDate);
        return ResponseEntity.ok(message);
    }

    @GetMapping("/date-range")
    @Operation(
        summary = "Get audit logs by date range",
        description = "Retrieve all audit logs within a specified date range. Useful for generating reports and analyzing trends over time."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Audit logs retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid date range"),
        @ApiResponse(responseCode = "403", description = "Access denied - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<AuditLogResponse>> getAuditLogsByDateRange(
            @Parameter(description = "Start date (YYYY-MM-DD)", example = "2024-01-01")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date (YYYY-MM-DD)", example = "2024-01-31")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("Admin request to retrieve audit logs between {} and {}", startDate, endDate);
        
        Instant startInstant = startDate.atStartOfDay().toInstant(ZoneOffset.UTC);
        Instant endInstant = endDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC);
        
        List<AuditLogResponse> auditLogs = auditLogService.getAuditLogsByDateRange(startInstant, endInstant);    
        log.info("Retrieved {} audit logs for date range", auditLogs.size());
        return ResponseEntity.ok(auditLogs);
    }
}
