package com.hukapp.service.auth.modules.payment.entity;

import java.math.BigDecimal;

import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.payment.enums.ProductType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class Product extends BaseEntity {

    @Column(nullable = false)
    private BigDecimal price;

    @Column(nullable = false)
    private String name;

    @NotNull
    @Min(1)
    @Column(nullable = false)
    private Integer validityPeriodInMonths;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ProductType type;

}
