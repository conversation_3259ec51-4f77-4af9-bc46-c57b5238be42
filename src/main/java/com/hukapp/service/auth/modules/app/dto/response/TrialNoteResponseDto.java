package com.hukapp.service.auth.modules.app.dto.response;

import java.time.Instant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response object for trial note operations")
public class TrialNoteResponseDto {
    
    @Schema(description = "Unique identifier of the trial note", example = "1")
    private Long id;
    
    @Schema(description = "Case number to which this trial note belongs", example = "2023/123")
    private String caseNumber;
    
    @Schema(description = "Content of the trial note", example = "This is a detailed trial note content describing the proceedings.")
    private String noteContent;
    
    @Schema(description = "Title of the trial note", example = "First Hearing Notes")
    private String noteTitle;
    
    @Schema(description = "Creation timestamp of the trial note")
    private Instant createdAt;
    
    @Schema(description = "Last update timestamp of the trial note")
    private Instant updatedAt;
    
    @Schema(description = "ID of the owner of this trial note", example = "1")
    private Long ownerId;
    
    @Schema(description = "Full name of the owner of this trial note", example = "John Doe")
    private String ownerName;
    
    @Schema(description = "Email of the owner of this trial note", example = "<EMAIL>")
    private String ownerEmail;
}
