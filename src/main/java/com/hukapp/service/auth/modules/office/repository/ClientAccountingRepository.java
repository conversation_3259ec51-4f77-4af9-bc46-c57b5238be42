package com.hukapp.service.auth.modules.office.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.office.entity.ClientAccounting;
import com.hukapp.service.auth.modules.office.enums.AccountingType;
import com.hukapp.service.auth.modules.person.entity.Person;

@Repository
public interface ClientAccountingRepository extends JpaRepository<ClientAccounting, Long> {
    
    /**
     * Find all accounting records by owner
     * @param owner the owner
     * @return list of client accounting records
     */
    List<ClientAccounting> findByOwnerOrderByRecordDateDesc(Person owner);
    
    /**
     * Find all accounting records by client and owner
     * @param client the client
     * @param owner the owner
     * @return list of client accounting records
     */
    List<ClientAccounting> findByClientAndOwnerOrderByRecordDateDesc(Client client, Person owner);
    
    /**
     * Find accounting record by ID and owner
     * @param id the record ID
     * @param owner the owner
     * @return optional client accounting record
     */
    Optional<ClientAccounting> findByIdAndOwner(Long id, Person owner);
    
    /**
     * Find all accounting records by owner and accounting type
     * @param owner the owner
     * @param accountingType the accounting type
     * @return list of client accounting records
     */
    List<ClientAccounting> findByOwnerAndAccountingTypeOrderByRecordDateDesc(Person owner, AccountingType accountingType);
    
    /**
     * Find all accounting records by client, owner and accounting type
     * @param client the client
     * @param owner the owner
     * @param accountingType the accounting type
     * @return list of client accounting records
     */
    List<ClientAccounting> findByClientAndOwnerAndAccountingTypeOrderByRecordDateDesc(Client client, Person owner, AccountingType accountingType);
    
    /**
     * Get total amount by client, owner and accounting type
     * @param client the client
     * @param owner the owner
     * @param accountingType the accounting type
     * @return total amount
     */
    @Query("SELECT COALESCE(SUM(ca.amount), 0) FROM ClientAccounting ca WHERE ca.client = :client AND ca.owner = :owner AND ca.accountingType = :accountingType")
    Double getTotalAmountByClientAndOwnerAndAccountingType(@Param("client") Client client, @Param("owner") Person owner, @Param("accountingType") AccountingType accountingType);
    
}
