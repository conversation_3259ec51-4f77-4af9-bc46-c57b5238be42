package com.hukapp.service.auth.modules.payment.repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.hukapp.service.auth.modules.payment.entity.AvasPayment;
import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;
import com.hukapp.service.auth.modules.payment.enums.ProductType;
import com.hukapp.service.auth.modules.person.entity.Person;

public interface PaymentRepository extends JpaRepository<AvasPayment, Long> {

    List<AvasPayment> findByOwner(Person owner);

    Optional<AvasPayment> findByPaymentId(String paymentId);

    Optional<AvasPayment> findByConversationId(String conversationId);

    Optional<AvasPayment> findByPaymentIdAndOwner(String paymentId, Person owner);

    Optional<AvasPayment> findByConversationIdAndOwner(String conversationId, Person owner);

    List<AvasPayment> findAllByStatusNot(PaymentStatus status);

    List<AvasPayment> findAllByStatusAndOwner(PaymentStatus success, Person person);

    /**
     * Find active payments for a user with valid subscription
     * @param owner the user
     * @param status the payment status (SUCCESS)
     * @param currentTime current timestamp to check validity
     * @return list of active payments
     */
    @Query("SELECT p FROM AvasPayment p WHERE p.owner = :owner AND p.status = :status AND p.validUntil > :currentTime")
    List<AvasPayment> findActivePaymentsByOwner(@Param("owner") Person owner,
                                               @Param("status") PaymentStatus status,
                                               @Param("currentTime") Instant currentTime);

    /**
     * Find the highest subscription level for a user with active payments
     * @param owner the user
     * @param status the payment status (SUCCESS)
     * @param currentTime current timestamp to check validity
     * @return optional of the highest product type
     */
    @Query("SELECT MAX(p.product.type) FROM AvasPayment p WHERE p.owner = :owner AND p.status = :status AND p.validUntil > :currentTime")
    Optional<ProductType> findHighestActiveSubscriptionLevel(@Param("owner") Person owner,
                                                           @Param("status") PaymentStatus status,
                                                           @Param("currentTime") Instant currentTime);

    /**
     * Check if user has active subscription of at least the required level
     * @param owner the user
     * @param status the payment status (SUCCESS)
     * @param currentTime current timestamp to check validity
     * @param requiredLevel the minimum required subscription level
     * @return true if user has valid subscription
     */
    @Query("SELECT COUNT(p) > 0 FROM AvasPayment p WHERE p.owner = :owner AND p.status = :status AND p.validUntil > :currentTime AND p.product.type = :requiredLevel")
    boolean hasActiveSubscriptionOfLevel(@Param("owner") Person owner,
                                       @Param("status") PaymentStatus status,
                                       @Param("currentTime") Instant currentTime,
                                       @Param("requiredLevel") ProductType requiredLevel);
}
