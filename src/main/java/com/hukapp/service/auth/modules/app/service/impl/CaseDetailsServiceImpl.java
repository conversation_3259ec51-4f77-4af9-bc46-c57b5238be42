package com.hukapp.service.auth.modules.app.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.app.dto.request.CaseDetailsRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseDetailsResponse;
import com.hukapp.service.auth.modules.app.entity.CaseDetails;
import com.hukapp.service.auth.modules.app.mapper.CaseDetailsMapper;
import com.hukapp.service.auth.modules.app.repository.CaseDetailsRepository;
import com.hukapp.service.auth.modules.app.repository.CaseRepository;
import com.hukapp.service.auth.modules.app.service.CaseDetailsService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.sync.enums.DataSourceEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class CaseDetailsServiceImpl implements CaseDetailsService {

    private final CaseDetailsRepository caseDetailsRepository;
    private final CaseDetailsMapper caseDetailsMapper;
    private final PersonService personService;
    private final CaseRepository caseRepository;

    @Override
    @Transactional
    public CaseDetailsResponse createCaseDetails(CaseDetailsRequest caseDetailsRequest, String userEmail) {
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        verifyCaseExists(caseDetailsRequest.getCaseNumber(), userEmail);

        // Check if case details already exist for this case number and user
        if (caseDetailsRepository.existsByCaseNumberAndOwner(caseDetailsRequest.getCaseNumber(), owner)) {
            throw new ResourceAlreadyExistsException("Bu dosya numarası için detaylar zaten mevcut: " +
                    caseDetailsRequest.getCaseNumber());
        }

        // Create and save the case details
        CaseDetails caseDetails = caseDetailsMapper.toEntity(caseDetailsRequest);
        caseDetails.setOwner(owner);

        caseDetails = caseDetailsRepository.save(caseDetails);
        log.debug("Created case details with ID: {} for case number: {}", caseDetails.getId(),
                caseDetails.getCaseNumber());

        return caseDetailsMapper.toDTO(caseDetails);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CaseDetailsResponse> getAllCaseDetails(String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        List<CaseDetails> caseDetailsList = caseDetailsRepository.findByOwner(owner);
        log.debug("Retrieved {} case details for user: {}", caseDetailsList.size(), userEmail);

        return caseDetailsList.stream()
                .map(caseDetailsMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public CaseDetailsResponse getCaseDetailsById(Long id, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        CaseDetails caseDetails = caseDetailsRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Dava detayı bulunamadı, ID: " + id));

        log.debug("Retrieved case details with ID: {} for user: {}", id, userEmail);
        return caseDetailsMapper.toDTO(caseDetails);
    }

    @Override
    @Transactional(readOnly = true)
    public CaseDetailsResponse getCaseDetailsByCaseNumber(String caseNumber, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        CaseDetails caseDetails = caseDetailsRepository.findByCaseNumberAndOwner(caseNumber, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Dava detayı bulunamadı, Dosya No: " + caseNumber));

        log.debug("Retrieved case details for case number: {} for user: {}", caseNumber, userEmail);
        return caseDetailsMapper.toDTO(caseDetails);
    }

    @Override
    @Transactional
    public CaseDetailsResponse updateCaseDetails(Long id, CaseDetailsRequest caseDetailsRequest, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        verifyCaseExists(caseDetailsRequest.getCaseNumber(), userEmail);

        CaseDetails existingCaseDetails = caseDetailsRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Dava detayı bulunamadı, ID: " + id));

        // Check if the case number is being changed and if it already exists
        if (!existingCaseDetails.getCaseNumber().equals(caseDetailsRequest.getCaseNumber()) &&
                caseDetailsRepository.existsByCaseNumberAndOwner(caseDetailsRequest.getCaseNumber(), owner)) {
            throw new ResourceAlreadyExistsException("Bu dosya numarası için detaylar zaten mevcut: " +
                    caseDetailsRequest.getCaseNumber());
        }

        // Update the case details
        existingCaseDetails.setCaseNumber(caseDetailsRequest.getCaseNumber());
        existingCaseDetails.setCaseType(caseDetailsRequest.getCaseType());
        existingCaseDetails.setCrimeType(caseDetailsRequest.getCrimeType());
        existingCaseDetails.setDerdest(caseDetailsRequest.getDerdest());
        existingCaseDetails.setCaseValue(caseDetailsRequest.getCaseValue());
        existingCaseDetails.setCaseReason(caseDetailsRequest.getCaseReason());
        existingCaseDetails.setCaseTitle(caseDetailsRequest.getCaseTitle());

        existingCaseDetails = caseDetailsRepository.save(existingCaseDetails);
        log.debug("Updated case details with ID: {}", existingCaseDetails.getId());

        return caseDetailsMapper.toDTO(existingCaseDetails);
    }

    @Override
    @Transactional
    public void deleteCaseDetails(Long id, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        CaseDetails caseDetails = caseDetailsRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Dava detayı bulunamadı, ID: " + id));

        caseDetailsRepository.delete(caseDetails);
        log.debug("Deleted case details with ID: {}", id);
    }

    /**
     * Verifies that a case with the given case number exists for the user
     *
     * @param caseNumber The case number to verify
     * @param userEmail  The email of the user
     * @throws ResourceNotFoundException if the case does not exist
     */
    private void verifyCaseExists(String caseNumber, String userEmail) {
        // Check if the case exists in any of the case data sources
        boolean caseExists = false;

        // Try to find the case in UYAP_CASE_TARAFLAR
        caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                userEmail, DataSourceEnum.UYAP_CASE_TARAFLAR, caseNumber).isPresent();

        if (!caseExists) {
            // Try to find the case in UYAP_CASE_HISTORY
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_HISTORY, caseNumber).isPresent();
        }

        if (!caseExists) {
            // Try to find the case in UYAP_CASE_TAHSILAT_REDDIYAT
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_TAHSILAT_REDDIYAT, caseNumber).isPresent();
        }

        if (!caseExists) {
            throw new ResourceNotFoundException("Dosya bulunamadı, dosya yıl/esas: " + caseNumber);
        }
    }
}
