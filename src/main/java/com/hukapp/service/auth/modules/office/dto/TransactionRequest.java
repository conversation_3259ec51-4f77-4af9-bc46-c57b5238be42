package com.hukapp.service.auth.modules.office.dto;

import java.math.BigDecimal;
import java.time.Instant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Transaction Create Or Update Request")
public class TransactionRequest {

    @NotNull(message = "İşlem Tipi ID gereklidir")
    private Long transactionTypeId;

    private String description;

    @NotNull(message = "İşlem Tutarı gereklidir")
    @Min(value = 1, message = "İşlem Tutarı en az 1 olmalıdır")
    private BigDecimal amount;

    @NotNull(message = "İşlem Tarihi gereklidir")
    private Instant transactionDate;

    private String caseNumber;

}
