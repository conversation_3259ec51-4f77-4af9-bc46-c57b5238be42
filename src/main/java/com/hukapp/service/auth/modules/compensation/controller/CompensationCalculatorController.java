package com.hukapp.service.auth.modules.compensation.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.compensation.dto.CompensationRequest;
import com.hukapp.service.auth.modules.compensation.dto.CompensationResponse;
import com.hukapp.service.auth.modules.compensation.service.CompensationService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("api/user/compensation")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
public class CompensationCalculatorController {

    private final CompensationService compensationService;

    @Operation(summary = "Calculate severance/notice compensation")
    @PostMapping("calculate")
    public ResponseEntity<CompensationResponse> postMethodName(
            @RequestBody @Valid CompensationRequest compensationRequest) {
        return ResponseEntity.ok(compensationService.calculateCompensation(compensationRequest));
    }

}
