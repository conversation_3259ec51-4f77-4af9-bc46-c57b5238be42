package com.hukapp.service.auth.modules.app.repository;

import com.hukapp.service.auth.modules.app.entity.PowerOfAttorney;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorneyFileMetadata;
import com.hukapp.service.auth.modules.person.entity.Person;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for PowerOfAttorneyFileMetadata with custom queries for file management operations
 */
@Repository
public interface PowerOfAttorneyFileRepository extends JpaRepository<PowerOfAttorneyFileMetadata, Long> {

    /**
     * Find file by ID excluding file content for performance (metadata only)
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.id = :id AND f.isDeleted = false AND f.markedForDeletion = false")
    Optional<PowerOfAttorneyFileMetadata> findByIdExcludingContent(@Param("id") Long id);

    /**
     * Find file by ID including file content for download
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f LEFT JOIN FETCH f.content WHERE f.id = :id AND f.isDeleted = false AND f.markedForDeletion = false")
    Optional<PowerOfAttorneyFileMetadata> findByIdIncludingContent(@Param("id") Long id);

    /**
     * Find file by ID and uploader (user authorization check) excluding content
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.id = :id AND f.uploader = :uploader AND f.isDeleted = false AND f.markedForDeletion = false")
    Optional<PowerOfAttorneyFileMetadata> findByIdAndUploaderExcludingContent(@Param("id") Long id, @Param("uploader") Person uploader);

    /**
     * Find file by ID and uploader (user authorization check) including content
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f LEFT JOIN FETCH f.content WHERE f.id = :id AND f.uploader = :uploader AND f.isDeleted = false AND f.markedForDeletion = false")
    Optional<PowerOfAttorneyFileMetadata> findByIdAndUploaderIncludingContent(@Param("id") Long id, @Param("uploader") Person uploader);

    /**
     * Find all files for a specific Power of Attorney and uploader
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.powerOfAttorney = :powerOfAttorney AND f.uploader = :uploader AND f.isDeleted = false AND f.markedForDeletion = false ORDER BY f.createdAt DESC")
    List<PowerOfAttorneyFileMetadata> findByPowerOfAttorneyAndUploader(@Param("powerOfAttorney") PowerOfAttorney powerOfAttorney, @Param("uploader") Person uploader);

    /**
     * Find all files for a specific Power of Attorney ID and uploader
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.powerOfAttorney.id = :powerOfAttorneyId AND f.uploader = :uploader AND f.isDeleted = false AND f.markedForDeletion = false ORDER BY f.createdAt DESC")
    List<PowerOfAttorneyFileMetadata> findByPowerOfAttorneyIdAndUploader(@Param("powerOfAttorneyId") Long powerOfAttorneyId, @Param("uploader") Person uploader);

    /**
     * Find all files for a specific uploader (user's files)
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.uploader = :uploader AND f.isDeleted = false AND f.markedForDeletion = false ORDER BY f.createdAt DESC")
    List<PowerOfAttorneyFileMetadata> findByUploader(@Param("uploader") Person uploader);

    /**
     * Find all active files with sorting (not deleted and not marked for deletion)
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.isDeleted = false AND f.markedForDeletion = false ORDER BY f.createdAt DESC")
    List<PowerOfAttorneyFileMetadata> findAllActive();

    /**
     * Check if file exists by MD5 hash for duplicate detection
     */
    boolean existsByMd5Hash(String md5Hash);

    /**
     * Check if file exists by id
     */
    boolean existsById(@NonNull Long id);

    /**
     * Check if file exists by id and uploader (user authorization check)
     */
    boolean existsByIdAndUploader(Long id, Person uploader);

    /**
     * Count files for a specific Power of Attorney and uploader
     */
    @Query("SELECT COUNT(f) FROM PowerOfAttorneyFileMetadata f WHERE f.powerOfAttorney.id = :powerOfAttorneyId AND f.uploader = :uploader AND f.isDeleted = false AND f.markedForDeletion = false")
    long countByPowerOfAttorneyIdAndUploader(@Param("powerOfAttorneyId") Long powerOfAttorneyId, @Param("uploader") Person uploader);

    /**
     * Count total files for a specific uploader
     */
    @Query("SELECT COUNT(f) FROM PowerOfAttorneyFileMetadata f WHERE f.uploader = :uploader AND f.isDeleted = false AND f.markedForDeletion = false")
    long countByUploader(@Param("uploader") Person uploader);

    /**
     * Find files marked for deletion (for cleanup operations)
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.markedForDeletion = true AND f.isDeleted = false")
    List<PowerOfAttorneyFileMetadata> findFilesMarkedForDeletion();

    /**
     * Find files by original filename pattern and uploader (for search functionality)
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.uploader = :uploader AND LOWER(f.originalFilename) LIKE LOWER(CONCAT('%', :filename, '%')) AND f.isDeleted = false AND f.markedForDeletion = false ORDER BY f.createdAt DESC")
    List<PowerOfAttorneyFileMetadata> findByUploaderAndOriginalFilenameContainingIgnoreCase(@Param("uploader") Person uploader, @Param("filename") String filename);

    /**
     * Find files by tags and uploader (for search functionality)
     */
    @Query("SELECT f FROM PowerOfAttorneyFileMetadata f WHERE f.uploader = :uploader AND LOWER(f.tags) LIKE LOWER(CONCAT('%', :tag, '%')) AND f.isDeleted = false AND f.markedForDeletion = false ORDER BY f.createdAt DESC")
    List<PowerOfAttorneyFileMetadata> findByUploaderAndTagsContainingIgnoreCase(@Param("uploader") Person uploader, @Param("tag") String tag);

    /**
     * Delete file by ID (hard delete)
     */
    @Modifying
    @Query("DELETE FROM PowerOfAttorneyFileMetadata f WHERE f.id = :id")
    void deleteById(@NonNull @Param("id") Long id);
}
