package com.hukapp.service.auth.modules.report.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.report.dto.response.CaseReportResponse;
import com.hukapp.service.auth.modules.report.service.CaseReportService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/cases/report")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Case Reports", description = "API for generating comprehensive case reports")
public class CaseReportController {

    private final CaseReportService caseReportService;

    @GetMapping(produces = "application/json;charset=UTF-8")
    @Operation(
        summary = "Generate detailed case report",
        description = "Generates a comprehensive report for a specific case number, including financial data and task statistics"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Report successfully generated",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = CaseReportResponse.class))
        ),
        @ApiResponse(responseCode = "401", description = "Unauthorized - User not authenticated", content = @Content),
        @ApiResponse(responseCode = "404", description = "Case not found", content = @Content),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    public ResponseEntity<CaseReportResponse> generateCaseReport(
            @Parameter(hidden = true) Authentication authentication,
            @Parameter(
                name = "caseNumber",
                description = "The case number to generate the report for",
                required = true,
                schema = @Schema(type = "string", example = "2023/123")
            )
            @RequestParam(name = "caseNumber") String caseNumber) {
        
        log.debug("Received request to generate case report for case number: {} by user: {}", 
                caseNumber, authentication.getName());
        
        CaseReportResponse report = caseReportService.generateCaseReport(caseNumber, authentication.getName());
        return ResponseEntity.ok(report);
    }
}
