package com.hukapp.service.auth.modules.reminder.controller;

import com.hukapp.service.auth.modules.reminder.dto.ReminderRequest;
import com.hukapp.service.auth.modules.reminder.dto.ReminderResponse;
import com.hukapp.service.auth.modules.reminder.service.ReminderService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * The ReminderController class handles HTTP requests for the /api/reminders
 * endpoint.
 * 
 */
@RestController
@RequestMapping("/api/reminders")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
public class ReminderController {

    private final ReminderService reminderService;

    @Operation(summary = "Get all reminders of the authenticated user")
    @GetMapping
    public List<ReminderResponse> getAllReminders(Authentication authentication) {
        return reminderService.getAllReminders(authentication);
    }

    @Operation(summary = "Get a reminder of the authenticated user by id")
    @GetMapping("/{id}")
    public ReminderResponse getReminderById(@PathVariable Long id, Authentication authentication) {
        return reminderService.getReminderById(id, authentication);
    }

    @Operation(summary = "Create a new reminder for the authenticated user")
    @PostMapping
    public ReminderResponse createReminder(@RequestBody @Valid ReminderRequest requestDTO, Authentication authentication) {
        return reminderService.createReminder(requestDTO, authentication);
    }

    @Operation(summary = "Update a reminder of the authenticated user by id")
    @PutMapping("/{id}")
    public ReminderResponse updateReminder(@PathVariable Long id, @Valid  @RequestBody ReminderRequest requestDTO, Authentication authentication) {
        return reminderService.updateReminder(id, requestDTO, authentication);
    }

    @Operation(summary = "Delete a reminder of the authenticated user by id")
    @DeleteMapping("/{id}")
    public void deleteReminder(@PathVariable Long id, Authentication authentication) {
        reminderService.deleteReminder(id, authentication);
    }
}