package com.hukapp.service.auth.modules.payment.dto;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.modules.payment.enums.ProductType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Product response")
public class ProductResponse {

    @Schema(description = "Product ID", example = "1")
    private Long id;

    @Schema(description = "Product name", example = "Premium Subscription")
    private String name;

    @Schema(description = "Product price", example = "99.99")
    private BigDecimal price;

    @Schema(description = "Creation date")
    private Instant createdAt;

    @Schema(description = "Last update date")
    private Instant updatedAt;

    @Schema(description = "Product validity period in months after purchase", example = "1")
    private Integer validityPeriodInMonths;

    @Schema(description = "Product type", example = "BASIC")
    private ProductType type;

}
