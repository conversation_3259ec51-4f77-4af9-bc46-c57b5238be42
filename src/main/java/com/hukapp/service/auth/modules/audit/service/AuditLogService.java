package com.hukapp.service.auth.modules.audit.service;

import com.hukapp.service.auth.modules.audit.dto.request.AuditLogCreateRequest;
import com.hukapp.service.auth.modules.audit.dto.response.AuditLogResponse;
import com.hukapp.service.auth.modules.audit.dto.response.AuditLogStatisticsResponse;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service interface for audit log operations
 */
public interface AuditLogService {

    /**
     * Save audit log asynchronously
     * This method should not throw exceptions to avoid affecting main API operations
     */
    CompletableFuture<Void> saveAuditLogAsync(AuditLogCreateRequest request);

    /**
     * Get audit logs by user email
     */
    List<AuditLogResponse> getAuditLogsByUser(String userEmail);

    /**
     * Get audit logs by user email within date range
     */
    List<AuditLogResponse> getAuditLogsByUserAndDateRange(String userEmail, Instant startDate, Instant endDate);

    /**
     * Get audit logs by endpoint URL
     */
    List<AuditLogResponse> getAuditLogsByEndpoint(String endpointUrl);

    /**
     * Get failed requests (status codes >= 400)
     */
    List<AuditLogResponse> getFailedRequests();

    /**
     * Get slow requests (processing time > threshold)
     */
    List<AuditLogResponse> getSlowRequests(Long thresholdMs);

    /**
     * Get audit log statistics for admin dashboard
     */
    AuditLogStatisticsResponse getAuditStatistics(Instant startDate, Instant endDate);

    /**
     * Get recent audit logs for admin overview
     */
    List<AuditLogResponse> getRecentAuditLogs();

    /**
     * Get audit logs with errors
     */
    List<AuditLogResponse> getAuditLogsWithErrors();

    /**
     * Get audit logs by client IP address
     */
    List<AuditLogResponse> getAuditLogsByClientIp(String clientIpAddress);

    /**
     * Count audit logs by user email
     */
    long countAuditLogsByUser(String userEmail);

    /**
     * Clean up old audit logs (for maintenance)
     */
    void cleanupOldAuditLogs(Instant cutoffDate);

    /**
     * Get audit logs between start and end date
     */
    List<AuditLogResponse> getAuditLogsByDateRange(Instant startDate, Instant endDate);
}
