package com.hukapp.service.auth.modules.task.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Request object for creating or updating a note")
public class NoteRequest {

    @Schema(description = "Content of the note", example = "This is a sample note content.")
    @NotBlank(message = "Not içeriği boş olamaz")
    private String content;
    
}
