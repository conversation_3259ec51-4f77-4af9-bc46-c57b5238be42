package com.hukapp.service.auth.modules.office.dto;

import java.math.BigDecimal;
import java.time.Instant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Transaction Create Or Update Response")
public class TransactionResponse {

    private Long id;
    private String description;
    private BigDecimal amount;
    private Instant transactionDate;
    private String caseNumber;
    private Instant createdAt;
    private Instant updatedAt;
    private String ownerName;
    private String ownerEmail;
    private TransactionTypeResponse transactionType;

}
