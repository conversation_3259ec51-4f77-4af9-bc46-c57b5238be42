package com.hukapp.service.auth.modules.payment.service.impl;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.cache.ApplicationParameterCache;
import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.payment.dto.CreateOrderRequest;
import com.hukapp.service.auth.modules.payment.dto.PaymentRequest;
import com.hukapp.service.auth.modules.payment.dto.PaymentResponse;
import com.hukapp.service.auth.modules.payment.dto.PaymentStatusResponse;
import com.hukapp.service.auth.modules.payment.entity.AvasPayment;
import com.hukapp.service.auth.modules.payment.entity.Product;
import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;
import com.hukapp.service.auth.modules.payment.mapper.PaymentMapper;
import com.hukapp.service.auth.modules.payment.repository.PaymentRepository;
import com.hukapp.service.auth.modules.payment.service.PaymentService;
import com.hukapp.service.auth.modules.payment.service.ProductService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.coupon.service.CouponService;
import com.iyzipay.Options;
import com.iyzipay.model.Address;
import com.iyzipay.model.BasketItem;
import com.iyzipay.model.BasketItemType;
import com.iyzipay.model.Buyer;
import com.iyzipay.model.CheckoutFormInitialize;
import com.iyzipay.model.Currency;
import com.iyzipay.model.Locale;
import com.iyzipay.model.PaymentGroup;
import com.iyzipay.request.CreateCheckoutFormInitializeRequest;
import com.iyzipay.request.RetrievePaymentRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/*
 * https://docs.iyzico.com/en/payment-methods/direct-charge/checkoutform/cf-implementation/cf-sample-imp. 
 * 
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    private static final String BASKET_ID_PREFIX = "BP";
    private static final String BASKET_ID_SUFFIX = "T";
    private static final String BASKET_ID_SUFFIX2 = "R";
    private static final int BASKET_ID_RANDOM_START = 1000;
    private static final int BASKET_ID_RANDOM_END = 9999;

    private static final String FALLBACK_URL_PARAMETER_KEY = "iyzico.callback.url";

    private final PaymentRepository paymentRepository;
    private final PaymentMapper paymentMapper;
    private final PersonService personService;
    private final Options iyzicoOptions;
    private final ProductService productService;
    private final ApplicationParameterCache applicationParameterCache;
    private final CouponService couponService;

    @Override
    public PaymentResponse createPayment(CreateOrderRequest request, String personEmail) {

        Person person = personService.getPersonByEmailOrElseThrow(personEmail);

        // Get product
        Long productId = request.getProductId();
        Product product = productService.getProductByIdOrElseThrow(productId);

        // Check if person has active subscription for the product type in request
        List<AvasPayment> payments = paymentRepository.findAllByStatusAndOwner(PaymentStatus.SUCCESS, person);
        payments.stream().filter(payment -> payment.getValidUntil().isAfter(Instant.now())).forEach(payment -> {
            if (product.getType().equals(payment.getProduct().getType())) {
                log.info("Person has an active subscription for the product type: {}", product.getType());
                throw new ResourceAlreadyExistsException("Zaten aktif bir aboneliğiniz mevcut.");
            }
        });
        log.info("Person does not have an active subscription for the product type: {}", product.getType());

        // Generate a unique conversation ID
        String conversationId = UUID.randomUUID().toString();

        // Handle coupon application
        BigDecimal originalPrice = product.getPrice();
        BigDecimal finalPrice = originalPrice;
        BigDecimal discountAmount = BigDecimal.ZERO;
        String appliedCouponCode = null;

        if (request.getCouponCode() != null && !request.getCouponCode().trim().isEmpty()) {
            appliedCouponCode = request.getCouponCode().trim();
            log.debug("Applying coupon: {} to payment", appliedCouponCode);

            try {
                discountAmount = couponService.validateAndCalculateDiscount(appliedCouponCode, originalPrice);
                finalPrice = originalPrice.subtract(discountAmount);
                log.info("Coupon {} applied successfully. Original price: {}, Discount: {}, Final price: {}",
                        appliedCouponCode, originalPrice, discountAmount, finalPrice);
            } catch (Exception e) {
                log.warn("Failed to apply coupon {}: {}", appliedCouponCode, e.getMessage());
                throw e; // Re-throw to let the global exception handler deal with it
            }
        }

        // Create payment request
        PaymentRequest paymentRequest = PaymentRequest.builder()
                .price(finalPrice)
                .currency(Currency.TRY.name())
                .basketId(BASKET_ID_PREFIX + productId.toString()
                        + BASKET_ID_SUFFIX + System.currentTimeMillis()
                        + BASKET_ID_SUFFIX2
                        + RandomUtils.insecure().randomInt(BASKET_ID_RANDOM_START, BASKET_ID_RANDOM_END))
                .callbackUrl(applicationParameterCache.getParameterValue(FALLBACK_URL_PARAMETER_KEY))
                .build();

        // Create iyzico checkout form initialize request
        CreateCheckoutFormInitializeRequest iyzicoRequest = createIyzicoRequest(paymentRequest, person, conversationId);

        // Initialize checkout form
        CheckoutFormInitialize checkoutFormInitialize = CheckoutFormInitialize.create(iyzicoRequest, iyzicoOptions);

        // Create and save payment entity
        AvasPayment payment = createPaymentEntity(paymentRequest, person, conversationId);
        payment.setPaymentPageUrl(checkoutFormInitialize.getPaymentPageUrl());
        payment.setPaymentId(checkoutFormInitialize.getToken());
        payment.setProduct(product);

        // Set coupon information
        if (appliedCouponCode != null) {
            payment.setAppliedCouponCode(appliedCouponCode);
            payment.setOriginalPrice(originalPrice);
            payment.setDiscountAmount(discountAmount);
        }

        paymentRepository.save(payment);

        // Create response with payment page URL and coupon information
        PaymentResponse response = paymentMapper.toDTO(payment);
        response.setPaymentPageUrl(checkoutFormInitialize.getPaymentPageUrl());

        if (appliedCouponCode != null) {
            response.setAppliedCouponCode(appliedCouponCode);
            response.setOriginalPrice(originalPrice);
            response.setDiscountAmount(discountAmount);
        }

        return response;
    }

    @Override
    public PaymentResponse getPaymentById(String paymentId, String personEmail) {
        Person person = personService.getPersonByEmailOrElseThrow(personEmail);
        AvasPayment payment = paymentRepository.findByPaymentIdAndOwner(paymentId, person)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found"));

        return paymentMapper.toDTO(payment);
    }

    @Override
    public List<PaymentResponse> getAllPayments(String personEmail) {
        Person person = personService.getPersonByEmailOrElseThrow(personEmail);
        return paymentRepository.findByOwner(person).stream()
                .map(paymentMapper::toDTO)
                .toList();
    }

    @Override
    public PaymentStatusResponse retrievePaymentByPaymentIdAndPerson(String paymentId, String personEmail) {
        Person person = personService.getPersonByEmailOrElseThrow(personEmail);
        
        // Check if payment exists for this person
        paymentRepository.findByPaymentIdAndOwner(paymentId, person)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found"));

        return retrievePaymentByPaymentId(paymentId);
    }

    /**
     * Retrieve payment status from iyzico
     *
     * @param paymentId Payment ID
     * @return Payment status response
     * 
     * https://docs.iyzico.com/ek-servisler/odeme-sorgulama
     */
    @Override
    public PaymentStatusResponse retrievePaymentByPaymentId(String paymentId) {

        AvasPayment payment = paymentRepository.findByPaymentId(paymentId)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found"));

        // Create retrieve request for payment
        RetrievePaymentRequest retrieveRequest = new RetrievePaymentRequest();
        retrieveRequest.setPaymentConversationId(payment.getConversationId());
        // Retrieve payment from iyzico
        com.iyzipay.model.Payment iyzicoPayment = com.iyzipay.model.Payment.retrieve(retrieveRequest, iyzicoOptions);

        // Update payment entity if needed
        if (iyzicoPayment != null && iyzicoPayment.getStatus() != null && iyzicoPayment.getPaymentStatus() != null
                && !payment.getStatus().equals(PaymentStatus.SUCCESS)) {

            payment.setErrorCode(iyzicoPayment.getErrorCode() + " - PaymentStatus: " + iyzicoPayment.getPaymentStatus());
            payment.setErrorMessage(iyzicoPayment.getErrorMessage());
            payment.setErrorGroup(iyzicoPayment.getErrorGroup());

            if ("success".equalsIgnoreCase(iyzicoPayment.getStatus())) {
                // If payment is successful, update the payment entity
                if (iyzicoPayment.getPaymentStatus().equals("SUCCESS")) {

                    payment.setStatus(PaymentStatus.SUCCESS);
                    payment.setPaymentTransactionId(iyzicoPayment.getPaymentId());
                    payment.setErrorCode(null);
                    payment.setErrorMessage(null);
                    payment.setErrorGroup(null);
                    payment.setPaymentDate(Instant.now());
                    // Set valid until date
                    ZonedDateTime validUntil = ZonedDateTime.now()
                            .plusMonths(payment.getProduct().getValidityPeriodInMonths());
                    payment.setValidUntil(validUntil.toInstant());

                    // Apply coupon usage if coupon was used
                    if (payment.getAppliedCouponCode() != null) {
                        try {
                            couponService.applyCouponUsage(payment.getAppliedCouponCode());
                            log.info("Applied coupon usage for successful payment: {}", payment.getAppliedCouponCode());
                        } catch (Exception e) {
                            log.error("Failed to apply coupon usage for payment {}: {}", payment.getId(), e.getMessage());
                            // Don't fail the payment, just log the error
                        }
                    }

                } else {
                    payment.setStatus(PaymentStatus.FAILURE);
                }
                
            } else if ("failure".equalsIgnoreCase(iyzicoPayment.getStatus())) {
                // If payment failed, update the payment entity
                payment.setStatus(PaymentStatus.FAILURE);
            }

            paymentRepository.save(payment);
        }

        // Create and return payment status response
        return PaymentStatusResponse.builder()
                .paymentId(payment.getPaymentId())
                .status(payment.getStatus())
                .successful(PaymentStatus.SUCCESS.equals(payment.getStatus()))
                .errorCode(payment.getErrorCode())
                .errorMessage(payment.getErrorMessage())
                .build();
    }

    private CreateCheckoutFormInitializeRequest createIyzicoRequest(PaymentRequest request, Person person,
            String conversationId) {
        CreateCheckoutFormInitializeRequest iyzicoRequest = new CreateCheckoutFormInitializeRequest();
        iyzicoRequest.setLocale(Locale.TR.getValue());
        iyzicoRequest.setConversationId(conversationId);
        iyzicoRequest.setPrice(request.getPrice());
        iyzicoRequest.setPaidPrice(request.getPrice());
        iyzicoRequest.setCurrency(getCurrency(request.getCurrency()));
        iyzicoRequest
                .setBasketId(request.getBasketId() != null ? request.getBasketId() : "B" + System.currentTimeMillis());
        iyzicoRequest.setPaymentGroup(PaymentGroup.PRODUCT.name());
        iyzicoRequest.setCallbackUrl(request.getCallbackUrl());

        // Set buyer information
        iyzicoRequest.setBuyer(createBuyer(person));

        // Set shipping and billing address
        Address address = createAddress(person);
        iyzicoRequest.setShippingAddress(address);
        iyzicoRequest.setBillingAddress(address);

        // Set basket items
        iyzicoRequest.setBasketItems(createBasketItems(request));

        return iyzicoRequest;
    }

    private Buyer createBuyer(Person person) {
        Buyer buyer = new Buyer();
        buyer.setId(person.getIdentityNumber().toString());
        buyer.setName(person.getName());
        buyer.setSurname(person.getSurname());
        buyer.setEmail(person.getEmail());
        buyer.setIdentityNumber(person.getIdentityNumber().toString());
        buyer.setRegistrationAddress("Turkey");
        buyer.setIp("127.0.0.1");
        buyer.setCity("Istanbul");
        buyer.setCountry("Turkey");

        return buyer;
    }

    private Address createAddress(Person person) {
        Address address = new Address();
        address.setContactName(person.getName() + " " + person.getSurname());
        address.setCity("Istanbul");
        address.setCountry("Turkey");
        address.setAddress("Turkey");

        return address;
    }

    private List<BasketItem> createBasketItems(PaymentRequest request) {
        BasketItem basketItem = new BasketItem();
        basketItem.setId("BI" + System.currentTimeMillis());
        basketItem.setName("AVAS Payment");
        basketItem.setCategory1("Legal Services");
        basketItem.setItemType(BasketItemType.VIRTUAL.name());
        basketItem.setPrice(request.getPrice());

        return List.of(basketItem);
    }

    private String getCurrency(String currencyCode) {
        try {
            return Currency.valueOf(currencyCode).name();
        } catch (IllegalArgumentException e) {
            return Currency.TRY.name();
        }
    }

    private AvasPayment createPaymentEntity(PaymentRequest request, Person person, String conversationId) {

        return AvasPayment.builder()
                .conversationId(conversationId)
                .price(request.getPrice())
                .paidPrice(request.getPrice())
                .currency(request.getCurrency())
                .basketId(request.getBasketId())
                .status(PaymentStatus.PENDING)
                .owner(person)
                .build();
    }

    @Override
    public List<PaymentResponse> getAllPayments() {
        return paymentRepository.findAll().stream()
                .map(paymentMapper::toDTO)
                .toList();
    }


}
