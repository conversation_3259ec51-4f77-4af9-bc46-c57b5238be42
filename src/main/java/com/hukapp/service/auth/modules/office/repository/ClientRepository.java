package com.hukapp.service.auth.modules.office.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.office.entity.Client;

@Repository
public interface ClientRepository extends JpaRepository<Client, Long> {
    
    /**
     * Find all clients by owner ID
     * @param ownerId the owner's ID
     * @return list of clients belonging to the owner
     */
    List<Client> findByOwnerId(Long ownerId);
    
    /**
     * Find a client by ID and owner ID
     * @param id the client ID
     * @param ownerId the owner's ID
     * @return optional client if found and belongs to the owner
     */
    Optional<Client> findByIdAndOwnerId(Long id, Long ownerId);
}
