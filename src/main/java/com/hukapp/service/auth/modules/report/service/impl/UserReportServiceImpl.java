package com.hukapp.service.auth.modules.report.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.app.dto.response.UserPhotoResponse;
import com.hukapp.service.auth.modules.app.service.UyapUserService;
import com.hukapp.service.auth.modules.office.entity.Transaction;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;
import com.hukapp.service.auth.modules.office.repository.TransactionRepository;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.repository.PersonRepository;
import com.hukapp.service.auth.modules.report.dto.response.UserReportResponse;
import com.hukapp.service.auth.modules.report.dto.response.UserReportResponse.FinancialSummarySection;
import com.hukapp.service.auth.modules.report.dto.response.UserReportResponse.TaskSummarySection;
import com.hukapp.service.auth.modules.report.dto.response.UserReportResponse.UserDetailsSection;
import com.hukapp.service.auth.modules.report.service.UserReportService;
import com.hukapp.service.auth.modules.sync.enums.DataSourceEnum;
import com.hukapp.service.auth.modules.task.entity.Task;
import com.hukapp.service.auth.modules.task.entity.Task.Priority;
import com.hukapp.service.auth.modules.task.entity.Task.Status;
import com.hukapp.service.auth.modules.task.repository.TaskRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserReportServiceImpl implements UserReportService {

    private final PersonRepository personRepository;
    private final UyapUserService uyapUserService;
    private final TransactionRepository transactionRepository;
    private final TaskRepository taskRepository;
    private final ObjectMapper objectMapper;

    @Override
    public UserReportResponse generateUserReport(String userEmail, boolean includePhoto) {
        log.debug("Generating comprehensive report for user: {} with includePhoto={}", userEmail, includePhoto);

        // Get basic user information from Person entity
        Person person = personRepository.findByEmail(userEmail)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + userEmail));

        // Build the report with basic user information
        UserReportResponse report = UserReportResponse.builder()
                .id(person.getId())
                .name(person.getName())
                .surname(person.getSurname())
                .fullName(person.getName() + " " + person.getSurname())
                .email(person.getEmail())
                .mobilePhone(person.getMobilePhone())
                .identityNumber(person.getIdentityNumber())
                .birthDate(person.getBirthDate())
                .isEmailVerified(person.isEmailVerified())
                .isMobilePhoneVerified(person.isMobilePhoneVerified())
                .isNewUser(person.isNewUser())
                .reportGeneratedAt(LocalDateTime.now())
                .build();

        // Add UYAP user details if available
        try {
            UserDetailsSection userDetails = getUserDetailsSection(userEmail, includePhoto);
            report.setUyapDetails(userDetails);
        } catch (Exception e) {
            log.warn("Could not retrieve UYAP user details for user: {}", userEmail, e);
        }

        // Add financial summary if available
        try {
            FinancialSummarySection financialSummary = calculateFinancialSummary(person);
            report.setFinancialSummary(financialSummary);
        } catch (Exception e) {
            log.warn("Could not calculate financial summary for user: {}", userEmail, e);
        }

        // Add task summary if available
        try {
            TaskSummarySection taskSummary = calculateTaskSummary(person);
            report.setTaskSummary(taskSummary);
        } catch (Exception e) {
            log.warn("Could not calculate task summary for user: {}", userEmail, e);
        }

        return report;
    }

    private UserDetailsSection getUserDetailsSection(String userEmail, boolean includePhoto) {
        UserDetailsSection section = new UserDetailsSection();

        try {
            // Get user details
            String userDetailsJson = uyapUserService.getUserInfo(userEmail, DataSourceEnum.UYAP_USER_DETAILS);
            Map<String, Object> userDetailsMap = parseJsonToMap(userDetailsJson);
            section.setDetails(userDetailsMap);

            // Get user photo if requested
            if (includePhoto) {
                UserPhotoResponse photoResponse = uyapUserService.getUserPhoto(userEmail);
                section.setPhotoData(photoResponse.getPhoto());
                log.debug("Included photo in report for user: {}", userEmail);
            } else {
                log.debug("Photo not included in report as per request for user: {}", userEmail);
            }
        } catch (Exception e) {
            log.warn("Error retrieving user details for: {}", userEmail, e);
        }

        return section;
    }

    private Map<String, Object> parseJsonToMap(String json) {
        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("Error parsing JSON to Map: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * Calculates financial summary for a user including total income, total expenses, and net income
     *
     * @param person The person entity for whom to calculate the financial summary
     * @return A FinancialSummarySection containing the calculated financial information
     */
    private FinancialSummarySection calculateFinancialSummary(Person person) {
        log.debug("Calculating financial summary for user: {}", person.getEmail());

        // Get all transactions for the user
        List<Transaction> allTransactions = transactionRepository.findAllByOwner(person);

        // Calculate total income
        BigDecimal totalIncome = allTransactions.stream()
                .filter(t -> t.getTransactionType().getCategory() == TransactionCategory.INCOME)
                .map(Transaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Calculate total expenses
        BigDecimal totalExpenses = allTransactions.stream()
                .filter(t -> t.getTransactionType().getCategory() == TransactionCategory.EXPENSE)
                .map(Transaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Calculate net income (income - expenses)
        BigDecimal netIncome = totalIncome.subtract(totalExpenses);

        // Build and return the financial summary section
        return FinancialSummarySection.builder()
                .totalIncome(totalIncome)
                .totalExpenses(totalExpenses)
                .netIncome(netIncome)
                .build();
    }

    /**
     * Calculates task summary for a user including total tasks and counts by status and priority
     *
     * @param person The person entity for whom to calculate the task summary
     * @return A TaskSummarySection containing the calculated task information
     */
    private TaskSummarySection calculateTaskSummary(Person person) {
        log.debug("Calculating task summary for user: {}", person.getEmail());

        // Get all tasks for the user
        List<Task> allTasks = taskRepository.findAllByReporter(person);

        // Calculate total tasks
        int totalTasks = allTasks.size();

        // Group tasks by status and count
        Map<Status, Integer> taskCountsByStatus = allTasks.stream()
                .collect(Collectors.groupingBy(
                    Task::getStatus,
                    () -> new EnumMap<>(Status.class),
                    Collectors.summingInt(task -> 1)
                ));

        // Ensure all status values are represented in the map, even if count is 0
        for (Status status : Status.values()) {
            taskCountsByStatus.putIfAbsent(status, 0);
        }

        // Group tasks by priority and count
        Map<Priority, Integer> taskCountsByPriority = allTasks.stream()
                .collect(Collectors.groupingBy(
                    Task::getPriority,
                    () -> new EnumMap<>(Priority.class),
                    Collectors.summingInt(task -> 1)
                ));

        // Ensure all priority values are represented in the map, even if count is 0
        for (Priority priority : Priority.values()) {
            taskCountsByPriority.putIfAbsent(priority, 0);
        }

        // Group tasks first by status, then by priority and count
        Map<Status, Map<Priority, Integer>> taskCountsByStatusAndPriority = new EnumMap<>(Status.class);

        // Initialize the nested map structure with all status and priority combinations
        for (Status status : Status.values()) {
            Map<Priority, Integer> priorityCounts = new EnumMap<>(Priority.class);
            for (Priority priority : Priority.values()) {
                priorityCounts.put(priority, 0);
            }
            taskCountsByStatusAndPriority.put(status, priorityCounts);
        }

        // Populate the nested map with actual counts
        allTasks.forEach(task -> {
            Status status = task.getStatus();
            Priority priority = task.getPriority();
            if (status != null && priority != null) {
                Map<Priority, Integer> priorityCounts = taskCountsByStatusAndPriority.get(status);
                priorityCounts.put(priority, priorityCounts.get(priority) + 1);
            }
        });

        // Build and return the task summary section
        return TaskSummarySection.builder()
                .totalTasks(totalTasks)
                .taskCountsByStatus(taskCountsByStatus)
                .taskCountsByPriority(taskCountsByPriority)
                .taskCountsByStatusAndPriority(taskCountsByStatusAndPriority)
                .build();
    }
}
