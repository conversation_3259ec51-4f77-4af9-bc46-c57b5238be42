package com.hukapp.service.auth.modules.compensation.constants;

import org.springframework.stereotype.Component;

@Component
public class CompensationParameterKeyConstants {

    private CompensationParameterKeyConstants() {
        // public constructor to prevent instantiation
    }

    public static final String MAX_SEVERANCE_PAY_PER_YEAR = "MAX_SEVERANCE_PAY_PER_YEAR";
    public static final String JOB_SEARCH_LEAVE_HOURS_PER_NOTICE_DAY = "JOB_SEARCH_LEAVE_HOURS_PER_NOTICE_DAY";
    public static final String STAMP_TAX_RATE = "STAMP_TAX_RATE";
    public static final String SEVERANCE_NOTICE_TEXT = "SEVERANCE_NOTICE_TEXT";

    public static final String NOTICE_THRESHOLD_SHORT_MONTHS = "NOTICE_THRESHOLD_SHORT_MONTHS";
    public static final String NOTICE_THRESHOLD_MEDIUM_MONTHS = "NOTICE_THRESHOLD_MEDIUM_MONTHS";
    public static final String NOTICE_THRESHOLD_LONG_MONTHS = "NOTICE_THRESHOLD_LONG_MONTHS";

    public static final String NOTICE_SHORT_DAYS = "NOTICE_SHORT_DAYS";
    public static final String NOTICE_MEDIUM_DAYS = "NOTICE_MEDIUM_DAYS";
    public static final String NOTICE_LONG_DAYS = "NOTICE_LONG_DAYS";
    public static final String NOTICE_MAX_DAYS = "NOTICE_MAX_DAYS";

    public static final String FIRST_INCOME_TAX_THRESHOLD = "FIRST_INCOME_TAX_THRESHOLD";
    public static final String SECOND_INCOME_TAX_THRESHOLD = "SECOND_INCOME_TAX_THRESHOLD";
    public static final String THIRD_INCOME_TAX_THRESHOLD = "THIRD_INCOME_TAX_THRESHOLD";
    public static final String FOURTH_INCOME_TAX_THRESHOLD = "FOURTH_INCOME_TAX_THRESHOLD";
    public static final String FIRST_INCOME_TAX_RATE = "FIRST_INCOME_TAX_RATE";
    public static final String SECOND_INCOME_TAX_RATE = "SECOND_INCOME_TAX_RATE";
    public static final String THIRD_INCOME_TAX_RATE = "THIRD_INCOME_TAX_RATE";
    public static final String FOURTH_INCOME_TAX_RATE = "FOURTH_INCOME_TAX_RATE";
    public static final String MAX_INCOME_TAX_RATE = "MAX_INCOME_TAX_RATE";
    
}
