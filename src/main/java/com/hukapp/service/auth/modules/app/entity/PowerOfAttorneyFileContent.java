package com.hukapp.service.auth.modules.app.entity;

import com.hukapp.service.auth.common.entity.BaseEntity;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.MapsId;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity representing the actual file content for Power of Attorney files.
 * This is separated from metadata for performance optimization with lazy loading.
 */
@Entity
@Table(name = "power_of_attorney_file_content")
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class PowerOfAttorneyFileContent extends BaseEntity {

    @Id
    private Long id;

    /**
     * Reference to the file metadata
     */
    @OneToOne
    @MapsId
    @JoinColumn(name = "id")
    private PowerOfAttorneyFileMetadata metadata;

    /**
     * The actual file content stored as BLOB with lazy loading
     */
    @Lob 
    @Basic(fetch = FetchType.LAZY)
    @Column(nullable = false)
    private byte[] content;
}
