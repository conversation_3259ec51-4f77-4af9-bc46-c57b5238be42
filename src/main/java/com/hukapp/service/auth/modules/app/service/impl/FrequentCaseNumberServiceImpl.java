package com.hukapp.service.auth.modules.app.service.impl;

import java.time.Instant;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.app.cache.FrequentCaseNumberCache;
import com.hukapp.service.auth.modules.app.dto.request.FrequentCaseNumberRequest;
import com.hukapp.service.auth.modules.app.dto.response.FrequentCaseNumberResponse;
import com.hukapp.service.auth.modules.app.entity.FrequentCaseNumber;
import com.hukapp.service.auth.modules.app.mapper.FrequentCaseNumberMapper;
import com.hukapp.service.auth.modules.app.repository.CaseRepository;
import com.hukapp.service.auth.modules.app.repository.FrequentCaseNumberRepository;
import com.hukapp.service.auth.modules.app.service.FrequentCaseNumberService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.sync.enums.DataSourceEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FrequentCaseNumberServiceImpl implements FrequentCaseNumberService {

    private final FrequentCaseNumberRepository frequentCaseNumberRepository;
    private final FrequentCaseNumberMapper frequentCaseNumberMapper;
    private final PersonService personService;
    private final CaseRepository caseRepository;
    private final FrequentCaseNumberCache frequentCaseNumberCache;

    @Override
    @Transactional
    public FrequentCaseNumberResponse addFrequentCaseNumber(FrequentCaseNumberRequest request, String userEmail) {
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Verify the case exists
        verifyCaseExists(request.getCaseNumber(), userEmail);

        // Check if the case number is already in the user's frequently used list
        if (frequentCaseNumberRepository.existsByCaseNumberAndOwner(request.getCaseNumber(), owner)) {
            throw new ResourceAlreadyExistsException("Bu dosya numarası zaten sık kullanılanlar listenizde mevcut: " +
                    request.getCaseNumber());
        }

        // Create and save the frequent case number
        FrequentCaseNumber frequentCaseNumber = frequentCaseNumberMapper.toEntity(request, owner);
        frequentCaseNumber = frequentCaseNumberRepository.save(frequentCaseNumber);

        FrequentCaseNumberResponse response = frequentCaseNumberMapper.toDTO(frequentCaseNumber);

        // Add to cache
        frequentCaseNumberCache.put(userEmail, request.getCaseNumber(), response);

        log.debug("Added case number {} to frequently used list for user: {}", request.getCaseNumber(), userEmail);

        return response;
    }

    @Override
    @Transactional(readOnly = true)
    public List<FrequentCaseNumberResponse> getFrequentCaseNumbers(String userEmail) {
        // Check cache first
        List<FrequentCaseNumberResponse> cachedResponses = frequentCaseNumberCache.getAllForUser(userEmail);
        if (!cachedResponses.isEmpty()) {
            log.debug("Retrieved {} frequently used case numbers from cache for user: {}",
                    cachedResponses.size(), userEmail);
            return cachedResponses;
        }

        // If not in cache, get from database
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Get all frequent case numbers for the user, ordered by last used timestamp
        List<FrequentCaseNumber> frequentCaseNumbers = frequentCaseNumberRepository.findByOwnerOrderByLastUsedAtDesc(owner);

        List<FrequentCaseNumberResponse> responses = frequentCaseNumberMapper.toDTOList(frequentCaseNumbers);

        // Add to cache
        for (FrequentCaseNumber fcn : frequentCaseNumbers) {
            frequentCaseNumberCache.put(userEmail, fcn.getCaseNumber(), frequentCaseNumberMapper.toDTO(fcn));
        }

        log.debug("Retrieved {} frequently used case numbers from database for user: {}",
                frequentCaseNumbers.size(), userEmail);

        return responses;
    }

    @Override
    @Transactional(readOnly = true)
    public FrequentCaseNumberResponse getFrequentCaseNumberById(Long id, String userEmail) {
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Find the frequent case number
        FrequentCaseNumber frequentCaseNumber = frequentCaseNumberRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Sık kullanılan dosya numarası bulunamadı, ID: " + id));

        FrequentCaseNumberResponse response = frequentCaseNumberMapper.toDTO(frequentCaseNumber);

        // Update cache
        frequentCaseNumberCache.put(userEmail, frequentCaseNumber.getCaseNumber(), response);

        log.debug("Retrieved frequently used case number with ID: {} for user: {}", id, userEmail);

        return response;
    }

    @Override
    @Transactional
    public void deleteFrequentCaseNumber(Long id, String userEmail) {
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Find the frequent case number
        FrequentCaseNumber frequentCaseNumber = frequentCaseNumberRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Sık kullanılan dosya numarası bulunamadı, ID: " + id));

        // Delete the frequent case number
        frequentCaseNumberRepository.delete(frequentCaseNumber);

        // Remove from cache
        frequentCaseNumberCache.remove(userEmail, frequentCaseNumber.getCaseNumber());

        log.debug("Deleted frequently used case number with ID: {} for user: {}", id, userEmail);
    }

    @Override
    @Transactional
    public void updateLastUsedTimestamp(String caseNumber, String userEmail) {
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Find the frequent case number
        frequentCaseNumberRepository.findByCaseNumberAndOwner(caseNumber, owner).ifPresent(frequentCaseNumber -> {
            // Update the last used timestamp
            frequentCaseNumber.setLastUsedAt(Instant.now());
            frequentCaseNumber = frequentCaseNumberRepository.save(frequentCaseNumber);

            // Update cache
            FrequentCaseNumberResponse response = frequentCaseNumberMapper.toDTO(frequentCaseNumber);
            frequentCaseNumberCache.put(userEmail, caseNumber, response);

            log.debug("Updated last used timestamp for case number: {} for user: {}", caseNumber, userEmail);
        });
    }

    /**
     * Verifies that a case with the given case number exists for the user
     *
     * @param caseNumber The case number to verify
     * @param userEmail The email of the user
     * @throws ResourceNotFoundException if the case does not exist
     */
    private void verifyCaseExists(String caseNumber, String userEmail) {
        // Check if the case exists in any of the case data sources
        boolean caseExists = false;

        // Try to find the case in UYAP_CASE_TARAFLAR
        caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                userEmail, DataSourceEnum.UYAP_CASE_TARAFLAR, caseNumber).isPresent();

        if (!caseExists) {
            // Try to find the case in UYAP_CASE_HISTORY
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_HISTORY, caseNumber).isPresent();
        }

        if (!caseExists) {
            // Try to find the case in UYAP_CASE_TAHSILAT_REDDIYAT
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_TAHSILAT_REDDIYAT, caseNumber).isPresent();
        }

        if (!caseExists) {
            throw new ResourceNotFoundException("Dosya bulunamadı, dosya numarası: " + caseNumber);
        }
    }
}
