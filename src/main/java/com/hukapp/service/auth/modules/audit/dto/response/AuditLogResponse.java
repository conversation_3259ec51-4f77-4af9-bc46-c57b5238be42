package com.hukapp.service.auth.modules.audit.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * DTO for audit log response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response DTO for audit log entries")
public class AuditLogResponse {

    @Schema(description = "Unique identifier of the audit log", example = "1")
    private Long id;

    @Schema(description = "Full endpoint URL that was accessed", example = "/api/user/profile")
    private String endpointUrl;

    @Schema(description = "HTTP request method", example = "GET")
    private String httpMethod;

    @Schema(description = "Query parameters as JSON string", example = "{\"page\":\"1\",\"size\":\"10\"}")
    private String queryParameters;

    @Schema(description = "Path/URL parameters as JSON string", example = "{\"id\":\"123\"}")
    private String pathParameters;

    @Schema(description = "HTTP response status code", example = "200")
    private Integer responseStatusCode;

    @Schema(description = "Request processing time in milliseconds", example = "150")
    private Long processingTimeMs;

    @Schema(description = "User email from JWT token", example = "<EMAIL>")
    private String userEmail;

    @Schema(description = "User roles from JWT token as JSON array string", example = "[\"ROLE_USER\"]")
    private String userRoles;

    @Schema(description = "Client IP address", example = "***********")
    private String clientIpAddress;

    @Schema(description = "User-Agent header information", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    private String userAgent;

    @Schema(description = "Request timestamp when the request was received")
    private Instant requestTimestamp;

    @Schema(description = "Response timestamp when the response was sent")
    private Instant responseTimestamp;

    @Schema(description = "Additional request headers as JSON string")
    private String requestHeaders;

    @Schema(description = "Request body size in bytes", example = "1024")
    private Long requestBodySize;

    @Schema(description = "Response body size in bytes", example = "2048")
    private Long responseBodySize;

    @Schema(description = "Session ID if available", example = "ABC123XYZ")
    private String sessionId;

    @Schema(description = "Any error message if the request failed")
    private String errorMessage;

    @Schema(description = "When the audit log was created")
    private Instant createdAt;

    @Schema(description = "When the audit log was last updated")
    private Instant updatedAt;

    @Schema(description = "Who created the audit log")
    private String createdBy;

    @Schema(description = "Who last updated the audit log")
    private String updatedBy;
}
