package com.hukapp.service.auth.modules.payment.scheduled;

import java.time.Instant;
import java.util.concurrent.TimeUnit;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;
import com.hukapp.service.auth.modules.payment.repository.PaymentRepository;
import com.hukapp.service.auth.modules.payment.service.PaymentService;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class ProcessPaymentsJob {

    private final PaymentRepository paymentRepository;
    private final PaymentService paymentService;

    @PostConstruct
    @Scheduled(timeUnit = TimeUnit.MINUTES, fixedRate = 3, initialDelay = 1)
    public void processPayments() {

        log.warn("Processing payments...");
        paymentRepository.findAllByStatusNot(PaymentStatus.SUCCESS).stream().forEach(payment -> {
            log.debug("Checking payment status for payment ID: {}", payment.getPaymentId());
            if (payment.getStatus() != PaymentStatus.SUCCESS) {

                paymentService.retrievePaymentByPaymentId(payment.getPaymentId());
                log.debug("Payment status updated for payment ID: {}", payment.getPaymentId());

                /*
                 * If payment is not updated for 600 minutes, delete it since iyzico links expire
                 * after 30 minutes
                 */
                if (payment.getCreatedAt().isBefore(Instant.now().minusSeconds(36000))) {
                    log.debug("Payment status not updated for payment ID: {}", payment.getPaymentId());
                    log.debug("Deleting payment with ID: {}", payment.getPaymentId());
                    paymentRepository.delete(payment);
                    log.debug("Payment deleted with ID: {}", payment.getPaymentId());
                }
            }
        });

    }

}
