package com.hukapp.service.auth.modules.coupon.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.coupon.dto.request.CreateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.request.UpdateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.response.CouponResponseDto;
import com.hukapp.service.auth.modules.coupon.entity.Coupon;
import com.hukapp.service.auth.modules.coupon.enums.DiscountType;
import com.hukapp.service.auth.modules.coupon.exception.CouponException;
import com.hukapp.service.auth.modules.coupon.exception.CouponExpiredException;
import com.hukapp.service.auth.modules.coupon.exception.CouponUsageLimitExceededException;
import com.hukapp.service.auth.modules.coupon.mapper.CouponMapper;
import com.hukapp.service.auth.modules.coupon.repository.CouponRepository;
import com.hukapp.service.auth.modules.coupon.service.CouponService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of CouponService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CouponServiceImpl implements CouponService {

    private final CouponRepository couponRepository;
    private final CouponMapper couponMapper;

    @Override
    public CouponResponseDto createCoupon(CreateCouponDto createDto) {
        log.debug("Creating coupon with code: {}", createDto.getCode());

        // Check if coupon code already exists
        if (couponRepository.existsByCode(createDto.getCode())) {
            throw new ResourceAlreadyExistsException("Bu kupon kodu zaten mevcut: " + createDto.getCode());
        }

        // Validate discount value based on type
        validateDiscountValue(createDto.getDiscountType(), createDto.getDiscountValue());

        Coupon coupon = couponMapper.toEntity(createDto);
        coupon = couponRepository.save(coupon);

        log.info("Created coupon with ID: {} and code: {}", coupon.getId(), coupon.getCode());
        return couponMapper.toResponseDto(coupon);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CouponResponseDto> getAllCoupons(Pageable pageable) {
        log.debug("Getting all coupons with pagination: {}", pageable);
        return couponRepository.findAll(pageable)
                .map(couponMapper::toResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CouponResponseDto> getActiveCoupons(Pageable pageable) {
        log.debug("Getting active coupons with pagination: {}", pageable);
        return couponRepository.findByActive(true, pageable)
                .map(couponMapper::toResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public CouponResponseDto getCouponById(Long id) {
        log.debug("Getting coupon by ID: {}", id);
        Coupon coupon = couponRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Kupon bulunamadı: " + id));
        return couponMapper.toResponseDto(coupon);
    }

    @Override
    @Transactional(readOnly = true)
    public CouponResponseDto getCouponByCode(String code) {
        log.debug("Getting coupon by code: {}", code);
        Coupon coupon = couponRepository.findByCode(code)
                .orElseThrow(() -> new ResourceNotFoundException("Kupon bulunamadı: " + code));
        return couponMapper.toResponseDto(coupon);
    }

    @Override
    public CouponResponseDto updateCoupon(Long id, UpdateCouponDto updateDto) {
        log.debug("Updating coupon with ID: {}", id);

        Coupon existingCoupon = couponRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Kupon bulunamadı: " + id));

        // Validate discount value if being updated
        if (updateDto.getDiscountType() != null && updateDto.getDiscountValue() != null) {
            validateDiscountValue(updateDto.getDiscountType(), updateDto.getDiscountValue());
        } else if (updateDto.getDiscountValue() != null) {
            validateDiscountValue(existingCoupon.getDiscountType(), updateDto.getDiscountValue());
        }

        couponMapper.updateEntityFromDto(updateDto, existingCoupon);
        existingCoupon = couponRepository.save(existingCoupon);

        log.info("Updated coupon with ID: {}", existingCoupon.getId());
        return couponMapper.toResponseDto(existingCoupon);
    }

    @Override
    public void deleteCoupon(Long id) {
        log.debug("Deleting coupon with ID: {}", id);

        Coupon coupon = couponRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Kupon bulunamadı: " + id));

        coupon.setActive(false);
        couponRepository.save(coupon);

        log.info("Deactivated coupon with ID: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CouponResponseDto> searchCouponsByCode(String searchTerm, Pageable pageable) {
        log.debug("Searching coupons by code containing: {}", searchTerm);
        return couponRepository.findByCodeContainingIgnoreCase(searchTerm, pageable)
                .map(couponMapper::toResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal validateAndCalculateDiscount(String couponCode, BigDecimal originalAmount) {
        log.debug("Validating coupon: {} for amount: {}", couponCode, originalAmount);

        Coupon coupon = couponRepository.findByCode(couponCode)
                .orElseThrow(() -> new ResourceNotFoundException("Kupon bulunamadı: " + couponCode));

        // Validate coupon
        if (!coupon.getActive()) {
            throw new CouponException("Kupon aktif değil: " + couponCode);
        }

        if (coupon.isExpired()) {
            throw new CouponExpiredException("Kupon süresi dolmuş: " + couponCode);
        }

        if (coupon.isUsageLimitExceeded()) {
            throw new CouponUsageLimitExceededException("Kupon kullanım limiti aşıldı: " + couponCode);
        }

        // Calculate discount
        BigDecimal discountAmount = calculateDiscountAmount(coupon, originalAmount);
        log.debug("Calculated discount amount: {} for coupon: {}", discountAmount, couponCode);

        return discountAmount;
    }

    @Override
    public void applyCouponUsage(String couponCode) {
        log.debug("Applying coupon usage for: {}", couponCode);

        // Use pessimistic lock to ensure thread-safe update
        Coupon coupon = couponRepository.findByCodeWithLock(couponCode)
                .orElseThrow(() -> new ResourceNotFoundException("Kupon bulunamadı: " + couponCode));

        // Double-check the coupon is still valid before incrementing
        if (!coupon.isValid()) {
            throw new CouponException("Kupon artık geçerli değil: " + couponCode);
        }

        coupon.incrementUsageCount();
        couponRepository.save(coupon);

        log.info("Applied coupon usage for: {}. New usage count: {}", couponCode, coupon.getCurrentUsageCount());
    }

    /**
     * Validate discount value based on discount type
     */
    private void validateDiscountValue(DiscountType discountType, BigDecimal discountValue) {
        if (discountType == DiscountType.PERCENTAGE) {
            if (discountValue.compareTo(BigDecimal.ZERO) <= 0 || discountValue.compareTo(new BigDecimal("100")) > 0) {
                throw new IllegalArgumentException("Yüzde indirimi 0 ile 100 arasında olmalıdır");
            }
        } else if (discountType == DiscountType.FIXED_AMOUNT) {
            if (discountValue.compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalArgumentException("Sabit tutar indirimi 0'dan büyük olmalıdır");
            }
        }
    }

    /**
     * Calculate discount amount based on coupon type and value
     */
    private BigDecimal calculateDiscountAmount(Coupon coupon, BigDecimal originalAmount) {
        if (coupon.getDiscountType() == DiscountType.PERCENTAGE) {
            return originalAmount.multiply(coupon.getDiscountValue())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        } else {
            // For fixed amount, return the discount value but not more than the original amount
            return coupon.getDiscountValue().min(originalAmount);
        }
    }
}
