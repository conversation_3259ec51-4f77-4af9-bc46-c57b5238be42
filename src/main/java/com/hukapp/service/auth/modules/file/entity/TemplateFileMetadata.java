package com.hukapp.service.auth.modules.file.entity;

import java.time.Instant;

import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.file.enums.FileType;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity representing a file stored in the database with metadata
 */
@Entity
@Table(name = "template_file_metadata")
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class TemplateFileMetadata extends BaseEntity {

    /**
     * Original filename as uploaded by the user
     */
    @Column(nullable = false, length = 255)
    @NotBlank(message = "Filename cannot be blank")
    private String originalFilename;

    /**
     * Unique filename generated by the system for storage
     */
    @Column(nullable = false, unique = true, length = 255)
    @NotBlank(message = "Stored filename cannot be blank")
    private String storedFilename;

    /**
     * MIME type of the file
     */
    @Column(nullable = false, length = 100)
    @NotBlank(message = "Content type cannot be blank")
    private String contentType;

    /**
     * File size in bytes
     */
    @Column(nullable = false)
    @NotNull(message = "File size cannot be null")
    @Positive(message = "File size must be positive")
    private Long fileSize;

    /**
     * File type category for organization and validation
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    @NotNull(message = "File type cannot be null")
    private FileType fileType;

    /**
     * The actual file content stored as BLOB
     */
    @OneToOne(mappedBy = "metadata", cascade = CascadeType.ALL, fetch = FetchType.LAZY, optional = false)
    private TemplateFileContent content;

    /**
     * User who uploaded the file
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uploader_id", nullable = false)
    @NotNull(message = "Uploader cannot be null")
    private Person uploader;

    /**
     * Optional description or notes about the file
     */
    @Column(length = 500)
    private String description;

    /**
     * Whether the file is marked for deletion (soft delete)
     */
    @Column(nullable = false)
    @Builder.Default
    private Boolean markedForDeletion = false;

    /**
     * When the file was marked for deletion
     */
    private Instant deletionMarkedAt;

    /**
     * Number of times the file has been downloaded
     */
    @Column(nullable = false)
    @Builder.Default
    private Long downloadCount = 0L;

    /**
     * Last time the file was accessed/downloaded
     */
    private Instant lastAccessedAt;

    /**
     * MD5 hash of the file content for integrity checking
     */
    @Column(length = 32)
    private String md5Hash;

    /**
     * Whether the file is publicly accessible (for future use)
     */
    @Column(nullable = false)
    @Builder.Default
    private Boolean isPublic = false;

    /**
     * Tags associated with the file for categorization
     */
    @Column(length = 500)
    private String tags;

    /**
     * Increments the download count and updates last accessed time
     */
    public void incrementDownloadCount() {
        this.downloadCount = (this.downloadCount == null ? 0L : this.downloadCount) + 1;
        this.lastAccessedAt = Instant.now();
    }

    /**
     * Marks the file for deletion
     */
    public void markForDeletion() {
        this.markedForDeletion = true;
        this.deletionMarkedAt = Instant.now();
    }

    /**
     * Unmarks the file for deletion
     */
    public void unmarkForDeletion() {
        this.markedForDeletion = false;
        this.deletionMarkedAt = null;
    }

    /**
     * Gets the file extension from the original filename
     * @return the file extension or empty string if none
     */
    public String getFileExtension() {
        if (originalFilename == null) {
            return "";
        }
        int lastDotIndex = originalFilename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == originalFilename.length() - 1) {
            return "";
        }
        return originalFilename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * Checks if the file is an image type
     * @return true if the file is an image
     */
    public boolean isImage() {
        return FileType.IMAGE.equals(this.fileType);
    }

    /**
     * Checks if the file is a document type
     * @return true if the file is a document
     */
    public boolean isDocument() {
        return FileType.DOCUMENT.equals(this.fileType) || 
               FileType.SPREADSHEET.equals(this.fileType) || 
               FileType.PRESENTATION.equals(this.fileType);
    }
}
