package com.hukapp.service.auth.modules.compensation.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.modules.compensation.cache.CompensationParametersCache;
import com.hukapp.service.auth.modules.compensation.constants.CompensationParameterKeyConstants;
import com.hukapp.service.auth.modules.compensation.dto.CompensationRequest;
import com.hukapp.service.auth.modules.compensation.dto.CompensationResponse;
import com.hukapp.service.auth.modules.compensation.util.CompensationUtil;
import com.hukapp.service.auth.modules.compensation.util.IncomeTaxCalculator;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CompensationServiceImpl implements CompensationService {

    private final CompensationParametersCache compensationParametersCache;

    private static final long TOTAL_DAYS_IN_MONTH = 30;
    private static final BigDecimal TOTAL_DAYS_IN_YEAR = BigDecimal.valueOf(365);

    @Override
    public CompensationResponse calculateCompensation(CompensationRequest compensationRequest) {

        long totalDays = CompensationUtil.calculateDaysBetweenTwoDates(compensationRequest.getStartDate(),
                compensationRequest.getEndDate());

        BigDecimal calculatedGrossSalary = this.calculateGrossSalary(compensationRequest.getGrossSalary());

        BigDecimal grossSeverancePay = BigDecimal.valueOf(totalDays).multiply(calculatedGrossSalary)
                .divide(TOTAL_DAYS_IN_YEAR, 2, RoundingMode.HALF_UP);
        BigDecimal severancePayStampTax = grossSeverancePay
                .multiply(BigDecimal.valueOf(compensationParametersCache
                        .getParameterValue(CompensationParameterKeyConstants.STAMP_TAX_RATE)));
        BigDecimal netSeverancePay = grossSeverancePay.subtract(severancePayStampTax);

        int noticePeriodInDays = this.calculateNoticePeriodInDays(compensationRequest.getStartDate(),
                compensationRequest.getEndDate());
        BigDecimal grossNoticePay = BigDecimal.valueOf(noticePeriodInDays).multiply(calculatedGrossSalary)
                .divide(BigDecimal.valueOf(TOTAL_DAYS_IN_MONTH), 2, RoundingMode.HALF_UP);
        BigDecimal noticePayStampTax = grossNoticePay
                .multiply(BigDecimal.valueOf(compensationParametersCache
                        .getParameterValue(CompensationParameterKeyConstants.STAMP_TAX_RATE)));
        BigDecimal cumulativeIncomeTaxBasis = Optional.ofNullable(compensationRequest.getCumulativeIncomeTaxBasis())
                .orElse(BigDecimal.ZERO);
        BigDecimal noticePayIncomeTax = this.calculateNoticePayIncomeTax(grossNoticePay, cumulativeIncomeTaxBasis);
        BigDecimal netNoticePay = grossNoticePay.subtract(noticePayStampTax).subtract(noticePayIncomeTax);

        BigDecimal totalCompensation = netSeverancePay.add(netNoticePay);

        return CompensationResponse.builder()
                .totalDays((int) totalDays)
                .grossSeverancePay(CompensationUtil.formatBigDecimalToString(grossSeverancePay))
                .severancePayStampTax(CompensationUtil.formatBigDecimalToString(severancePayStampTax))
                .netSeverancePay(CompensationUtil.formatBigDecimalToString(netSeverancePay))
                .noticePeriodInDays(noticePeriodInDays)
                .jobSearchLeaveHours(noticePeriodInDays * 2)
                .grossNoticePay(CompensationUtil.formatBigDecimalToString(grossNoticePay))
                .noticePayStampTax(CompensationUtil.formatBigDecimalToString(noticePayStampTax))
                .noticePayIncomeTax(CompensationUtil.formatBigDecimalToString(noticePayIncomeTax))
                .netNoticePay(CompensationUtil.formatBigDecimalToString(netNoticePay))
                .totalCompensation(CompensationUtil.formatBigDecimalToString(totalCompensation))
                .severanceNoticeText(compensationParametersCache
                        .getParameterDescription(CompensationParameterKeyConstants.SEVERANCE_NOTICE_TEXT))
                .build();

    }

    private BigDecimal calculateNoticePayIncomeTax(BigDecimal grossNoticePay, BigDecimal cumulativeIncomeTaxBasis) {

        double firstIncomeTaxThreshold = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.FIRST_INCOME_TAX_THRESHOLD);
        double secondIncomeTaxThreshold = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.SECOND_INCOME_TAX_THRESHOLD);
        double thirdIncomeTaxThreshold = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.THIRD_INCOME_TAX_THRESHOLD);
        double fourthIncomeTaxThreshold = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.FOURTH_INCOME_TAX_THRESHOLD);

        double firstIncomeTaxRate = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.FIRST_INCOME_TAX_RATE);
        double secondIncomeTaxRate = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.SECOND_INCOME_TAX_RATE);
        double thirdIncomeTaxRate = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.THIRD_INCOME_TAX_RATE);
        double fourthIncomeTaxRate = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.FOURTH_INCOME_TAX_RATE);
        double fifthIncomeTaxRate = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.MAX_INCOME_TAX_RATE);

        double[] thresholds = { firstIncomeTaxThreshold, secondIncomeTaxThreshold, thirdIncomeTaxThreshold,
                fourthIncomeTaxThreshold };
        double[] rates = { firstIncomeTaxRate, secondIncomeTaxRate, thirdIncomeTaxRate, fourthIncomeTaxRate,
                fifthIncomeTaxRate };
        double cumulativeIncome = cumulativeIncomeTaxBasis.doubleValue();
        double newIncome = grossNoticePay.doubleValue();
        double tax = IncomeTaxCalculator.calculateTax(cumulativeIncome, newIncome, thresholds, rates);

        return BigDecimal.valueOf(tax).setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateGrossSalary(BigDecimal grossSalary) {
        double maxSeverancePayPerYear = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.MAX_SEVERANCE_PAY_PER_YEAR);

        BigDecimal maxSeverancePay = BigDecimal.valueOf(maxSeverancePayPerYear);

        if (grossSalary.compareTo(maxSeverancePay) <= 0) {
            maxSeverancePay = grossSalary;
        }

        return maxSeverancePay;
    }

    private int calculateNoticePeriodInDays(LocalDate startDate, LocalDate endDate) {

        double noticeThresholdShortMonths = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.NOTICE_THRESHOLD_SHORT_MONTHS);
        double noticeThresholdMediumMonths = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.NOTICE_THRESHOLD_MEDIUM_MONTHS);
        double noticeThresholdLongMonths = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.NOTICE_THRESHOLD_LONG_MONTHS);

        double noticeShortDays = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.NOTICE_SHORT_DAYS);
        double noticeMediumDays = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.NOTICE_MEDIUM_DAYS);
        double noticeLongDays = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.NOTICE_LONG_DAYS);
        double noticeMaxDays = compensationParametersCache
                .getParameterValue(CompensationParameterKeyConstants.NOTICE_MAX_DAYS);

        long totalDays = CompensationUtil.calculateDaysBetweenTwoDates(startDate, endDate);

        if (totalDays <= TOTAL_DAYS_IN_MONTH * noticeThresholdShortMonths) {
            return (int) noticeShortDays;
        } else if (totalDays <= TOTAL_DAYS_IN_MONTH * noticeThresholdMediumMonths) {
            return (int) noticeMediumDays;
        } else if (totalDays <= TOTAL_DAYS_IN_MONTH * noticeThresholdLongMonths) {
            return (int) noticeLongDays;
        } else {
            return (int) noticeMaxDays;
        }

    }

}
