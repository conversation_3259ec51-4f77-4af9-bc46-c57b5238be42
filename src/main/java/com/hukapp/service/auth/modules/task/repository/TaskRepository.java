package com.hukapp.service.auth.modules.task.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.task.entity.Task;

public interface TaskRepository extends JpaRepository<Task, Long> {

    List<Task> findAllByReporter(Person person);
    Optional<Task> findByIdAndReporter(Long id, Person person);
}