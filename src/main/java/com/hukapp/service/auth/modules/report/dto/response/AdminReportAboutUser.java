package com.hukapp.service.auth.modules.report.dto.response;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Set;

import com.hukapp.service.auth.modules.payment.enums.ProductType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminReportAboutUser {

    private Long userId;
    private Instant createdAt;
    private boolean isNewUser;
    private Set<String> roles;
    private ProductType subscriptionLevel;
    private BigDecimal totalPaymentAmount;
    
}
