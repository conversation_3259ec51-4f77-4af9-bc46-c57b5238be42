package com.hukapp.service.auth.modules.person.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

import com.hukapp.service.auth.common.dto.response.BaseResponse;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Person Response DTO")
public class PersonCreateResponse extends BaseResponse {
    private Long id;
    private String name;
    private String surname;
    private String personInfoText;
    private Long identityNumber;
    private LocalDate birthDate;
    private String email;
    private String mobilePhone;
    private Boolean isNewUser;
    private Boolean isDeleted;
}