package com.hukapp.service.auth.modules.report.service;

import java.util.List;

import com.hukapp.service.auth.modules.report.dto.response.AdminReportAboutUser;

/**
 * Service interface for generating admin reports about users
 */
public interface AdminUserReportService {

    /**
     * Generates comprehensive reports for all users in the system
     * 
     * @return List of AdminReportAboutUser containing user information, roles, subscription levels, and payment data
     */
    List<AdminReportAboutUser> generateAllUserReports();
}
