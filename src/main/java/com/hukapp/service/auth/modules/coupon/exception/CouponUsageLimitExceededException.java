package com.hukapp.service.auth.modules.coupon.exception;

/**
 * Exception thrown when trying to use a coupon that has exceeded its usage limit
 */
public class CouponUsageLimitExceededException extends CouponException {

    public CouponUsageLimitExceededException(String message) {
        super(message);
    }

    public CouponUsageLimitExceededException(String message, Throwable cause) {
        super(message, cause);
    }
}
