package com.hukapp.service.auth.modules.file.service.impl;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.file.dto.request.FileUploadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileDownloadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileListDto;
import com.hukapp.service.auth.modules.file.dto.response.FileResponseDto;
import com.hukapp.service.auth.modules.file.entity.TemplateFileContent;
import com.hukapp.service.auth.modules.file.entity.TemplateFileMetadata;
import com.hukapp.service.auth.modules.file.enums.FileType;
import com.hukapp.service.auth.modules.file.exception.FileUploadException;
import com.hukapp.service.auth.modules.file.exception.FileValidationException;
import com.hukapp.service.auth.modules.file.mapper.FileMapper;
import com.hukapp.service.auth.modules.file.repository.FileContentRepository;
import com.hukapp.service.auth.modules.file.repository.FileRepository;
import com.hukapp.service.auth.modules.file.service.FileService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of FileService for file management operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class FileServiceImpl implements FileService {

    private final FileRepository fileRepository;
    private final FileContentRepository fileContentRepository;
    private final FileMapper fileMapper;
    private final PersonService personService;

    // File validation constants
    private static final long MAX_FILE_SIZE = 50L * 1024 * 1024; // 50MB
    private static final String[] ALLOWED_EXTENSIONS = {
        "pdf", "doc", "docx", "txt", "rtf", "xls", "xlsx", "csv", "ppt", "pptx",
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "mp4", "avi", "mov", "wmv",
        "flv", "mp3", "wav", "flac", "aac", "ogg", "zip", "rar", "7z", "tar", "gz"
    };

    private static final String FILE_NOT_FOUND_MESSAGE = "File not found with ID: ";

    @Override
    public FileResponseDto uploadFile(FileUploadDto fileUploadDto, String uploaderEmail) {
        log.info("Starting file upload for user: {}", uploaderEmail);
        
        MultipartFile file = fileUploadDto.getFile();
        validateFileUpload(file);
        
        try {
            // Get uploader
            Person uploader = personService.getPersonByEmailOrElseThrow(uploaderEmail);
            
            // Generate file content hash
            byte[] fileContent = file.getBytes();
            String md5Hash = generateMD5Hash(fileContent);
            
            // Check for duplicate files
            if (fileRepository.existsByMd5Hash(md5Hash)) {
                log.warn("Duplicate file detected with MD5: {}", md5Hash);
                // Optionally return existing file or throw exception
                // For now, we'll allow duplicates but log the warning
            }
            
            // Determine file type
            FileType fileType = FileType.fromFilename(file.getOriginalFilename());
            if (fileType == FileType.OTHER) {
                fileType = FileType.fromMimeType(file.getContentType());
            }
            
            // Generate unique stored filename
            String storedFilename = generateUniqueFilename(file.getOriginalFilename());
            
            // Create file entity
            TemplateFileMetadata fileEntity = TemplateFileMetadata.builder()
                .originalFilename(file.getOriginalFilename())
                .storedFilename(storedFilename)
                .contentType(file.getContentType())
                .fileSize(file.getSize())
                .fileType(fileType)
                .uploader(uploader)
                .description(fileUploadDto.getDescription())
                .tags(fileUploadDto.getTags())
                .isPublic(Boolean.TRUE.equals(fileUploadDto.getIsPublic()))
                .md5Hash(md5Hash)
                .downloadCount(0L)
                .markedForDeletion(false)
                .build();
            
            fileRepository.save(fileEntity);

            // Create file content entity
            TemplateFileContent fileContentEntity = TemplateFileContent.builder()
                .metadata(fileEntity)
                .content(fileContent)
                .build();

            fileContentRepository.save(fileContentEntity);
            
            log.info("File uploaded successfully with ID: {} for user: {}", fileEntity.getId(), uploaderEmail);
            
            return fileMapper.toResponseDto(fileEntity);
            
        } catch (IOException e) {
            log.error("Error reading file content during upload: {}", e.getMessage(), e);
            throw new FileUploadException("Failed to read file content", e);
        } catch (Exception e) {
            log.error("Error during file upload: {}", e.getMessage(), e);
            throw new FileUploadException("File upload failed", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public FileResponseDto getFileById(Long fileId) {
        log.debug("Getting file by ID: {}", fileId);
        
        TemplateFileMetadata fileEntity = fileRepository.findByIdExcludingContent(fileId)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));
        
        return fileMapper.toResponseDto(fileEntity);
    }

    @Override
    public FileDownloadDto downloadFile(Long fileId, String userEmail) {
        log.info("Downloading file ID: {} for user: {}", fileId, userEmail);
        
        TemplateFileMetadata fileEntity = fileRepository.findByIdIncludingContent(fileId)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));
        
        // Update download statistics
        fileEntity.incrementDownloadCount();
        fileRepository.save(fileEntity);
        
        log.info("File downloaded successfully: {} by user: {}", fileEntity.getOriginalFilename(), userEmail);
        
        return fileMapper.toDownloadDto(fileEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public FileDownloadDto getFileMetadata(Long fileId) {
        log.debug("Getting file metadata for ID: {}", fileId);
        
        TemplateFileMetadata fileEntity = fileRepository.findByIdExcludingContent(fileId)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));

        return fileMapper.toDownloadDtoWithoutContent(fileEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FileListDto> listFiles() {
        log.debug("Listing files");
        List<TemplateFileMetadata> fileEntities = fileRepository.findAll();

        return fileEntities.stream()
            .map(fileMapper::toListDto)
            .toList();
    }

    @Override
    public void deleteFile(Long fileId, String userEmail) {
        log.info("Deleting file ID: {} by user: {}", fileId, userEmail);

        if (!fileRepository.existsById(fileId)) {
            throw new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId);
        }
        
        // Hard delete
        TemplateFileMetadata fileEntity = fileRepository.findById(fileId)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));
        TemplateFileContent fileContentEntity = fileContentRepository.findById(fileId)
            .orElseThrow(() -> new ResourceNotFoundException("File content not found for ID: " + fileId));
        
        // Delete content first
        fileContentRepository.delete(fileContentEntity);
        // Then delete metadata
        fileRepository.delete(fileEntity);
        
        log.info("File deleted successfully ID: {} by user: {}", fileId ,userEmail);
    }

    @Override
    public void validateFileUpload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new FileValidationException("File cannot be null or empty");
        }

        // Check file size
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new FileValidationException("File size exceeds maximum allowed size of " +
                (MAX_FILE_SIZE / (1024 * 1024)) + "MB");
        }

        // Check file extension
        String filename = file.getOriginalFilename();
        if (filename == null || filename.trim().isEmpty()) {
            throw new FileValidationException("Filename cannot be null or empty");
        }

        String extension = getFileExtension(filename).toLowerCase();
        boolean isAllowed = false;
        for (String allowedExt : ALLOWED_EXTENSIONS) {
            if (allowedExt.equals(extension)) {
                isAllowed = true;
                break;
            }
        }

        if (!isAllowed) {
            throw new FileValidationException("File type not allowed. Allowed types: " +
                String.join(", ", ALLOWED_EXTENSIONS));
        }

        // Check content type
        String contentType = file.getContentType();
        if (contentType == null || contentType.trim().isEmpty()) {
            throw new FileValidationException("Content type cannot be null or empty");
        }

        log.debug("File validation passed for: {}", filename);
    }

    @Override
    @Transactional(readOnly = true)
    public FileStatistics getFileStatistics() {
        log.debug("Getting file statistics");

        Long totalFiles = fileRepository.countActiveFiles();
        Long totalSize = fileRepository.getTotalFileSize();

        // Get file count by type
        List<Object[]> typeStats = fileRepository.getFileCountByType();
        Map<String, Long> fileCountByType = new HashMap<>();
        for (Object[] stat : typeStats) {
            fileCountByType.put(stat[0].toString(), (Long) stat[1]);
        }

        // Get file count by uploader
        List<Object[]> uploaderStats = fileRepository.getFileCountByUploader();
        Map<String, Long> fileCountByUploader = new HashMap<>();
        for (Object[] stat : uploaderStats) {
            fileCountByUploader.put(stat[0].toString(), (Long) stat[1]);
        }

        return new FileStatistics(totalFiles, totalSize, fileCountByType, fileCountByUploader);
    }

    /**
     * Generates MD5 hash for file content
     */
    private String generateMD5Hash(byte[] content) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(content);

            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5 algorithm not available", e);
            return null;
        }
    }

    /**
     * Generates a unique filename for storage
     */
    private String generateUniqueFilename(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString();

        if (extension.isEmpty()) {
            return uuid;
        } else {
            return uuid + "." + extension;
        }
    }

    /**
     * Extracts file extension from filename
     */
    private String getFileExtension(String filename) {
        if (filename == null) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }
}
