package com.hukapp.service.auth.modules.office.service;

import java.util.List;

import com.hukapp.service.auth.modules.office.dto.TransactionRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionResponse;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;

public interface TransactionService {

    TransactionResponse saveTransaction(TransactionRequest entity, String personEmail);

    List<TransactionResponse> getAllTransactions(String personEmail, TransactionCategory category);

    TransactionResponse updateTransaction(Long id, TransactionRequest entity, String personEmail);

    void deleteTransaction(Long id, String personEmail);

}
