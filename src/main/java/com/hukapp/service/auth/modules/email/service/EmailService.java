package com.hukapp.service.auth.modules.email.service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Service interface for sending emails
 */
public interface EmailService {

    /**
     * Send a simple text email
     *
     * @param to recipient email address
     * @param subject email subject
     * @param text email body text
     */
    void sendSimpleEmail(String to, String subject, String text);

    /**
     * Send an HTML email
     *
     * @param to recipient email address
     * @param subject email subject
     * @param htmlContent email body as HTML
     */
    void sendHtmlEmail(String to, String subject, String htmlContent);

    /**
     * Send an email using a template
     *
     * @param to recipient email address
     * @param subject email subject
     * @param template template name
     * @param templateModel model containing variables for the template
     */
    void sendTemplateEmail(String to, String subject, String template, Map<String, Object> templateModel);

    /**
     * Send an OTP email
     *
     * @param to recipient email address
     * @param otp the one-time password
     */
    void sendOtpEmail(String to, String otp);

    /**
     * Send a login notification email
     *
     * @param toEmail recipient email address
     * @param ipAddress IP address of the login
     * @param userAgent user agent of the login
     */
    void sendLoginNotification(String toEmail, String ipAddress, String userAgent);

    /**
     * Send a login notification email asynchronously
     *
     * @param toEmail recipient email address
     * @param ipAddress IP address of the login
     * @param userAgent user agent of the login
     * @return CompletableFuture<Void> that completes when the email is sent
     */
    CompletableFuture<Void> sendLoginNotificationAsync(String toEmail, String ipAddress, String userAgent);
}
