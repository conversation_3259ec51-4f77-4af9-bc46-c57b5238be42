package com.hukapp.service.auth.modules.file.controller;

import java.util.List;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.file.dto.request.FileUploadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileDownloadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileListDto;
import com.hukapp.service.auth.modules.file.dto.response.FileResponseDto;
import com.hukapp.service.auth.modules.file.service.FileService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Admin controller for file management operations
 * Requires ADMIN role for all operations
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/files")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Admin File Management", description = "Admin endpoints for comprehensive file management operations")
public class AdminFileController {

    private final FileService fileService;

    @Operation(
        summary = "Upload a file",
        description = """
            Upload a file to the database with metadata storage.
            
            **Supported file types:**
            - Documents: pdf, doc, docx, txt, rtf
            - Spreadsheets: xls, xlsx, csv
            - Presentations: ppt, pptx
            - Images: jpg, jpeg, png, gif, bmp, webp
            - Videos: mp4, avi, mov, wmv, flv
            - Audio: mp3, wav, flac, aac, ogg
            - Archives: zip, rar, 7z, tar, gz
            
            **File size limit:** 50MB
            
            **Admin privileges:** This endpoint requires ADMIN role and bypasses subscription checks.
            """
    )
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<FileResponseDto> uploadFile(
            @Parameter(description = "File upload request with file and metadata")
            @ModelAttribute @Valid FileUploadDto fileUploadDto,
            Authentication authentication) {
        
        log.info("Admin file upload request from user: {}", authentication.getName());
        
        FileResponseDto response = fileService.uploadFile(fileUploadDto, authentication.getName());
        
        log.info("File uploaded successfully with ID: {} by admin: {}", 
            response.getId(), authentication.getName());
        
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "List all files")
    @GetMapping
    public ResponseEntity<List<FileListDto>> listFiles(Authentication authentication) {
        
        log.debug("Admin listing files request from user: {}", authentication.getName());

        List<FileListDto> files = fileService.listFiles();

        return ResponseEntity.ok(files);
    }

    @Operation(summary = "Get file details by ID")
    @GetMapping("/{id}")
    public ResponseEntity<FileResponseDto> getFileById(
            @Parameter(description = "File ID", required = true, example = "1")
            @PathVariable Long id,
            Authentication authentication) {

        log.debug("Admin getting file details for ID: {} by user: {}", id, authentication.getName());

        FileResponseDto fileResponse = fileService.getFileById(id);

        return ResponseEntity.ok(fileResponse);
    }

    @Operation(summary = "Download file by ID")
    @GetMapping("/{id}/download")
    public ResponseEntity<byte[]> downloadFile(
            @Parameter(description = "File ID", required = true, example = "1")
            @PathVariable Long id,
            Authentication authentication) {

        log.info("Admin downloading file ID: {} by user: {}", id, authentication.getName());

        FileDownloadDto fileDownload = fileService.downloadFile(id, authentication.getName());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(fileDownload.getContentType()));
        headers.setContentDispositionFormData("attachment", fileDownload.getFilename());
        headers.setContentLength(fileDownload.getFileSize());

        // Add cache control headers
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);

        return ResponseEntity.ok()
            .headers(headers)
            .body(fileDownload.getContent());
    }

    @Operation(summary = "Delete file by ID")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteFile(
            @Parameter(description = "File ID", required = true, example = "1")
            @PathVariable Long id,
            Authentication authentication) {

        log.warn("Admin deleting file ID: {} by user: {}", id, authentication.getName());

        fileService.deleteFile(id, authentication.getName());

        log.info("File deleted successfully by admin: {}", authentication.getName());

        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Get file statistics")
    @GetMapping("/statistics")
    public ResponseEntity<FileService.FileStatistics> getFileStatistics(Authentication authentication) {

        log.debug("Admin requesting file statistics by user: {}", authentication.getName());

        FileService.FileStatistics statistics = fileService.getFileStatistics();

        return ResponseEntity.ok(statistics);
    }
}
