package com.hukapp.service.auth.modules.person.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;

import com.hukapp.service.auth.common.entity.BaseEntity;

@Entity
@Getter
@Setter
public class AvasRole extends BaseEntity {

    @Id
    private Long id;

    @Column(nullable = false, unique = true)
    private String roleName;

}