package com.hukapp.service.auth.modules.task.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.task.dto.TaskRequest;
import com.hukapp.service.auth.modules.task.dto.TaskResponse;
import com.hukapp.service.auth.modules.task.entity.Task;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true),
    imports = {Person.class, Client.class}
)
public interface TaskMapper {
    

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "client", expression = "java(request.getClientId() != null ? Client.builder().id(request.getClientId()).build() : null)")
    Task toEntity(TaskRequest request);

    @Mapping(target = "reporter", source = "reporter.id")
    @Mapping(target = "clientId", source = "client.id")
    TaskResponse toDTO(Task task);

}
