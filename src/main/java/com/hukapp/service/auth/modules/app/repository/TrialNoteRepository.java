package com.hukapp.service.auth.modules.app.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.app.entity.TrialNote;
import com.hukapp.service.auth.modules.person.entity.Person;

@Repository
public interface TrialNoteRepository extends JpaRepository<TrialNote, Long> {
    
    /**
     * Find all trial notes for a specific case number and owner
     * @param caseNumber the case number to search for
     * @param owner the owner of the trial notes
     * @return list of trial notes matching the criteria
     */
    List<TrialNote> findByCaseNumberAndOwner(String caseNumber, Person owner);
    
    /**
     * Find all trial notes for a specific owner
     * @param owner the owner of the trial notes
     * @return list of trial notes belonging to the owner
     */
    List<TrialNote> findByOwner(Person owner);
    
    /**
     * Find a specific trial note by ID and owner (for access control)
     * @param id the ID of the trial note
     * @param owner the owner of the trial note
     * @return optional trial note if found and belongs to the owner
     */
    Optional<TrialNote> findByIdAndOwner(Long id, Person owner);
    
    /**
     * Find a specific trial note by ID, case number and owner (for additional validation)
     * @param id the ID of the trial note
     * @param caseNumber the case number
     * @param owner the owner of the trial note
     * @return optional trial note if found and matches all criteria
     */
    Optional<TrialNote> findByIdAndCaseNumberAndOwner(Long id, String caseNumber, Person owner);
    
    /**
     * Find all trial notes containing a specific text in title or content for a specific owner
     * @param searchTerm the text to search for
     * @param owner the owner of the trial notes
     * @return list of trial notes matching the search criteria
     */
    List<TrialNote> findByOwnerAndNoteTitleContainingIgnoreCaseOrNoteContentContainingIgnoreCase(
            Person owner, String titleSearchTerm, String contentSearchTerm);
}
