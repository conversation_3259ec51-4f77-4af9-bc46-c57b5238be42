package com.hukapp.service.auth.modules.app.dto.response;

import java.time.Instant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response object for case note operations")
public class CaseNoteResponse {
    
    private Long id;
    private String content;
    private String caseNumber;
    private Instant createdAt;
    private Instant updatedAt;
    private long ownerId;
    private String ownerName;
    private String ownerEmail;
}
