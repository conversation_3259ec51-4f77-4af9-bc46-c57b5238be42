package com.hukapp.service.auth.modules.coupon.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.coupon.enums.DiscountType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity representing a coupon that can be applied to payments
 */
@Entity
@Table(name = "coupon", uniqueConstraints = {
    @UniqueConstraint(columnNames = "code", name = "uk_coupon_code")
})
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class Coupon extends BaseEntity {

    /**
     * Unique coupon code/name
     */
    @Column(nullable = false, unique = true, length = 50)
    private String code;

    /**
     * Optional description of the coupon
     */
    @Column(length = 255)
    private String description;

    /**
     * Type of discount (PERCENTAGE or FIXED_AMOUNT)
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DiscountType discountType;

    /**
     * Discount value (percentage or fixed amount)
     */
    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal discountValue;

    /**
     * Maximum number of times this coupon can be used
     */
    @Column(nullable = false)
    private Integer usageLimit;

    /**
     * Current number of times this coupon has been used
     */
    @Column(nullable = false)
    @Builder.Default
    private Integer currentUsageCount = 0;

    /**
     * Optional expiration date
     */
    private LocalDateTime expirationDate;

    /**
     * Whether the coupon is active
     */
    @Column(nullable = false)
    @Builder.Default
    private Boolean active = true;

    /**
     * Version field for optimistic locking to ensure thread-safe updates
     * Note: BaseEntity already has a version field, so we don't need to override it
     */

    /**
     * Check if the coupon is valid for use
     * @return true if coupon is valid, false otherwise
     */
    public boolean isValid() {
        return active && 
               (expirationDate == null || expirationDate.isAfter(LocalDateTime.now())) &&
               currentUsageCount < usageLimit;
    }

    /**
     * Check if the coupon has expired
     * @return true if coupon has expired, false otherwise
     */
    public boolean isExpired() {
        return expirationDate != null && expirationDate.isBefore(LocalDateTime.now());
    }

    /**
     * Check if the coupon usage limit has been exceeded
     * @return true if usage limit exceeded, false otherwise
     */
    public boolean isUsageLimitExceeded() {
        return currentUsageCount >= usageLimit;
    }

    /**
     * Increment the usage count (should be used within a transaction)
     */
    public void incrementUsageCount() {
        this.currentUsageCount++;
    }
}
