package com.hukapp.service.auth.modules.app.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Request object for creating or updating a case note")
public class CaseNoteRequest {

    @Schema(description = "Content of the note", example = "This is a sample case note content.")
    @NotBlank(message = "Not içeriği boş olamaz")
    private String content;
    
    @Schema(description = "Case number to which this note belongs", example = "2023/123")
    @NotBlank(message = "Dosya numarası boş olamaz")
    private String caseNumber;
}
