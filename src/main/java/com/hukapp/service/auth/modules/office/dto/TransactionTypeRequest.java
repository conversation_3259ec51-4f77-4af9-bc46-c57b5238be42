package com.hukapp.service.auth.modules.office.dto;

import com.hukapp.service.auth.modules.office.enums.TransactionCategory;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Transaction Type Create Or Update Request")
public class TransactionTypeRequest {

    @NotNull(message = "İsim gereklidir.")
    @Size(min = 2, message = "İsim en az 2 karakter olmalıdır.")
    @Schema(description = "Transaction type name", example = "Electric Bill")
    private String name;

    @NotNull(message = "Kategori gereklidir.")
    @Schema(description = "Transaction Type category", example = "EXPENSE")
    private TransactionCategory category;

}
