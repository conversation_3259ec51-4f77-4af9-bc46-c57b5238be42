package com.hukapp.service.auth.modules.office.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.office.dto.request.ClientCreateDto;
import com.hukapp.service.auth.modules.office.dto.request.ClientUpdateDto;
import com.hukapp.service.auth.modules.office.dto.response.ClientResponseDto;
import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.office.mapper.ClientMapper;
import com.hukapp.service.auth.modules.office.repository.ClientRepository;
import com.hukapp.service.auth.modules.office.service.ClientService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ClientServiceImpl implements ClientService {

    private static final String CLIENT_NOT_FOUND_MESSAGE = "Müvekkil bulunamadı";

    private final ClientRepository clientRepository;
    private final ClientMapper clientMapper;
    private final PersonService personService;

    @Override
    public ClientResponseDto createClient(ClientCreateDto createDto, String ownerEmail) {
        log.debug("Creating client with name: {} for owner: {}", createDto.getName(), ownerEmail);
        
        // Get the owner (authenticated user)
        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        
        // Convert DTO to entity
        Client client = clientMapper.toEntity(createDto);
        client.setOwner(owner);
        
        // Save the client
        Client savedClient = clientRepository.save(client);
        
        log.debug("Client created successfully with ID: {}", savedClient.getId());
        return clientMapper.toResponseDto(savedClient);
    }

    @Override
    @Transactional(readOnly = true)
    public ClientResponseDto getClientById(Long id, String ownerEmail) {
        log.debug("Getting client with ID: {} for owner: {}", id, ownerEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        
        Client client = clientRepository.findByIdAndOwnerId(id, owner.getId())
                .orElseThrow(() -> new ResourceNotFoundException(CLIENT_NOT_FOUND_MESSAGE));
        
        return clientMapper.toResponseDto(client);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ClientResponseDto> getAllClients(String ownerEmail) {
        log.debug("Getting all clients for owner: {}", ownerEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);

        List<Client> clients = clientRepository.findByOwnerId(owner.getId());
        
        return clientMapper.toResponseDtoList(clients);
    }

    @Override
    public ClientResponseDto updateClient(Long id, ClientUpdateDto updateDto, String ownerEmail) {
        log.debug("Updating client with ID: {} for owner: {}", id, ownerEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        
        Client existingClient = clientRepository.findByIdAndOwnerId(id, owner.getId())
                .orElseThrow(() -> new ResourceNotFoundException(CLIENT_NOT_FOUND_MESSAGE));
        
        // Update the entity with new data
        clientMapper.updateEntityFromDto(updateDto, existingClient);
        
        // Save the updated client
        Client updatedClient = clientRepository.save(existingClient);
        
        log.debug("Client updated successfully with ID: {}", updatedClient.getId());
        return clientMapper.toResponseDto(updatedClient);
    }

    @Override
    public void deleteClient(Long id, String ownerEmail) {
        log.debug("Deleting client with ID: {} for owner: {}", id, ownerEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        
        Client client = clientRepository.findByIdAndOwnerId(id, owner.getId())
                .orElseThrow(() -> new ResourceNotFoundException(CLIENT_NOT_FOUND_MESSAGE));
        
        // delete
        clientRepository.delete(client);
        
        log.debug("Client deleted successfully with ID: {}", id);
    }

}
