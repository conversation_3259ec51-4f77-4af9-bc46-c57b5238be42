package com.hukapp.service.auth.modules.app.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.app.entity.FrequentCaseNumber;
import com.hukapp.service.auth.modules.person.entity.Person;

@Repository
public interface FrequentCaseNumberRepository extends JpaRepository<FrequentCaseNumber, Long> {
    
    /**
     * Find all frequent case numbers for a specific owner
     * 
     * @param owner the owner of the case numbers
     * @return list of frequent case numbers
     */
    List<FrequentCaseNumber> findByOwnerOrderByLastUsedAtDesc(Person owner);
    
    /**
     * Find a specific frequent case number by ID and owner
     * 
     * @param id the ID of the frequent case number
     * @param owner the owner of the case number
     * @return optional containing the frequent case number if found
     */
    Optional<FrequentCaseNumber> findByIdAndOwner(Long id, Person owner);
    
    /**
     * Find a specific frequent case number by case number and owner
     * 
     * @param caseNumber the case number
     * @param owner the owner of the case number
     * @return optional containing the frequent case number if found
     */
    Optional<FrequentCaseNumber> findByCaseNumberAndOwner(String caseNumber, Person owner);
    
    /**
     * Check if a case number already exists for an owner
     * 
     * @param caseNumber the case number to check
     * @param owner the owner of the case number
     * @return true if the case number exists for the owner, false otherwise
     */
    boolean existsByCaseNumberAndOwner(String caseNumber, Person owner);
}
