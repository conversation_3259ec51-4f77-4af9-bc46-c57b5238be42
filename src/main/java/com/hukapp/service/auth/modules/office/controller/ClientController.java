package com.hukapp.service.auth.modules.office.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.office.dto.request.ClientCreateDto;
import com.hukapp.service.auth.modules.office.dto.request.ClientUpdateDto;
import com.hukapp.service.auth.modules.office.dto.response.ClientResponseDto;
import com.hukapp.service.auth.modules.office.service.ClientService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/clients")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Validated
@Tag(name = "Client Management", description = "CRUD operations for managing clients")
public class ClientController {

    private final ClientService clientService;

    @PostMapping
    @Operation(
        summary = "Create new client",
        description = """
            Creates a new client for the authenticated user.
            
            Available Client Types:
            - INDIVIDUAL: Bireysel müvekkil
            - CORPORATE: Kurumsal müvekkil  
            - OTHER: Diğer
            
            The client name must be unique for each user.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Client created successfully",
            content = @Content(schema = @Schema(implementation = ClientResponseDto.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input data or client name already exists"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "409",
            description = "Client name already exists"
        )
    })
    public ResponseEntity<ClientResponseDto> createClient(
            @Parameter(description = "Client creation data", required = true)
            @Valid @RequestBody ClientCreateDto createDto,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Creating client with name: {}", createDto.getName());
        ClientResponseDto response = clientService.createClient(createDto, authentication.getName());
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get client by ID",
        description = "Retrieves a specific client by ID for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Client retrieved successfully",
            content = @Content(schema = @Schema(implementation = ClientResponseDto.class))
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Client not found"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        )
    })
    public ResponseEntity<ClientResponseDto> getClientById(
            @Parameter(description = "Client ID", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Getting client with ID: {}", id);
        ClientResponseDto response = clientService.getClientById(id, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(
        summary = "Get all clients",
        description = "Retrieves all clients for the authenticated user. Results are sorted by creation date."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Clients retrieved successfully",
            content = @Content(schema = @Schema(implementation = ClientResponseDto.class))
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        )
    })
    public ResponseEntity<List<ClientResponseDto>> getAllClients(
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Getting all clients for user: {}", authentication.getName());
        List<ClientResponseDto> response = clientService.getAllClients(authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update client",
        description = "Updates an existing client for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Client updated successfully",
            content = @Content(schema = @Schema(implementation = ClientResponseDto.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input data or client name already exists"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Client not found"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "409",
            description = "Client name already exists"
        )
    })
    public ResponseEntity<ClientResponseDto> updateClient(
            @Parameter(description = "Client ID", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(description = "Client update data", required = true)
            @Valid @RequestBody ClientUpdateDto updateDto,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Updating client with ID: {}", id);
        ClientResponseDto response = clientService.updateClient(id, updateDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete client",
        description = "Deletes (soft delete) an existing client for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "204",
            description = "Client deleted successfully"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Client not found"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        )
    })
    public ResponseEntity<Void> deleteClient(
            @Parameter(description = "Client ID", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Deleting client with ID: {}", id);
        clientService.deleteClient(id, authentication.getName());
        return ResponseEntity.noContent().build();
    }

}
