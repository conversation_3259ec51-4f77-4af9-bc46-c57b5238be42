package com.hukapp.service.auth.modules.app.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.app.entity.CaseDetails;
import com.hukapp.service.auth.modules.person.entity.Person;

@Repository
public interface CaseDetailsRepository extends JpaRepository<CaseDetails, Long> {
    
    List<CaseDetails> findByOwner(Person owner);
    
    Optional<CaseDetails> findByIdAndOwner(Long id, Person owner);
    
    Optional<CaseDetails> findByCaseNumberAndOwner(String caseNumber, Person owner);
    
    boolean existsByCaseNumberAndOwner(String caseNumber, Person owner);
}
