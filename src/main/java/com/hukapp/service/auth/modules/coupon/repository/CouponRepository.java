package com.hukapp.service.auth.modules.coupon.repository;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.coupon.entity.Coupon;

import jakarta.persistence.LockModeType;

/**
 * Repository interface for Coupon entity operations
 */
@Repository
public interface CouponRepository extends JpaRepository<Coupon, Long> {

    /**
     * Find a coupon by its code
     * @param code the coupon code
     * @return Optional containing the coupon if found
     */
    Optional<Coupon> findByCode(String code);

    /**
     * Find a coupon by its code with pessimistic lock for thread-safe updates
     * @param code the coupon code
     * @return Optional containing the coupon if found
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT c FROM Coupon c WHERE c.code = :code")
    Optional<Coupon> findByCodeWithLock(@Param("code") String code);

    /**
     * Check if a coupon with the given code exists
     * @param code the coupon code
     * @return true if exists, false otherwise
     */
    boolean existsByCode(String code);

    /**
     * Find all active coupons with pagination
     * @param active the active status
     * @param pageable pagination information
     * @return Page of coupons
     */
    Page<Coupon> findByActive(Boolean active, Pageable pageable);

    /**
     * Find all coupons with pagination
     * @param pageable pagination information
     * @return Page of coupons
     */
    @NonNull Page<Coupon> findAll(@NonNull Pageable pageable);

    /**
     * Find coupons by code containing (case insensitive) with pagination
     * @param code partial code to search
     * @param pageable pagination information
     * @return Page of coupons
     */
    Page<Coupon> findByCodeContainingIgnoreCase(String code, Pageable pageable);
}
