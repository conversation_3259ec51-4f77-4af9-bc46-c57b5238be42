package com.hukapp.service.auth.modules.audit.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration properties for audit logging system
 */
@Data
@Component
@ConfigurationProperties(prefix = "audit.logging")
public class AuditLoggingProperties {

    /**
     * Enable or disable audit logging globally
     */
    private boolean enabled = true;

    /**
     * List of endpoint patterns to exclude from audit logging
     * Supports ant-style patterns (e.g., /api/admin/**, /auth/**)
     */
    private List<String> excludedEndpoints = new ArrayList<>(List.of(
            "/api/admin/**",
            "/auth/**",
            "/actuator/**",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/favicon.ico"
    ));

    /**
     * Enable logging of request headers (excluding sensitive ones)
     */
    private boolean logRequestHeaders = true;

    /**
     * Enable calculation and logging of request/response body sizes
     */
    private boolean logBodySizes = true;

    /**
     * Maximum number of days to retain audit logs
     */
    private int retentionDays = 90;

    /**
     * Enable automatic cleanup of old audit logs
     */
    private boolean autoCleanupEnabled = true;

    /**
     * Cron expression for automatic cleanup job
     * Default: Run daily at 2 AM
     */
    private String cleanupCronExpression = "0 0 2 * * ?";

    /**
     * Threshold in milliseconds to consider a request as "slow"
     */
    private long slowRequestThresholdMs = 1000;

    /**
     * Enable detailed error logging
     */
    private boolean logErrorDetails = true;

    /**
     * Maximum length for error messages to store
     */
    private int maxErrorMessageLength = 1000;

    /**
     * Enable session ID logging
     */
    private boolean logSessionId = true;

    /**
     * List of HTTP methods to exclude from logging
     */
    private List<String> excludedHttpMethods = new ArrayList<>();

    /**
     * Enable logging for anonymous/unauthenticated requests
     */
    private boolean logAnonymousRequests = false;

    /**
     * Maximum number of audit logs to return in admin queries
     */
    private int maxResultsLimit = 1000;
}
