package com.hukapp.service.auth.modules.report.dto.response;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.hukapp.service.auth.modules.task.entity.Task.Priority;
import com.hukapp.service.auth.modules.task.entity.Task.Status;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
@Schema(description = "Comprehensive user report containing personal information, financial data, and task statistics")
public class UserReportResponse {

    // Basic user information
    @Schema(description = "User ID")
    private Long id;

    @Schema(description = "User's first name")
    private String name;

    @Schema(description = "User's last name")
    private String surname;

    @Schema(description = "User's full name (first + last name)")
    private String fullName;

    @Schema(description = "User's email address")
    private String email;

    @Schema(description = "User's mobile phone number")
    private String mobilePhone;

    @Schema(description = "User's identity number")
    private Long identityNumber;

    @Schema(description = "User's birth date")
    private LocalDate birthDate;

    @Schema(description = "Whether the user's email is verified")
    private Boolean isEmailVerified;

    @Schema(description = "Whether the user's mobile phone is verified")
    private Boolean isMobilePhoneVerified;

    @Schema(description = "Whether the user is new to the system")
    private Boolean isNewUser;

    // UYAP user details
    @Schema(description = "User details from UYAP system, may include photo if requested")
    private UserDetailsSection uyapDetails;

    // Financial information
    @Schema(description = "Summary of user's financial information including income, expenses, and net income")
    private FinancialSummarySection financialSummary;

    // Task information
    @Schema(description = "Summary of user's tasks grouped by status and priority")
    private TaskSummarySection taskSummary;

    // Report metadata
    @Schema(description = "Timestamp when the report was generated")
    private LocalDateTime reportGeneratedAt;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    @Schema(description = "User details from external systems")
    public static class UserDetailsSection {
        @Schema(description = "Map of user details from UYAP system")
        private Map<String, Object> details;

        @Schema(description = "User's photo data, included only if explicitly requested")
        private String photoData;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    @Schema(description = "Summary of user's financial information")
    public static class FinancialSummarySection {
        @Schema(description = "Total income amount")
        private BigDecimal totalIncome;

        @Schema(description = "Total expenses amount")
        private BigDecimal totalExpenses;

        @Schema(description = "Net income (income - expenses)")
        private BigDecimal netIncome;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    @Schema(description = "Summary of user's tasks with counts by status and priority")
    public static class TaskSummarySection {
        @Schema(description = "Total number of tasks")
        private int totalTasks;

        @Schema(description = "Count of tasks grouped by status (OPEN, IN_PROGRESS, COMPLETED, CANCELLED)")
        private Map<Status, Integer> taskCountsByStatus;

        @Schema(description = "Count of tasks grouped by priority (LOW, MEDIUM, HIGH, CRITICAL)")
        private Map<Priority, Integer> taskCountsByPriority;

        @Schema(description = "Count of tasks grouped first by status and then by priority within each status")
        private Map<Status, Map<Priority, Integer>> taskCountsByStatusAndPriority;
    }
}
