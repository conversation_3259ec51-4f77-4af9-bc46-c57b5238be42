package com.hukapp.service.auth.modules.email.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Configuration properties for email service.
 * Supports various SMTP server configurations.
 */
@ConfigurationProperties(prefix = "email")
public record EmailProperties(
    // Basic SMTP settings
    String host,
    Integer port,
    String username,
    String password,
    String from,

    // Security settings
    Boolean auth,
    Boolean startTls,
    Boolean ssl,

    // Connection settings
    Integer connectionTimeout,
    Integer timeout,
    Integer writeTimeout,

    // Debug settings
    Boolean debug
) {
    /**
     * Constructor with default values for optional properties
     */
    public EmailProperties {
        // Set defaults for null values
        if (auth == null) auth = true;
        if (startTls == null) startTls = false;
        if (ssl == null) ssl = false;
        if (connectionTimeout == null) connectionTimeout = 5000;
        if (timeout == null) timeout = 5000;
        if (writeTimeout == null) writeTimeout = 5000;
        if (debug == null) debug = false;
    }
}
