package com.hukapp.service.auth.modules.audit.service.impl;

import com.hukapp.service.auth.modules.audit.dto.request.AuditLogCreateRequest;
import com.hukapp.service.auth.modules.audit.dto.response.AuditLogResponse;
import com.hukapp.service.auth.modules.audit.dto.response.AuditLogStatisticsResponse;
import com.hukapp.service.auth.modules.audit.entity.AuditLog;
import com.hukapp.service.auth.modules.audit.mapper.AuditLogMapper;
import com.hukapp.service.auth.modules.audit.repository.AuditLogRepository;
import com.hukapp.service.auth.modules.audit.service.AuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Implementation of AuditLogService with async processing for zero-impact logging
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class AuditLogServiceImpl implements AuditLogService {

    private final AuditLogRepository auditLogRepository;
    private final AuditLogMapper auditLogMapper;

    @Override
    @Async("taskExecutor")
    @Transactional
    public CompletableFuture<Void> saveAuditLogAsync(AuditLogCreateRequest request) {
        try {
            log.debug("Saving audit log asynchronously for user: {} endpoint: {}", 
                     request.getUserEmail(), request.getEndpointUrl());
            
            AuditLog auditLog = auditLogMapper.toEntity(request);
            auditLogRepository.save(auditLog);
            
            log.debug("Audit log saved successfully for user: {} endpoint: {}", 
                     request.getUserEmail(), request.getEndpointUrl());
            
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("Failed to save audit log asynchronously for user: {} endpoint: {} - Error: {}", 
                     request.getUserEmail(), request.getEndpointUrl(), e.getMessage(), e);
            
            // Don't rethrow the exception to avoid affecting main API operations
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    @Override
    public List<AuditLogResponse> getAuditLogsByUser(String userEmail) {
        log.debug("Retrieving audit logs for user: {}", userEmail);
        List<AuditLog> auditLogs = auditLogRepository.findByUserEmailOrderByRequestTimestampDesc(userEmail);
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public List<AuditLogResponse> getAuditLogsByUserAndDateRange(String userEmail, Instant startDate, Instant endDate) {
        log.debug("Retrieving audit logs for user: {} between {} and {}", userEmail, startDate, endDate);
        List<AuditLog> auditLogs = auditLogRepository.findByUserEmailAndDateRange(userEmail, startDate, endDate);
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public List<AuditLogResponse> getAuditLogsByEndpoint(String endpointUrl) {
        log.debug("Retrieving audit logs for endpoint: {}", endpointUrl);
        List<AuditLog> auditLogs = auditLogRepository.findByEndpointUrlOrderByRequestTimestampDesc(endpointUrl);
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public List<AuditLogResponse> getFailedRequests() {
        log.debug("Retrieving failed requests");
        List<AuditLog> auditLogs = auditLogRepository.findFailedRequests();
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public List<AuditLogResponse> getSlowRequests(Long thresholdMs) {
        log.debug("Retrieving slow requests with threshold: {}ms", thresholdMs);
        List<AuditLog> auditLogs = auditLogRepository.findSlowRequests(thresholdMs);
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public AuditLogStatisticsResponse getAuditStatistics(Instant startDate, Instant endDate) {
        log.debug("Retrieving audit statistics between {} and {}", startDate, endDate);

        try {
            // Get statistics using separate queries to avoid casting issues
            Long totalRequests = auditLogRepository.getTotalRequestCount(startDate, endDate);
            Long uniqueUsers = auditLogRepository.getUniqueUserCount(startDate, endDate);
            Double avgProcessingTime = auditLogRepository.getAverageProcessingTime(startDate, endDate);
            Long errorCount = auditLogRepository.getErrorCount(startDate, endDate);

            List<Object[]> endpointStats = auditLogRepository.getMostAccessedEndpoints(startDate, endDate);
            List<Object[]> userStats = auditLogRepository.getMostActiveUsers(startDate, endDate);

            // Safely handle null values
            long totalRequestsValue = totalRequests != null ? totalRequests : 0L;
            long uniqueUsersValue = uniqueUsers != null ? uniqueUsers : 0L;
            double avgProcessingTimeValue = avgProcessingTime != null ? avgProcessingTime : 0.0;
            long errorCountValue = errorCount != null ? errorCount : 0L;

            return AuditLogStatisticsResponse.builder()
                    .totalRequests(totalRequestsValue)
                    .uniqueUsers(uniqueUsersValue)
                    .avgProcessingTime(avgProcessingTimeValue)
                    .errorCount(errorCountValue)
                    .mostAccessedEndpoints(endpointStats != null ? endpointStats.stream()
                            .map(row -> AuditLogStatisticsResponse.EndpointStatistic.builder()
                                    .endpointUrl((String) row[0])
                                    .accessCount(((Number) row[1]).longValue())
                                    .build())
                            .toList() : List.of())
                    .mostActiveUsers(userStats != null ? userStats.stream()
                            .map(row -> AuditLogStatisticsResponse.UserStatistic.builder()
                                    .userEmail((String) row[0])
                                    .requestCount(((Number) row[1]).longValue())
                                    .build())
                            .toList() : List.of())
                    .build();
        } catch (Exception e) {
            log.error("Error retrieving audit statistics: {}", e.getMessage(), e);
            // Return empty statistics in case of error
            return AuditLogStatisticsResponse.builder()
                    .totalRequests(0L)
                    .uniqueUsers(0L)
                    .avgProcessingTime(0.0)
                    .errorCount(0L)
                    .mostAccessedEndpoints(List.of())
                    .mostActiveUsers(List.of())
                    .build();
        }
    }

    @Override
    public List<AuditLogResponse> getRecentAuditLogs() {
        log.debug("Retrieving recent audit logs");
        List<AuditLog> auditLogs = auditLogRepository.findRecentAuditLogs();
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public List<AuditLogResponse> getAuditLogsWithErrors() {
        log.debug("Retrieving audit logs with errors");
        List<AuditLog> auditLogs = auditLogRepository.findAuditLogsWithErrors();
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public List<AuditLogResponse> getAuditLogsByClientIp(String clientIpAddress) {
        log.debug("Retrieving audit logs for client IP: {}", clientIpAddress);
        List<AuditLog> auditLogs = auditLogRepository.findByClientIpAddressOrderByRequestTimestampDesc(clientIpAddress);
        return auditLogMapper.toResponseList(auditLogs);
    }

    @Override
    public long countAuditLogsByUser(String userEmail) {
        log.debug("Counting audit logs for user: {}", userEmail);
        return auditLogRepository.countByUserEmail(userEmail);
    }

    @Override
    @Transactional
    public void cleanupOldAuditLogs(Instant cutoffDate) {
        log.info("Cleaning up audit logs older than: {}", cutoffDate);
        try {
            auditLogRepository.deleteOldAuditLogs(cutoffDate);
            log.info("Successfully cleaned up old audit logs");
        } catch (Exception e) {
            log.error("Failed to cleanup old audit logs: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<AuditLogResponse> getAuditLogsByDateRange(Instant startDate, Instant endDate) {
        
        log.debug("Retrieving audit logs between {} and {}", startDate, endDate);
        List<AuditLog> auditLogs = auditLogRepository.findAuditLogsByDateRange(startDate, endDate);
        return auditLogMapper.toResponseList(auditLogs);
    }
}
