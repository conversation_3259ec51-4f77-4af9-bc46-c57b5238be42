package com.hukapp.service.auth.modules.file.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.file.entity.TemplateFileMetadata;

/**
 * Repository interface for TemplateFileMetadata with custom queries for file management operations
 */
@Repository
public interface FileRepository extends JpaRepository<TemplateFileMetadata, Long>, JpaSpecificationExecutor<TemplateFileMetadata> {

    /**
     * Find file by ID excluding file content for performance
     */
    @Query("SELECT f FROM TemplateFileMetadata f WHERE f.id = :id AND f.isDeleted = false AND f.markedForDeletion = false")
    Optional<TemplateFileMetadata> findByIdExcludingContent(@Param("id") Long id);

    /**
     * Find file by ID including file content for download
     */
    @Query("SELECT f FROM TemplateFileMetadata f WHERE f.id = :id AND f.isDeleted = false AND f.markedForDeletion = false")
    Optional<TemplateFileMetadata> findByIdIncludingContent(@Param("id") Long id);

    /**
     * Find all active files with sorting (not deleted and not marked for deletion)
     */
    @Query("SELECT f FROM TemplateFileMetadata f WHERE f.isDeleted = false AND f.markedForDeletion = false")
    List<TemplateFileMetadata> findAllActive();

    /**
     * Check if file exists by MD5 hash
     */
    boolean existsByMd5Hash(String md5Hash);

    /**
     * Check if file exists by id
     */
    boolean existsById(@NonNull Long id);

    /**
     * Get total file count for statistics
     */
    @Query("SELECT COUNT(f) FROM TemplateFileMetadata f WHERE f.isDeleted = false AND f.markedForDeletion = false")
    Long countActiveFiles();

    /**
     * Get total file size for statistics
     */
    @Query("SELECT COALESCE(SUM(f.fileSize), 0) FROM TemplateFileMetadata f WHERE f.isDeleted = false AND f.markedForDeletion = false")
    Long getTotalFileSize();

    /**
     * Get file count by type for statistics
     */
    @Query("SELECT f.fileType, COUNT(f) FROM TemplateFileMetadata f WHERE f.isDeleted = false AND f.markedForDeletion = false GROUP BY f.fileType")
    List<Object[]> getFileCountByType();

    /**
     * Get file count by uploader for statistics
     */
    @Query("SELECT f.uploader.email, COUNT(f) FROM TemplateFileMetadata f WHERE f.isDeleted = false AND f.markedForDeletion = false GROUP BY f.uploader.email")
    List<Object[]> getFileCountByUploader();

    /**
     * Delete file by ID (hard delete)
     */
    @Modifying
    @Query("DELETE FROM TemplateFileMetadata f WHERE f.id = :id")
    void deleteById(@NonNull @Param("id") Long id);
}
