package com.hukapp.service.auth.modules.reminder.service;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.reminder.dto.ReminderRequest;
import com.hukapp.service.auth.modules.reminder.dto.ReminderResponse;
import com.hukapp.service.auth.modules.reminder.entity.Reminder;
import com.hukapp.service.auth.modules.reminder.mapper.ReminderMapper;
import com.hukapp.service.auth.modules.reminder.repository.ReminderRepository;

import lombok.RequiredArgsConstructor;

import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ReminderService {

    private final PersonService personService;

    private final ReminderRepository reminderRepository;

    private final ReminderMapper reminderMapper;

    public List<ReminderResponse> getAllReminders(Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        return reminderRepository.findAllByOwnerId(person.getId()).stream()
                .map(reminderMapper::toResponseDTO)
                .toList();
    }

    public ReminderResponse getReminderById(Long id, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        return reminderRepository.findByIdAndOwnerId(id, person.getId())
                .map(reminderMapper::toResponseDTO)
                .orElseThrow(() -> new ResourceNotFoundException("Reminder not found"));
    }

    public ReminderResponse createReminder(ReminderRequest requestDTO, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        Reminder reminder = reminderMapper.toEntity(requestDTO);
        reminder.setOwner(person);
        Reminder savedReminder = reminderRepository.save(reminder);
        return reminderMapper.toResponseDTO(savedReminder);
    }

    public ReminderResponse updateReminder(Long id, ReminderRequest requestDTO, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        Reminder reminder = reminderRepository.findByIdAndOwnerId(id, person.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Reminder not found"));
        reminderMapper.updateEntityFromDTO(requestDTO, reminder);
        Reminder updatedReminder = reminderRepository.save(reminder);
        return reminderMapper.toResponseDTO(updatedReminder);
    }

    public void deleteReminder(Long id, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        reminderRepository.findByIdAndOwnerId(id, person.getId())
                .ifPresentOrElse(reminderRepository::delete,
                        () -> {
                            throw new ResourceNotFoundException("Reminder not found");
                        });
    }
}