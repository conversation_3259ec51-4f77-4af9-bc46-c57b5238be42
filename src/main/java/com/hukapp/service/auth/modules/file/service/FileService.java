package com.hukapp.service.auth.modules.file.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.hukapp.service.auth.modules.file.dto.request.FileUploadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileDownloadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileListDto;
import com.hukapp.service.auth.modules.file.dto.response.FileResponseDto;

/**
 * Service interface for file management operations
 */
public interface FileService {

    /**
     * Upload a file to the database
     * 
     * @param fileUploadDto the file upload request
     * @param uploaderEmail the email of the user uploading the file
     * @return the uploaded file response
     */
    FileResponseDto uploadFile(FileUploadDto fileUploadDto, String uploaderEmail);

    /**
     * Get file details by ID
     * 
     * @param fileId the file ID
     * @return the file response
     */
    FileResponseDto getFileById(Long fileId);

    /**
     * Download file by ID
     * 
     * @param fileId the file ID
     * @param userEmail the email of the user requesting the download
     * @return the file download response
     */
    FileDownloadDto downloadFile(Long fileId, String userEmail);

    /**
     * Get file metadata for download (without content)
     * 
     * @param fileId the file ID
     * @return the file download metadata
     */
    FileDownloadDto getFileMetadata(Long fileId);

    /**
     * List all files with filtering
     *
     * @param filterDto the filter parameters
     * @return list of all files
     */
    List<FileListDto> listFiles();

    /**
     * Delete a file by ID (soft delete)
     * 
     * @param fileId the file ID
     * @param userEmail the email of the user requesting deletion
     */
    void deleteFile(Long fileId, String userEmail);

    /**
     * Validate file upload
     * 
     * @param file the multipart file to validate
     * @throws IllegalArgumentException if validation fails
     */
    void validateFileUpload(MultipartFile file);

    /**
     * Get file statistics
     * 
     * @return file statistics object
     */
    FileStatistics getFileStatistics();

    /**
     * Inner class for file statistics
     */
    class FileStatistics {
        private Long totalFiles;
        private Long totalSize;
        private java.util.Map<String, Long> fileCountByType;
        private java.util.Map<String, Long> fileCountByUploader;

        // Constructors
        public FileStatistics() {}

        public FileStatistics(Long totalFiles, Long totalSize, 
                            java.util.Map<String, Long> fileCountByType,
                            java.util.Map<String, Long> fileCountByUploader) {
            this.totalFiles = totalFiles;
            this.totalSize = totalSize;
            this.fileCountByType = fileCountByType;
            this.fileCountByUploader = fileCountByUploader;
        }

        // Getters and setters
        public Long getTotalFiles() { return totalFiles; }
        public void setTotalFiles(Long totalFiles) { this.totalFiles = totalFiles; }

        public Long getTotalSize() { return totalSize; }
        public void setTotalSize(Long totalSize) { this.totalSize = totalSize; }

        public java.util.Map<String, Long> getFileCountByType() { return fileCountByType; }
        public void setFileCountByType(java.util.Map<String, Long> fileCountByType) { this.fileCountByType = fileCountByType; }

        public java.util.Map<String, Long> getFileCountByUploader() { return fileCountByUploader; }
        public void setFileCountByUploader(java.util.Map<String, Long> fileCountByUploader) { this.fileCountByUploader = fileCountByUploader; }
    }
}
