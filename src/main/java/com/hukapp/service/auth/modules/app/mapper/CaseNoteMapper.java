package com.hukapp.service.auth.modules.app.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.app.dto.request.CaseNoteRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseNoteResponse;
import com.hukapp.service.auth.modules.app.entity.CaseNote;
import com.hukapp.service.auth.modules.person.entity.Person;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true),
    imports = {Person.class}
)
public interface CaseNoteMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    CaseNote toEntity(CaseNoteRequest caseNoteRequest);
    
    @Mapping(target = "ownerName", expression = "java(caseNote.getOwner().getName() + ' ' + caseNote.getOwner().getSurname())")
    @Mapping(target = "ownerEmail", expression = "java(caseNote.getOwner().getEmail())")
    @Mapping(target = "ownerId", expression = "java(caseNote.getOwner().getId())")
    CaseNoteResponse toDTO(CaseNote caseNote);
}
