package com.hukapp.service.auth.modules.reminder.dto;

import java.time.Instant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response for creating or updating a reminder")
public class ReminderResponse {

    private Long id;

    private String title;

    private String description;

    private Instant dueDate;

    private int repeatIntervalInHours;

    private String priority;

    private long reporterId;

}