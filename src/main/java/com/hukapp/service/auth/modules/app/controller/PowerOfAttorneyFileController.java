package com.hukapp.service.auth.modules.app.controller;

import com.hukapp.service.auth.modules.app.dto.request.PoaFileUploadDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileDownloadDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileListDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileResponseDto;
import com.hukapp.service.auth.modules.app.service.PowerOfAttorneyFileService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for Power of Attorney file management operations
 * Subject to subscription-based access control (HTTP 402 for unauthorized users)
 */
@Slf4j
@RestController
@RequestMapping("/api/user/poa")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Power of Attorney File Management", description = "User endpoints for Power of Attorney file operations")
public class PowerOfAttorneyFileController {

    private final PowerOfAttorneyFileService poaFileService;

    @Operation(
        summary = "Upload a file for a Power of Attorney",
        description = """
            Upload a file to be associated with a specific Power of Attorney.
            
            **Supported file types:**
            - Documents: PDF, DOC, DOCX
            - Images: JPG, JPEG, PNG
            
            **File size limit:** 10MB
            
            **Authorization:** User can only upload files to their own Power of Attorney records.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File uploaded successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PoaFileResponseDto.class))),
        @ApiResponse(responseCode = "400", description = "Invalid file or validation error"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed"),
        @ApiResponse(responseCode = "404", description = "Power of Attorney not found"),
        @ApiResponse(responseCode = "413", description = "File too large"),
        @ApiResponse(responseCode = "415", description = "Unsupported file type")
    })
    @PostMapping(value = "/files/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<PoaFileResponseDto> uploadFile(
            @Parameter(description = "File upload request with Power of Attorney ID, file and metadata")
            @ModelAttribute @Valid PoaFileUploadDto poaFileUploadDto,
            Authentication authentication) {
        
        log.info("Power of Attorney file upload request for POA ID: {} from user: {}", 
            poaFileUploadDto.getPowerOfAttorneyId(), authentication.getName());
        
        PoaFileResponseDto response = poaFileService.uploadFile(poaFileUploadDto, authentication.getName());
        
        log.info("Power of Attorney file uploaded successfully with ID: {} for POA ID: {} by user: {}", 
            response.getId(), poaFileUploadDto.getPowerOfAttorneyId(), authentication.getName());
        
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Download a Power of Attorney file by ID",
        description = """
            Download a Power of Attorney file by its ID. The file content is returned as a byte array
            with appropriate content-type headers for browser download.
            
            **Authorization:** User can only download their own Power of Attorney files.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File downloaded successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @GetMapping("/files/{id}/download")
    public ResponseEntity<byte[]> downloadFile(
            @Parameter(description = "File ID", required = true, example = "1")
            @PathVariable Long id,
            Authentication authentication) {

        log.info("Power of Attorney file download request for ID: {} from user: {}", id, authentication.getName());

        PoaFileDownloadDto downloadDto = poaFileService.downloadFile(id, authentication.getName());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(downloadDto.getContentType()));
        headers.setContentDispositionFormData("attachment", downloadDto.getFilename());
        headers.setContentLength(downloadDto.getFileSize());
        
        // Add custom headers for additional metadata
        headers.add("X-POA-ID", downloadDto.getPowerOfAttorneyId().toString());
        headers.add("X-POA-Number", downloadDto.getPowerOfAttorneyNumber());
        headers.add("X-File-MD5", downloadDto.getMd5Hash());

        log.info("Power of Attorney file downloaded successfully: {} by user: {}", 
            downloadDto.getFilename(), authentication.getName());

        return ResponseEntity.ok()
            .headers(headers)
            .body(downloadDto.getContent());
    }

    @Operation(
        summary = "List files for a specific Power of Attorney",
        description = """
            Get a list of all files associated with a specific Power of Attorney.
            
            **Authorization:** User can only list files for their own Power of Attorney records.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Files listed successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PoaFileListDto.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed"),
        @ApiResponse(responseCode = "404", description = "Power of Attorney not found")
    })
    @GetMapping("/{poaId}/files")
    public ResponseEntity<List<PoaFileListDto>> listFilesByPowerOfAttorney(
            @Parameter(description = "Power of Attorney ID", required = true, example = "1")
            @PathVariable Long poaId,
            Authentication authentication) {

        log.info("Power of Attorney files list request for POA ID: {} from user: {}", poaId, authentication.getName());

        List<PoaFileListDto> files = poaFileService.listFilesByPowerOfAttorney(poaId, authentication.getName());

        log.info("Found {} Power of Attorney files for POA ID: {} for user: {}", 
            files.size(), poaId, authentication.getName());

        return ResponseEntity.ok(files);
    }

    @Operation(
        summary = "List all Power of Attorney files for the authenticated user",
        description = """
            Get a list of all Power of Attorney files uploaded by the authenticated user
            across all their Power of Attorney records.
            
            **Authorization:** User can only see their own files.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Files listed successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PoaFileListDto.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed")
    })
    @GetMapping("/files")
    public ResponseEntity<List<PoaFileListDto>> listUserFiles(Authentication authentication) {

        log.info("All Power of Attorney files list request from user: {}", authentication.getName());

        List<PoaFileListDto> files = poaFileService.listUserFiles(authentication.getName());

        log.info("Found {} total Power of Attorney files for user: {}", files.size(), authentication.getName());

        return ResponseEntity.ok(files);
    }

    @Operation(
        summary = "Delete a Power of Attorney file by ID",
        description = """
            Delete a Power of Attorney file by its ID. This is a hard delete operation
            that permanently removes the file and its content from the database.

            **Authorization:** User can only delete their own Power of Attorney files.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "File deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @DeleteMapping("/files/{id}")
    public ResponseEntity<Void> deleteFile(
            @Parameter(description = "File ID", required = true, example = "1")
            @PathVariable Long id,
            Authentication authentication) {

        log.info("Power of Attorney file deletion request for ID: {} from user: {}", id, authentication.getName());

        poaFileService.deleteFile(id, authentication.getName());

        log.info("Power of Attorney file deleted successfully with ID: {} by user: {}", id, authentication.getName());

        return ResponseEntity.noContent().build();
    }

    @Operation(
        summary = "Get Power of Attorney file details by ID",
        description = """
            Get detailed information about a Power of Attorney file by its ID,
            including metadata but not the file content.

            **Authorization:** User can only view details of their own Power of Attorney files.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File details retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PoaFileResponseDto.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @GetMapping("/files/{id}")
    public ResponseEntity<PoaFileResponseDto> getFileDetails(
            @Parameter(description = "File ID", required = true, example = "1")
            @PathVariable Long id,
            Authentication authentication) {

        log.info("Power of Attorney file details request for ID: {} from user: {}", id, authentication.getName());

        PoaFileResponseDto fileDetails = poaFileService.getFileDetails(id, authentication.getName());

        return ResponseEntity.ok(fileDetails);
    }

    @Operation(
        summary = "Search Power of Attorney files by filename",
        description = """
            Search for Power of Attorney files by filename (partial match, case-insensitive).

            **Authorization:** User can only search their own Power of Attorney files.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PoaFileListDto.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed")
    })
    @GetMapping("/files/search/filename")
    public ResponseEntity<List<PoaFileListDto>> searchFilesByFilename(
            @Parameter(description = "Filename to search for (partial match)", required = true, example = "power_of_attorney")
            @RequestParam String filename,
            Authentication authentication) {

        log.info("Power of Attorney files search by filename: '{}' from user: {}", filename, authentication.getName());

        List<PoaFileListDto> files = poaFileService.searchFilesByFilename(filename, authentication.getName());

        log.info("Found {} Power of Attorney files matching filename: '{}' for user: {}",
            files.size(), filename, authentication.getName());

        return ResponseEntity.ok(files);
    }

    @Operation(
        summary = "Search Power of Attorney files by tag",
        description = """
            Search for Power of Attorney files by tag (partial match, case-insensitive).

            **Authorization:** User can only search their own Power of Attorney files.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PoaFileListDto.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed")
    })
    @GetMapping("/files/search/tag")
    public ResponseEntity<List<PoaFileListDto>> searchFilesByTag(
            @Parameter(description = "Tag to search for (partial match)", required = true, example = "original")
            @RequestParam String tag,
            Authentication authentication) {

        log.info("Power of Attorney files search by tag: '{}' from user: {}", tag, authentication.getName());

        List<PoaFileListDto> files = poaFileService.searchFilesByTag(tag, authentication.getName());

        log.info("Found {} Power of Attorney files matching tag: '{}' for user: {}",
            files.size(), tag, authentication.getName());

        return ResponseEntity.ok(files);
    }

    @Operation(
        summary = "Get file count for a specific Power of Attorney",
        description = """
            Get the number of files associated with a specific Power of Attorney.

            **Authorization:** User can only get counts for their own Power of Attorney records.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File count retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed"),
        @ApiResponse(responseCode = "404", description = "Power of Attorney not found")
    })
    @GetMapping("/{poaId}/files/count")
    public ResponseEntity<Long> getFileCountByPowerOfAttorney(
            @Parameter(description = "Power of Attorney ID", required = true, example = "1")
            @PathVariable Long poaId,
            Authentication authentication) {

        log.info("Power of Attorney file count request for POA ID: {} from user: {}", poaId, authentication.getName());

        long count = poaFileService.getFileCountByPowerOfAttorney(poaId, authentication.getName());

        return ResponseEntity.ok(count);
    }

    @Operation(
        summary = "Get total file count for the authenticated user",
        description = """
            Get the total number of Power of Attorney files uploaded by the authenticated user
            across all their Power of Attorney records.

            **Authorization:** User can only see their own file count.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Total file count retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "402", description = "Payment required - subscription needed")
    })
    @GetMapping("/files/count")
    public ResponseEntity<Long> getTotalFileCount(Authentication authentication) {

        log.info("Total Power of Attorney file count request from user: {}", authentication.getName());

        long count = poaFileService.getTotalFileCount(authentication.getName());

        return ResponseEntity.ok(count);
    }
}