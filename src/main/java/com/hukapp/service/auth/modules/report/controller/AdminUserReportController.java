package com.hukapp.service.auth.modules.report.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.report.dto.response.AdminReportAboutUser;
import com.hukapp.service.auth.modules.report.service.AdminUserReportService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Admin controller for generating comprehensive user reports
 * Requires ADMIN role for all operations
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/users/reports")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Admin User Reports", description = "Admin endpoints for generating comprehensive reports about all users")
public class AdminUserReportController {

    private final AdminUserReportService adminUserReportService;

    @Operation(
        summary = "Generate comprehensive reports for all users",
        description = """
            Generates detailed reports for all users in the system, including:
            
            **User Information:**
            - User ID and creation date
            - New user status
            - User roles and permissions
            
            **Subscription Information:**
            - Current subscription level (BASIC, PREMIUM, ENTERPRISE)
            - Total payment amount from all successful transactions
            
            **Admin Privileges:** This endpoint requires ADMIN role and excludes sensitive information 
            as per admin reporting standards.
            
            **No Pagination:** Returns all users in a single response for comprehensive admin overview.
            """
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Successfully generated user reports",
            content = @Content(
                mediaType = "application/json",
                array = @ArraySchema(schema = @Schema(implementation = AdminReportAboutUser.class))
            )
        ),
        @ApiResponse(
            responseCode = "401", 
            description = "Unauthorized - User not authenticated",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "403", 
            description = "Access denied - ADMIN role required",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error while generating reports",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping
    public ResponseEntity<List<AdminReportAboutUser>> generateAllUserReports(
            @Parameter(hidden = true) Authentication authentication) {
        
        log.info("Admin user reports requested by: {}", authentication.getName());
        
        List<AdminReportAboutUser> reports = adminUserReportService.generateAllUserReports();
        
        log.info("Successfully generated {} user reports for admin: {}", 
                reports.size(), authentication.getName());
        
        return ResponseEntity.ok(reports);
    }
}
