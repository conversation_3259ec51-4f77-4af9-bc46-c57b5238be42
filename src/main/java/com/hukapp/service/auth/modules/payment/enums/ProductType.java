package com.hukapp.service.auth.modules.payment.enums;

/**
 * Enum representing different subscription levels available in the system.
 * The order of enum values represents the hierarchy of subscription levels,
 * where higher levels include all features of lower levels.
 */
public enum ProductType {
    /**
     * Basic subscription level - entry level access
     */
    BASIC(1, "Temel Abonelik"),

    /**
     * Premium subscription level - enhanced features
     */
    PREMIUM(2, "Premium Abonelik"),

    /**
     * Enterprise subscription level - full access with advanced features
     */
    ENTERPRISE(3, "Kurumsal Abonelik");

    private final int level;
    private final String displayName;

    ProductType(int level, String displayName) {
        this.level = level;
        this.displayName = displayName;
    }

    /**
     * Gets the numeric level of this subscription type
     * @return the subscription level (higher numbers = higher tier)
     */
    public int getLevel() {
        return level;
    }

    /**
     * Gets the display name of this subscription type
     * @return the human-readable name
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Checks if this subscription type meets or exceeds the required level
     * @param required the required subscription type
     * @return true if this subscription meets the requirement
     */
    public boolean meetsRequirement(ProductType required) {
        return this.level >= required.level;
    }
}
