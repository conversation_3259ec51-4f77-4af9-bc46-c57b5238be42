package com.hukapp.service.auth.modules.payment.mapper;

import java.util.List;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.payment.dto.ProductCreateRequest;
import com.hukapp.service.auth.modules.payment.dto.ProductResponse;
import com.hukapp.service.auth.modules.payment.dto.ProductUpdateRequest;
import com.hukapp.service.auth.modules.payment.entity.Product;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true)
)
public interface ProductMapper {

    /**
     * Convert Product entity to ProductResponse DTO
     * @param product Product entity
     * @return ProductResponse DTO
     */
    ProductResponse toResponse(Product product);

    /**
     * Convert list of Product entities to list of ProductResponse DTOs
     * @param products List of Product entities
     * @return List of ProductResponse DTOs
     */
    List<ProductResponse> toResponseList(List<Product> products);

    /**
     * Convert ProductCreateRequest DTO to Product entity
     * @param request ProductCreateRequest DTO
     * @return Product entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    Product toEntity(ProductCreateRequest request);

    /**
     * Update Product entity from ProductUpdateRequest DTO
     * @param request ProductUpdateRequest DTO
     * @param product Product entity to update
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateEntity(ProductUpdateRequest request, @MappingTarget Product product);

}
