package com.hukapp.service.auth.modules.person.dto.response;

import com.hukapp.service.auth.common.dto.response.BaseResponse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Email verification response")
public class VerifyEmailResponse extends BaseResponse {
    
    @Schema(description = "Whether the email was successfully verified")
    private boolean verified;
    
    @Schema(description = "Person ID if verification was successful")
    private Long personId;
}
