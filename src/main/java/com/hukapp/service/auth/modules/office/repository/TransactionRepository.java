package com.hukapp.service.auth.modules.office.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.hukapp.service.auth.modules.office.entity.Transaction;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;
import com.hukapp.service.auth.modules.person.entity.Person;

public interface TransactionRepository extends JpaRepository<Transaction, Long> {

    List<Transaction> findAllByOwner(Person person);

    Optional<Transaction> findByIdAndOwner(Long id, Person person);

    List<Transaction> findAllByOwnerAndTransactionTypeCategory(Person person, TransactionCategory category);
    
}
