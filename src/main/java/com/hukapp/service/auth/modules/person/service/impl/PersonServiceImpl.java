package com.hukapp.service.auth.modules.person.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.cache.InMemoryOtpCache;
import com.hukapp.service.auth.common.cache.PendingPersonCache;
import com.hukapp.service.auth.common.exception.custom.AuthException;
import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.common.exception.custom.UnexpectedStatusException;
import com.hukapp.service.auth.modules.email.service.EmailService;
import com.hukapp.service.auth.modules.person.dto.request.PersonCreateRequest;
import com.hukapp.service.auth.modules.person.dto.request.PersonLoginRequest;
import com.hukapp.service.auth.modules.person.dto.request.VerifyEmailRequest;
import com.hukapp.service.auth.modules.person.dto.response.PersonCreateResponse;
import com.hukapp.service.auth.modules.person.dto.response.PersonLoginResponse;
import com.hukapp.service.auth.modules.person.dto.response.VerifyEmailResponse;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.entity.AvasRole;
import com.hukapp.service.auth.modules.person.mapper.PersonMapper;
import com.hukapp.service.auth.modules.person.repository.OtpRepository;
import com.hukapp.service.auth.modules.person.repository.PersonRepository;
import com.hukapp.service.auth.modules.person.service.PersonService;

import org.springframework.context.MessageSource;
import java.util.Locale;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;

@Service
@RequiredArgsConstructor
@Slf4j
public class PersonServiceImpl implements PersonService {

    private final PersonRepository personRepository;
    private final PersonMapper personMapper;
    private final PasswordEncoder passwordEncoder;
    private final MessageSource messageSource;
    private final OtpRepository otpRepository;
    private final EmailService emailService;
    private final InMemoryOtpCache otpCache;
    private final PendingPersonCache pendingPersonCache;

    private static final Random RANDOM = new Random();

    @Override
    public PersonCreateResponse initiatePersonCreation(PersonCreateRequest personCreateRequest) {
        log.info("Initiating person creation process for email: {}", personCreateRequest.getEmail());

        // Check if email already exists
        if (personRepository.findByEmail(personCreateRequest.getEmail()).isPresent()) {
            log.warn("Email already exists: {}", personCreateRequest.getEmail());
            throw new ResourceAlreadyExistsException(messageSource.getMessage("person.email.exists", null, "Email already exists", LocaleContextHolder.getLocale()));
        }

        if (personRepository.findByIdentityNumber(personCreateRequest.getIdentityNumber()).isPresent()) {
            log.warn("Identity number already exists: {}", personCreateRequest.getIdentityNumber());
            throw new ResourceAlreadyExistsException(messageSource.getMessage("person.email.exists", null, "Person already exists", LocaleContextHolder.getLocale()));
        }

        // Generate OTP
        String otp = generateOtp();

        // Store OTP in cache
        otpCache.put(personCreateRequest.getEmail(), otp);

        // Store person data in cache
        pendingPersonCache.put(personCreateRequest.getEmail(), personCreateRequest);

        // Send verification email
        try {
            emailService.sendOtpEmail(personCreateRequest.getEmail(), otp);
            log.info("Verification email sent to: {}", personCreateRequest.getEmail());
        } catch (Exception e) {
            log.error("Failed to send verification email to: {}", personCreateRequest.getEmail(), e);
            // We still continue the process even if email sending fails
        }

        // Create response
        PersonCreateResponse response = new PersonCreateResponse();
        response.setEmail(personCreateRequest.getEmail());
        response.setResponseMessage(messageSource.getMessage("person.verification.email.sent", null, "Verification email sent", LocaleContextHolder.getLocale()));

        return response;
    }

    @Override
    public VerifyEmailResponse verifyEmailAndCreatePerson(VerifyEmailRequest verifyEmailRequest) {
        log.info("Verifying email for: {}", verifyEmailRequest.getEmail());

        // Validate OTP
        if (!otpCache.validateOtp(verifyEmailRequest.getEmail(), verifyEmailRequest.getOtp())) {
            log.warn("Invalid or expired OTP for email: {}", verifyEmailRequest.getEmail());
            throw new AuthException(messageSource.getMessage("otp.invalid.or.expired", null, "Invalid or expired OTP", LocaleContextHolder.getLocale()));
        }

        // Get pending person data
        PersonCreateRequest personCreateRequest = pendingPersonCache.get(verifyEmailRequest.getEmail());
        if (personCreateRequest == null) {
            log.warn("No pending registration found for email: {}", verifyEmailRequest.getEmail());
            throw new AuthException(messageSource.getMessage("person.registration.expired", null, "Registration request expired", LocaleContextHolder.getLocale()));
        }

        // Create person
        Person person = personMapper.toEntity(personCreateRequest);
        person.setPassword(passwordEncoder.encode(person.getPassword()));
        person.setEmailVerified(true); // Set email as verified
        person = personRepository.save(person);

        // Remove from caches
        otpCache.remove(verifyEmailRequest.getEmail());
        pendingPersonCache.remove(verifyEmailRequest.getEmail());

        log.info("Person created successfully with verified email: {}", verifyEmailRequest.getEmail());

        // Create response
        VerifyEmailResponse response = new VerifyEmailResponse();
        response.setVerified(true);
        response.setPersonId(person.getId());
        response.setResponseMessage(messageSource.getMessage("person.created.success", null, "Person created successfully", LocaleContextHolder.getLocale()));

        return response;
    }

    // Legacy method - direct save without verification
    @Override
    public PersonCreateResponse savePerson(PersonCreateRequest personCreateRequest) {
        // Base entity fields will be auto-populated by JPA auditing
        Person person = personMapper.toEntity(personCreateRequest);
        // Encode the password before saving
        person.setPassword(passwordEncoder.encode(person.getPassword()));
        person = personRepository.save(person);
        return personMapper.toDTO(person);
    }

    /**
     * Generates a random 8-digit OTP
     * @return the generated OTP
     */
    private String generateOtp() {
        return String.format("%08d", 10000000 + RANDOM.nextInt(90000000));
    }

    // Read
    @Override
    public List<Person> getAllPersons() {
        return personRepository.findAll();
    }

    @Override
    public Optional<Person> getPersonById(Long id) {
        return personRepository.findById(id);
    }

    @Override
    public Optional<Person> getPersonByIdentityNumber(Long identityNumber) {
        return personRepository.findByIdentityNumber(identityNumber);
    }

    @Override
    public Optional<Person> getPersonByEmail(String email) {
        return personRepository.findByEmail(email);
    }

    // Update
    @Override
    public Person updatePerson(Person person) {
        return personRepository.save(person);
    }

    // Delete
    @Override
    public void deletePerson(Long id) {
        personRepository.deleteById(id);
    }

    @Override
    public PersonLoginResponse login(PersonLoginRequest personLoginRequest) {
        Person person = personRepository.findByEmail(personLoginRequest.getEmail())
                .orElseThrow(() -> new AuthException(
                        messageSource.getMessage("auth.error.invalidCredentials", null, Locale.getDefault())));

        if (!passwordEncoder.matches(personLoginRequest.getPassword(), person.getPassword())) {
            throw new AuthException(
                    messageSource.getMessage("auth.error.invalidCredentials", null, Locale.getDefault()));
        }

        // TODO: Login sonrasinda kullaniciya login olma islemi ile ilgli bilgilendirme
        // yapilabilir
        // Ornek: Son giris tarihi, son giris yeri, son giris ip adresi, vs.
        return PersonLoginResponse.builder()
                .id(person.getId())
                .name(person.getName())
                .surname(person.getSurname())
                .isNewUser(person.isNewUser())
                .isDeleted(person.getIsDeleted())
                .jwt("jwt") // Note: You might want to implement proper JWT token generation here
                .build();
    }

    @Override
    public void updatePassword(String email, String otp, String newPassword) {

        // Check if the OTP is validated and not expired if not throw an exception
        otpRepository.findByEmailAndOtpValueAndIsUsedTrueAndExpirationTimeAfter(email, otp, LocalDateTime.now())
                .orElseThrow(() -> new AuthException(
                        messageSource.getMessage("otp.invalid.or.expired", null, Locale.getDefault())));

        // Find the person by email and update the password if person is not found throw an exception
        Person person = personRepository.findByEmail(email)
                .orElseThrow(() -> new UnexpectedStatusException(
                        messageSource.getMessage("otp.invalid.or.expired", null, Locale.getDefault())));

        person.setPassword(passwordEncoder.encode(newPassword));
        personRepository.save(person);

        // TODO: Parola degistirme islemi sonrasinda kullaniciya bilgilendirme yapilabilir
        // Ornek: Parola degistirme islemi basarili, parola degistirme islemi basarisiz, vs.

    }

    @Override
    public Person getPersonByEmailOrElseThrow(String email) {
        return personRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Kullanıcı bulunamadı: " + email));
    }

    @Override
    public List<String> getUserRoles(String email) {
        return personRepository.findByEmail(email)
                .map(Person::getRoles)
                .orElseThrow(() -> new ResourceNotFoundException("Kullanıcı bulunamadı: " + email))
                .stream()
                .map(AvasRole::getRoleName)
                .toList();
    }

    @Override
    public Person getPersonByIdOrElseThrow(Long id) {
        return personRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Kullanıcı bulunamadı: " + id));
    }

}