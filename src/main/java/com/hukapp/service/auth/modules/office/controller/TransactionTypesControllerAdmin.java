package com.hukapp.service.auth.modules.office.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.office.dto.TransactionTypeRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionTypeResponse;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;
import com.hukapp.service.auth.modules.office.service.TransactionTypeService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Slf4j
@RestController
@RequestMapping("api/admin/office/transaction-types")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Admin Transaction Types Management", description = "Admin endpoints for managing transaction types")
public class TransactionTypesControllerAdmin {

    private final TransactionTypeService transactionTypeService;

    @Operation(summary = "Create Transaction Type", description = "Create a new transaction type")
    @PostMapping
    public ResponseEntity<TransactionTypeResponse> createTransactionType(
            @RequestBody @Valid TransactionTypeRequest entity) {

        return ResponseEntity.ok(
                transactionTypeService.createTransactionType(entity));
    }

    @Operation(summary = "Get all transaction types. You can also filter by category")
    @GetMapping
    public ResponseEntity<List<TransactionTypeResponse>> getTransactionTypes(
            @RequestParam(required = false) TransactionCategory category) {

        return ResponseEntity.ok(transactionTypeService.getAllTransactionTypes(category));
    }

    @Operation(summary = "Update an existing transaction type")
    @PutMapping("{id}")
    public ResponseEntity<TransactionTypeResponse> updateTransactionType(@PathVariable Long id,
            @RequestBody @Valid TransactionTypeRequest entity) {

        return ResponseEntity.ok(transactionTypeService.updateTransactionType(id, entity));
    }

    @Operation(summary = "Delete an existing transaction type")
    @DeleteMapping("{id}")
    public ResponseEntity<Object> deleteTransactionType(@PathVariable Long id) {

        transactionTypeService.deleteTransactionType(id);
        return ResponseEntity.noContent().build();
    }

}
