package com.hukapp.service.auth.modules.file.mapper;

import java.util.List;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.file.dto.response.FileDownloadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileListDto;
import com.hukapp.service.auth.modules.file.dto.response.FileResponseDto;
import com.hukapp.service.auth.modules.file.entity.TemplateFileMetadata;
import com.hukapp.service.auth.modules.person.entity.Person;

/**
 * MapStruct mapper for file entity and DTO conversions
 */
@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true)
)
public interface FileMapper {

    /**
     * Maps FileEntity to FileResponseDto with complete information
     */
    @Mapping(target = "uploader", source = "uploader", qualifiedByName = "mapUploaderInfo")
    @Mapping(target = "responseMessage", constant = "File retrieved successfully")
    FileResponseDto toResponseDto(TemplateFileMetadata fileEntity);

    /**
     * Maps FileEntity to FileListDto for listing purposes
     */
    @Mapping(target = "uploaderName", source = "uploader", qualifiedByName = "mapUploaderName")
    @Mapping(target = "uploaderEmail", source = "uploader.email")
    FileListDto toListDto(TemplateFileMetadata fileEntity);

    /**
     * Maps FileEntity to FileDownloadDto for download purposes
     */
    @Mapping(target = "filename", source = "originalFilename")
    @Mapping(target = "content", source = "content.content")
    FileDownloadDto toDownloadDto(TemplateFileMetadata fileEntity);

    /**
     * Maps FileEntity to FileDownloadDto without content (metadata only)
     */
    @Mapping(target = "filename", source = "originalFilename")
    @Mapping(target = "content", ignore = true)
    FileDownloadDto toDownloadDtoWithoutContent(TemplateFileMetadata fileEntity);

    /**
     * Maps list of FileEntity to list of FileResponseDto
     */
    List<FileResponseDto> toResponseDtoList(List<TemplateFileMetadata> fileEntities);

    /**
     * Maps list of FileEntity to list of FileListDto
     */
    List<FileListDto> toListDtoList(List<TemplateFileMetadata> fileEntities);

    /**
     * Maps Person entity to UploaderInfo for FileResponseDto
     */
    @Named("mapUploaderInfo")
    default FileResponseDto.UploaderInfo mapUploaderInfo(Person person) {
        if (person == null) {
            return null;
        }
        
        FileResponseDto.UploaderInfo uploaderInfo = new FileResponseDto.UploaderInfo();
        uploaderInfo.setId(person.getId());
        uploaderInfo.setFullName(person.getName() + " " + person.getSurname());
        uploaderInfo.setEmail(person.getEmail());
        
        return uploaderInfo;
    }

    /**
     * Maps Person entity to uploader name for FileListDto
     */
    @Named("mapUploaderName")
    default String mapUploaderName(Person person) {
        if (person == null) {
            return null;
        }
        return person.getName() + " " + person.getSurname();
    }
}
