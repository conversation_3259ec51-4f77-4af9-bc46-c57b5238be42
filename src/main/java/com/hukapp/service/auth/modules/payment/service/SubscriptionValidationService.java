package com.hukapp.service.auth.modules.payment.service;

import com.hukapp.service.auth.modules.payment.enums.ProductType;
import com.hukapp.service.auth.modules.person.entity.Person;

/**
 * Service interface for validating user subscriptions and access permissions.
 * This service is responsible for checking if a user has the required subscription
 * level to access specific features or endpoints.
 */
public interface SubscriptionValidationService {
    
    /**
     * Validates if the user has an active subscription that meets the required level.
     * 
     * @param userEmail the email of the user to validate
     * @param requiredLevel the minimum subscription level required
     * @return true if the user has a valid subscription, false otherwise
     */
    boolean hasValidSubscription(String userEmail, ProductType requiredLevel);
    
    /**
     * Validates if the person entity has an active subscription that meets the required level.
     * 
     * @param person the person entity to validate
     * @param requiredLevel the minimum subscription level required
     * @return true if the person has a valid subscription, false otherwise
     */
    boolean hasValidSubscription(Person person, ProductType requiredLevel);
    
    /**
     * Gets the highest active subscription level for a user.
     * 
     * @param userEmail the email of the user
     * @return the highest subscription level, or null if no active subscription
     */
    ProductType getHighestSubscriptionLevel(String userEmail);
    
    /**
     * Gets the highest active subscription level for a person entity.
     * 
     * @param person the person entity
     * @return the highest subscription level, or null if no active subscription
     */
    ProductType getHighestSubscriptionLevel(Person person);
    
    /**
     * Checks if the user has any active subscription (regardless of level).
     * 
     * @param userEmail the email of the user
     * @return true if the user has any active subscription, false otherwise
     */
    boolean hasAnyActiveSubscription(String userEmail);
    
    /**
     * Checks if the person has any active subscription (regardless of level).
     * 
     * @param person the person entity
     * @return true if the person has any active subscription, false otherwise
     */
    boolean hasAnyActiveSubscription(Person person);
    
    /**
     * Gets a human-readable description of the subscription requirement.
     * 
     * @param requiredLevel the required subscription level
     * @return a descriptive message about the requirement
     */
    String getSubscriptionRequirementMessage(ProductType requiredLevel);
}
