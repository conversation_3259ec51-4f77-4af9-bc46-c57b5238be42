package com.hukapp.service.auth.modules.task.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Note extends BaseEntity{

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String content;

    @ManyToOne
    @JoinColumn(nullable = false)
    @JsonBackReference
    private Task task;

}