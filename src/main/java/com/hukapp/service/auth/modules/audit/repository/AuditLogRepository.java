package com.hukapp.service.auth.modules.audit.repository;

import com.hukapp.service.auth.modules.audit.entity.AuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * Repository interface for AuditLog entity with custom queries for efficient log retrieval
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long>, JpaSpecificationExecutor<AuditLog> {

    /**
     * Find audit logs by user email
     */
    List<AuditLog> findByUserEmailOrderByRequestTimestampDesc(String userEmail);

    /**
     * Find audit logs by user email within a date range
     */
    @Query("SELECT a FROM AuditLog a WHERE a.userEmail = :userEmail " +
           "AND a.requestTimestamp BETWEEN :startDate AND :endDate " +
           "ORDER BY a.requestTimestamp DESC")
    List<AuditLog> findByUserEmailAndDateRange(@Param("userEmail") String userEmail,
                                               @Param("startDate") Instant startDate,
                                               @Param("endDate") Instant endDate);

    /**
     * Find audit logs by endpoint URL
     */
    List<AuditLog> findByEndpointUrlOrderByRequestTimestampDesc(String endpointUrl);

    /**
     * Find audit logs by HTTP method
     */
    List<AuditLog> findByHttpMethodOrderByRequestTimestampDesc(String httpMethod);

    /**
     * Find audit logs by response status code
     */
    List<AuditLog> findByResponseStatusCodeOrderByRequestTimestampDesc(Integer statusCode);

    /**
     * Find failed requests (status codes >= 400)
     */
    @Query("SELECT a FROM AuditLog a WHERE a.responseStatusCode >= 400 " +
           "ORDER BY a.requestTimestamp DESC")
    List<AuditLog> findFailedRequests();

    /**
     * Find slow requests (processing time > threshold)
     */
    @Query("SELECT a FROM AuditLog a WHERE a.processingTimeMs > :thresholdMs " +
           "ORDER BY a.processingTimeMs DESC")
    List<AuditLog> findSlowRequests(@Param("thresholdMs") Long thresholdMs);

    /**
     * Get total request count for admin dashboard
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.requestTimestamp BETWEEN :startDate AND :endDate")
    Long getTotalRequestCount(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Get unique user count for admin dashboard
     */
    @Query("SELECT COUNT(DISTINCT a.userEmail) FROM AuditLog a WHERE a.requestTimestamp BETWEEN :startDate AND :endDate")
    Long getUniqueUserCount(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Get average processing time for admin dashboard
     */
    @Query("SELECT COALESCE(AVG(a.processingTimeMs), 0.0) FROM AuditLog a WHERE a.requestTimestamp BETWEEN :startDate AND :endDate")
    Double getAverageProcessingTime(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Get error count for admin dashboard
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.responseStatusCode >= 400 AND a.requestTimestamp BETWEEN :startDate AND :endDate")
    Long getErrorCount(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Get most accessed endpoints
     */
    @Query("SELECT a.endpointUrl, COUNT(a) as accessCount " +
           "FROM AuditLog a " +
           "WHERE a.requestTimestamp BETWEEN :startDate AND :endDate " +
           "GROUP BY a.endpointUrl " +
           "ORDER BY accessCount DESC")
    List<Object[]> getMostAccessedEndpoints(@Param("startDate") Instant startDate,
                                           @Param("endDate") Instant endDate);

    /**
     * Get most active users
     */
    @Query("SELECT a.userEmail, COUNT(a) as requestCount " +
           "FROM AuditLog a " +
           "WHERE a.requestTimestamp BETWEEN :startDate AND :endDate " +
           "GROUP BY a.userEmail " +
           "ORDER BY requestCount DESC")
    List<Object[]> getMostActiveUsers(@Param("startDate") Instant startDate,
                                     @Param("endDate") Instant endDate);

    /**
     * Find recent audit logs for admin overview
     */
    @Query("SELECT a FROM AuditLog a ORDER BY a.requestTimestamp DESC LIMIT 500")
    List<AuditLog> findRecentAuditLogs();

    /**
     * Count audit logs by user email
     */
    long countByUserEmail(String userEmail);

    /**
     * Delete old audit logs (for cleanup purposes)
     */
    @Query("DELETE FROM AuditLog a WHERE a.requestTimestamp < :cutoffDate")
    void deleteOldAuditLogs(@Param("cutoffDate") Instant cutoffDate);

    /**
     * Find audit logs by client IP address
     */
    List<AuditLog> findByClientIpAddressOrderByRequestTimestampDesc(String clientIpAddress);

    /**
     * Find audit logs with errors
     */
    @Query("SELECT a FROM AuditLog a WHERE a.errorMessage IS NOT NULL " +
           "ORDER BY a.requestTimestamp DESC")
    List<AuditLog> findAuditLogsWithErrors();

    /**
     * Find audit logs between start and end date
     */
    @Query("SELECT a FROM AuditLog a WHERE a.requestTimestamp BETWEEN :startDate AND :endDate " +
           "ORDER BY a.requestTimestamp DESC")
    List<AuditLog> findAuditLogsByDateRange(@Param("startDate") Instant startDate,
                                           @Param("endDate") Instant endDate);
}
