package com.hukapp.service.auth.modules.app.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.app.dto.request.CaseDetailsRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseDetailsResponse;
import com.hukapp.service.auth.modules.app.entity.CaseDetails;
import com.hukapp.service.auth.modules.person.entity.Person;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true),
    imports = {Person.class}
)
public interface CaseDetailsMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    CaseDetails toEntity(CaseDetailsRequest caseDetailsRequest);
    
    @Mapping(target = "ownerName", expression = "java(caseDetails.getOwner().getName() + ' ' + caseDetails.getOwner().getSurname())")
    @Mapping(target = "ownerEmail", expression = "java(caseDetails.getOwner().getEmail())")
    @Mapping(target = "ownerId", expression = "java(caseDetails.getOwner().getId())")
    CaseDetailsResponse toDTO(CaseDetails caseDetails);
}
