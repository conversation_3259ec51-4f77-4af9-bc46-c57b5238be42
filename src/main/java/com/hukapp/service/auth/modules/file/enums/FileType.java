package com.hukapp.service.auth.modules.file.enums;

/**
 * Enum representing different file types for categorization and validation
 */
public enum FileType {
    /**
     * Image files (jpg, jpeg, png, gif, bmp, webp)
     */
    IMAGE("Image", "image/", new String[]{"jpg", "jpeg", "png", "gif", "bmp", "webp"}),
    
    /**
     * Document files (pdf, doc, docx, txt, rtf)
     */
    DOCUMENT("Document", "application/", new String[]{"pdf", "doc", "docx", "txt", "rtf"}),
    
    /**
     * Spreadsheet files (xls, xlsx, csv)
     */
    SPREADSHEET("Spreadsheet", "application/", new String[]{"xls", "xlsx", "csv"}),
    
    /**
     * Presentation files (ppt, pptx)
     */
    PRESENTATION("Presentation", "application/", new String[]{"ppt", "pptx"}),
    
    /**
     * Archive files (zip, rar, 7z, tar, gz)
     */
    ARCHIVE("Archive", "application/", new String[]{"zip", "rar", "7z", "tar", "gz"}),
    
    /**
     * Video files (mp4, avi, mov, wmv, flv)
     */
    VIDEO("Video", "video/", new String[]{"mp4", "avi", "mov", "wmv", "flv"}),
    
    /**
     * Audio files (mp3, wav, flac, aac, ogg)
     */
    AUDIO("Audio", "audio/", new String[]{"mp3", "wav", "flac", "aac", "ogg"}),
    
    /**
     * Other/unknown file types
     */
    OTHER("Other", "", new String[]{});

    private final String displayName;
    private final String mimeTypePrefix;
    private final String[] allowedExtensions;

    FileType(String displayName, String mimeTypePrefix, String[] allowedExtensions) {
        this.displayName = displayName;
        this.mimeTypePrefix = mimeTypePrefix;
        this.allowedExtensions = allowedExtensions;
    }

    /**
     * Gets the display name of this file type
     * @return the human-readable name
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Gets the MIME type prefix for this file type
     * @return the MIME type prefix
     */
    public String getMimeTypePrefix() {
        return mimeTypePrefix;
    }

    /**
     * Gets the allowed file extensions for this file type
     * @return array of allowed extensions
     */
    public String[] getAllowedExtensions() {
        return allowedExtensions.clone();
    }

    /**
     * Determines the file type based on the file extension
     * @param filename the filename to analyze
     * @return the determined file type
     */
    public static FileType fromFilename(String filename) {
        if (filename == null || filename.isEmpty()) {
            return OTHER;
        }
        
        String extension = getFileExtension(filename).toLowerCase();
        
        for (FileType type : values()) {
            for (String allowedExt : type.allowedExtensions) {
                if (allowedExt.equalsIgnoreCase(extension)) {
                    return type;
                }
            }
        }
        
        return OTHER;
    }

    /**
     * Determines the file type based on the MIME type
     * @param mimeType the MIME type to analyze
     * @return the determined file type
     */
    public static FileType fromMimeType(String mimeType) {
        if (mimeType == null || mimeType.isEmpty()) {
            return OTHER;
        }
        
        for (FileType type : values()) {
            if (type != OTHER && mimeType.startsWith(type.mimeTypePrefix)) {
                return type;
            }
        }
        
        return OTHER;
    }

    /**
     * Checks if the given file extension is allowed for this file type
     * @param extension the file extension to check
     * @return true if the extension is allowed
     */
    public boolean isExtensionAllowed(String extension) {
        if (extension == null) {
            return false;
        }
        
        for (String allowedExt : allowedExtensions) {
            if (allowedExt.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Extracts the file extension from a filename
     * @param filename the filename
     * @return the file extension without the dot
     */
    private static String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }
}
