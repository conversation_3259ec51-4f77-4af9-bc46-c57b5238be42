package com.hukapp.service.auth.modules.office.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.office.dto.TransactionRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionResponse;
import com.hukapp.service.auth.modules.office.entity.Transaction;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true)
)
public interface TransactionMapper {
    

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    Transaction toEntity(TransactionRequest request);

    @Mapping(target = "ownerName", expression = "java(transaction.getOwner().getName() + \" \" + transaction.getOwner().getSurname())")
    @Mapping(target = "ownerEmail", expression = "java(transaction.getOwner().getEmail())")
    @Mapping(target = "transactionType", source = "transaction.transactionType")
    TransactionResponse toDTO(Transaction transaction);


    
}
