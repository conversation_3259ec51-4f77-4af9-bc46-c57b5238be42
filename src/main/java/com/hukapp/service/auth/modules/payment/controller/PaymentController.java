package com.hukapp.service.auth.modules.payment.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.payment.dto.CreateOrderRequest;
import com.hukapp.service.auth.modules.payment.dto.PaymentResponse;
import com.hukapp.service.auth.modules.payment.dto.PaymentStatusResponse;
import com.hukapp.service.auth.modules.payment.dto.ProductResponse;
import com.hukapp.service.auth.modules.payment.service.PaymentService;
import com.hukapp.service.auth.modules.payment.service.ProductService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("api/user/payment")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Payment", description = "Payment operations with iyzico")
public class PaymentController {

    private final PaymentService paymentService;
    private final ProductService productService;

    @Operation(summary = "Get all products", description = "Retrieve all available products")
    @GetMapping
    public ResponseEntity<List<ProductResponse>> getAllProducts(Authentication authentication) {
        log.debug("Getting all products for the user: {}", authentication.getName());
        List<ProductResponse> products = productService.getAllProducts();
        return ResponseEntity.ok(products);
    }

    @Operation(summary = "Create order", description = "Create a new order to pay with iyzico for selected products")
    @PostMapping("/create-order")
    public ResponseEntity<PaymentResponse> createOrder(@RequestBody @Validated CreateOrderRequest request,
            Authentication authentication) {

        log.debug("Creating payment for user: {}", authentication.getName());
        return ResponseEntity.ok(paymentService.createPayment(request, authentication.getName()));
    }

    @Operation(summary = "Get all payments", description = "Get all payments for the authenticated user")
    @GetMapping("/payments")
    public ResponseEntity<List<PaymentResponse>> getAllPayments(Authentication authentication) {
        log.debug("Getting all payments for user: {}", authentication.getName());
        return ResponseEntity.ok(paymentService.getAllPayments(authentication.getName()));
    }

    @Operation(summary = "Get payment by ID", description = "Get payment details by payment ID")
    @GetMapping("/{paymentId}")
    public ResponseEntity<PaymentResponse> getPaymentById(@PathVariable String paymentId,
            Authentication authentication) {
        log.debug("Getting payment with ID: {} for user: {}", paymentId, authentication.getName());
        return ResponseEntity.ok(paymentService.getPaymentById(paymentId, authentication.getName()));
    }

    @Operation(summary = "Retrieve payment status", description = "Check if a payment is made or not using iyzico API and update payment status")
    @GetMapping("/{paymentId}/check")
    public ResponseEntity<PaymentStatusResponse> checkPaymentStatus(@PathVariable String paymentId,
            Authentication authentication) {
        log.debug("Checking payment status for payment ID: {} and user: {}", paymentId, authentication.getName());
        return ResponseEntity.ok(paymentService.retrievePaymentByPaymentIdAndPerson(paymentId, authentication.getName()));
    }

}
