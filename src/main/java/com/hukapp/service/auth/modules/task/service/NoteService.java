package com.hukapp.service.auth.modules.task.service;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.task.dto.NoteRequest;
import com.hukapp.service.auth.modules.task.dto.NoteResponse;
import com.hukapp.service.auth.modules.task.entity.Note;
import com.hukapp.service.auth.modules.task.entity.Task;
import com.hukapp.service.auth.modules.task.mapper.NoteMapper;
import com.hukapp.service.auth.modules.task.repository.NoteRepository;
import com.hukapp.service.auth.modules.task.repository.TaskRepository;

import lombok.RequiredArgsConstructor;

import org.springframework.security.core.Authentication;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class NoteService {

    private final NoteRepository noteRepository;
    private final TaskRepository taskRepository;
    private final PersonService personService;
    private final NoteMapper noteMapper;

    public NoteResponse createNoteForTask(long taskId, NoteRequest noteRequest, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        Optional<Task> task = taskRepository.findByIdAndReporter(taskId, person);

        if (task.isPresent()) {
            Note note = noteMapper.toEntity(noteRequest);
            note.setTask(task.get());
            return noteMapper.toDTO(noteRepository.save(note));
        } else {
            throw new ResourceNotFoundException("Task not found");
        }

    }

    public void deleteNote(Long id, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        Optional<Note> note = noteRepository.findById(id);

        if (note.isPresent() && note.get().getTask().getReporter().getId().equals(person.getId())) {
            noteRepository.deleteById(id);
        } else {
            throw new ResourceNotFoundException("Note not found");
        }
    }

    public NoteResponse updateNote(Long noteId, NoteRequest entity, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        Optional<Note> note = noteRepository.findById(noteId);

        if (note.isPresent() && note.get().getTask().getReporter().getId().equals(person.getId())) {
            Note existingNote = note.get();
            existingNote.setContent(entity.getContent());
            return noteMapper.toDTO(noteRepository.save(existingNote));
        } else {
            throw new ResourceNotFoundException("Note not found");
        }
    }
}