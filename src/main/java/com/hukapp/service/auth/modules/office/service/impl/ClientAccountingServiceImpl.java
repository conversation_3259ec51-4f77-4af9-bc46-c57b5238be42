package com.hukapp.service.auth.modules.office.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.common.exception.custom.UnexpectedStatusException;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingCreateRequest;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingResponse;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingUpdateRequest;
import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.office.entity.ClientAccounting;
import com.hukapp.service.auth.modules.office.enums.AccountingType;
import com.hukapp.service.auth.modules.office.mapper.ClientAccountingMapper;
import com.hukapp.service.auth.modules.office.repository.ClientAccountingRepository;
import com.hukapp.service.auth.modules.office.repository.ClientRepository;
import com.hukapp.service.auth.modules.office.service.ClientAccountingService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ClientAccountingServiceImpl implements ClientAccountingService {

    private static final String RECORD_NOT_FOUND_MESSAGE = "Muhasebe kaydı bulunamadı";
    private static final String CLIENT_NOT_FOUND_MESSAGE = "Müvekkil bulunamadı";

    private final ClientAccountingRepository clientAccountingRepository;
    private final ClientRepository clientRepository;
    private final PersonService personService;
    private final ClientAccountingMapper clientAccountingMapper;

    @Override
    public ClientAccountingResponse createClientAccounting(ClientAccountingCreateRequest request, String ownerEmail) {
        log.debug("Creating client accounting record for client ID: {} by owner: {}", request.getClientId(),
                ownerEmail);
        
        // throw exception if client and caseNumber both null
        if (request.getClientId() == null && request.getCaseNumber() == null) {
            throw new UnexpectedStatusException("Müvekkil veya dosya numarası gereklidir");
        }

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        // Client is optional, so we check if it's null
        Client client = null;
        if (request.getClientId() != null) {
            client = getClientByIdAndOwner(request.getClientId(), owner);
        }

        ClientAccounting clientAccounting = clientAccountingMapper.toEntity(request);
        clientAccounting.setOwner(owner);
        clientAccounting.setClient(client);

        ClientAccounting savedRecord = clientAccountingRepository.save(clientAccounting);

        log.debug("Client accounting record created successfully with ID: {}", savedRecord.getId());
        return clientAccountingMapper.toResponse(savedRecord);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ClientAccountingResponse> getAllClientAccountingRecords(String ownerEmail) {
        log.debug("Getting all client accounting records for owner: {}", ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        List<ClientAccounting> records = clientAccountingRepository.findByOwnerOrderByRecordDateDesc(owner);

        return records.stream()
                .map(clientAccountingMapper::toResponse)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ClientAccountingResponse> getClientAccountingRecordsByClient(Long clientId, String ownerEmail) {
        log.debug("Getting client accounting records for client ID: {} by owner: {}", clientId, ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        Client client = getClientByIdAndOwner(clientId, owner);

        List<ClientAccounting> records = clientAccountingRepository.findByClientAndOwnerOrderByRecordDateDesc(client,
                owner);

        return records.stream()
                .map(clientAccountingMapper::toResponse)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ClientAccountingResponse> getClientAccountingRecordsByType(AccountingType accountingType,
            String ownerEmail) {
        log.debug("Getting client accounting records by type: {} for owner: {}", accountingType, ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        List<ClientAccounting> records = clientAccountingRepository
                .findByOwnerAndAccountingTypeOrderByRecordDateDesc(owner, accountingType);

        return records.stream()
                .map(clientAccountingMapper::toResponse)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ClientAccountingResponse> getClientAccountingRecordsByClientAndType(Long clientId,
            AccountingType accountingType, String ownerEmail) {
        log.debug("Getting client accounting records for client ID: {} and type: {} by owner: {}", clientId,
                accountingType, ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        Client client = getClientByIdAndOwner(clientId, owner);

        List<ClientAccounting> records = clientAccountingRepository
                .findByClientAndOwnerAndAccountingTypeOrderByRecordDateDesc(client, owner, accountingType);

        return records.stream()
                .map(clientAccountingMapper::toResponse)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public ClientAccountingResponse getClientAccountingRecordById(Long id, String ownerEmail) {
        log.debug("Getting client accounting record with ID: {} for owner: {}", id, ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        ClientAccounting record = clientAccountingRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(RECORD_NOT_FOUND_MESSAGE));

        return clientAccountingMapper.toResponse(record);
    }

    @Override
    public ClientAccountingResponse updateClientAccounting(Long id, ClientAccountingUpdateRequest request,
            String ownerEmail) {
        log.debug("Updating client accounting record with ID: {} for owner: {}", id, ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        ClientAccounting existingRecord = clientAccountingRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(RECORD_NOT_FOUND_MESSAGE));

        clientAccountingMapper.updateEntityFromRequest(request, existingRecord);

        ClientAccounting updatedRecord = clientAccountingRepository.save(existingRecord);

        log.debug("Client accounting record updated successfully with ID: {}", updatedRecord.getId());
        return clientAccountingMapper.toResponse(updatedRecord);
    }

    @Override
    public void deleteClientAccounting(Long id, String ownerEmail) {
        log.debug("Deleting client accounting record with ID: {} for owner: {}", id, ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        ClientAccounting record = clientAccountingRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(RECORD_NOT_FOUND_MESSAGE));

        clientAccountingRepository.delete(record);

        log.debug("Client accounting record deleted successfully with ID: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Double getTotalAmountByClientAndType(Long clientId, AccountingType accountingType, String ownerEmail) {
        log.debug("Getting total amount for client ID: {} and type: {} by owner: {}", clientId, accountingType,
                ownerEmail);

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        Client client = getClientByIdAndOwner(clientId, owner);

        return clientAccountingRepository.getTotalAmountByClientAndOwnerAndAccountingType(client, owner,
                accountingType);
    }

    private Client getClientByIdAndOwner(Long clientId, Person owner) {
        return clientRepository.findByIdAndOwnerId(clientId, owner.getId())
                .orElseThrow(() -> new ResourceNotFoundException(CLIENT_NOT_FOUND_MESSAGE));
    }

    @Override
    public List<ClientAccountingResponse> getAllClientAccountingRecordsByType(AccountingType accountingType,
            String ownerEmail) {

        Person owner = personService.getPersonByEmailOrElseThrow(ownerEmail);
        List<ClientAccounting> records = clientAccountingRepository
                .findByOwnerAndAccountingTypeOrderByRecordDateDesc(owner, accountingType);

        return records.stream()
                .map(clientAccountingMapper::toResponse)
                .toList();
    }

}
