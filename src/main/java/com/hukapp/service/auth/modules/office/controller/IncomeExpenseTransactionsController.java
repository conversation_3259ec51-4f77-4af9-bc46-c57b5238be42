package com.hukapp.service.auth.modules.office.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.common.dto.response.BaseResponse;
import com.hukapp.service.auth.modules.office.dto.CurrencyDto;
import com.hukapp.service.auth.modules.office.dto.TransactionRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionResponse;
import com.hukapp.service.auth.modules.office.dto.TransactionTypeResponse;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;
import com.hukapp.service.auth.modules.office.service.TransactionService;
import com.hukapp.service.auth.modules.office.service.TransactionTypeService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Currency;
import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Slf4j
@RestController
@RequestMapping("api/user/office/transactions/income-expense")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Income/Expense Transactions", description = "API for managing income and expense transactions")
public class IncomeExpenseTransactionsController {

    private final TransactionService transactionService;
    private final TransactionTypeService transactionTypeService;

    @Operation(summary = "Get Currency List", description = "Get available currencies")
    @GetMapping("currencies")
    public ResponseEntity<List<CurrencyDto>> getCurrencyList() {

        List<CurrencyDto> currencies = Currency.getAvailableCurrencies().stream()
                .map(currency -> CurrencyDto.builder()
                        .code(currency.getCurrencyCode())
                        .name(currency.getDisplayName())
                        .build())
                .sorted((c1, c2) -> c1.getName().compareToIgnoreCase(c2.getName()))
                .toList();

        return ResponseEntity.ok(currencies);
    }

    @Operation(summary = "Save Income/Expense", description = "Create a new income or expense")
    @PostMapping
    public ResponseEntity<TransactionResponse> saveIncomeExpense(@RequestBody TransactionRequest entity,
            Authentication authentication) {
        return ResponseEntity.ok(transactionService.saveTransaction(entity, authentication.getName()));
    }

    @Operation(summary = "Get all Income/Expense transactions, you can filter by category")
    @GetMapping
    public ResponseEntity<List<TransactionResponse>> getAllTransactions(Authentication authentication,
            @RequestParam(required = false) TransactionCategory category) {
        return ResponseEntity.ok(transactionService.getAllTransactions(authentication.getName(), category));
    }

    @Operation(summary = "Update Income/Expense transaction by id")
    @PutMapping("{id}")
    public ResponseEntity<TransactionResponse> putMethodName(@PathVariable Long id, @RequestBody TransactionRequest entity, Authentication authentication) {
        TransactionResponse transactionResponse = transactionService.updateTransaction(id, entity, authentication.getName());
        return ResponseEntity.ok(transactionResponse);
    }

    @Operation(summary = "Delete an Income/Expense transaction by id")
    @DeleteMapping("{id}")
    public ResponseEntity<BaseResponse> deleteMethodName(@PathVariable Long id, Authentication authentication) {
        transactionService.deleteTransaction(id, authentication.getName());
        return ResponseEntity.ok(BaseResponse.builder()
                .responseMessage("Gelir/Gider başarılı bir şekilde silindi")
                .build());
    }

    @Operation(summary = "Get all transaction types. You can also filter by category")
    @GetMapping("transaction-types")
    public ResponseEntity<List<TransactionTypeResponse>> getTransactionTypes(
            @RequestParam(required = false) TransactionCategory category) {

        return ResponseEntity.ok(transactionTypeService.getAllTransactionTypes(category));
    }

}
