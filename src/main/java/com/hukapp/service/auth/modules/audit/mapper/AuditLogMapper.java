package com.hukapp.service.auth.modules.audit.mapper;

import com.hukapp.service.auth.modules.audit.dto.request.AuditLogCreateRequest;
import com.hukapp.service.auth.modules.audit.dto.response.AuditLogResponse;
import com.hukapp.service.auth.modules.audit.entity.AuditLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * MapStruct mapper for AuditLog entity and DTOs
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AuditLogMapper {

    /**
     * Convert AuditLogCreateRequest to AuditLog entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    AuditLog toEntity(AuditLogCreateRequest request);

    /**
     * Convert AuditLog entity to AuditLogResponse
     */
    AuditLogResponse toResponse(AuditLog auditLog);

    /**
     * Convert list of AuditLog entities to list of AuditLogResponse
     */
    List<AuditLogResponse> toResponseList(List<AuditLog> auditLogs);
}
