package com.hukapp.service.auth.modules.coupon.dto.response;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

import com.hukapp.service.auth.modules.coupon.enums.DiscountType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for coupon response
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response DTO for coupon operations")
public class CouponResponseDto {

    @Schema(description = "Coupon ID", example = "1")
    private Long id;

    @Schema(description = "Unique coupon code", example = "SUMMER2024")
    private String code;

    @Schema(description = "Description of the coupon", example = "Summer discount coupon")
    private String description;

    @Schema(description = "Type of discount", example = "PERCENTAGE")
    private DiscountType discountType;

    @Schema(description = "Discount value", example = "10.00")
    private BigDecimal discountValue;

    @Schema(description = "Maximum usage limit", example = "100")
    private Integer usageLimit;

    @Schema(description = "Current usage count", example = "25")
    private Integer currentUsageCount;

    @Schema(description = "Expiration date")
    private LocalDateTime expirationDate;

    @Schema(description = "Whether the coupon is active", example = "true")
    private Boolean active;

    @Schema(description = "Whether the coupon is valid for use", example = "true")
    private Boolean isValid;

    @Schema(description = "Whether the coupon has expired", example = "false")
    private Boolean isExpired;

    @Schema(description = "Whether usage limit has been exceeded", example = "false")
    private Boolean isUsageLimitExceeded;

    @Schema(description = "Creation timestamp")
    private Instant createdAt;

    @Schema(description = "Last update timestamp")
    private Instant updatedAt;

    @Schema(description = "Created by user")
    private String createdBy;

    @Schema(description = "Last updated by user")
    private String updatedBy;
}
