package com.hukapp.service.auth.modules.office.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.common.dto.response.BaseResponse;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingCreateRequest;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingResponse;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingUpdateRequest;
import com.hukapp.service.auth.modules.office.enums.AccountingType;
import com.hukapp.service.auth.modules.office.service.ClientAccountingService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Controller for client accounting operations
 * Subject to subscription-based access control (HTTP 402 for unauthorized users)
 */
@Slf4j
@RestController
@RequestMapping("/api/user/client-accounting")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Client Accounting", description = "API for managing client-specific accounting records (Alınanlar, Alınacaklar, Geri Ödemeler)")
public class ClientAccountingController {

    private final ClientAccountingService clientAccountingService;

    @PostMapping
    @Operation(
        summary = "Create client accounting record",
        description = "Creates a new accounting record for a client with specified type (RECEIVED, RECEIVABLE, REFUND)"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Accounting record created successfully",
            content = @Content(schema = @Schema(implementation = ClientAccountingResponse.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "402",
            description = "Subscription required"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Client not found"
        )
    })
    public ResponseEntity<ClientAccountingResponse> createClientAccounting(
            @Parameter(description = "Client accounting data", required = true)
            @Valid @RequestBody ClientAccountingCreateRequest request,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Creating client accounting record for client ID: {}", request.getClientId());
        ClientAccountingResponse response = clientAccountingService.createClientAccounting(request, authentication.getName());
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping
    @Operation(
        summary = "Get all client accounting records",
        description = "Retrieves all accounting records for the authenticated user, optionally filtered by accounting type"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Accounting records retrieved successfully",
            content = @Content(schema = @Schema(implementation = ClientAccountingResponse.class))
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "402",
            description = "Subscription required"
        )
    })
    public ResponseEntity<List<ClientAccountingResponse>> getAllClientAccountingRecords(
            @Parameter(description = "Filter by accounting type (RECEIVED, RECEIVABLE, REFUND)", required = false)
            @RequestParam(required = false) AccountingType accountingType,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Getting all client accounting records for user: {}", authentication.getName());
        
        List<ClientAccountingResponse> response;
        if (accountingType != null) {
            response = clientAccountingService.getClientAccountingRecordsByType(accountingType, authentication.getName());
        } else {
            response = clientAccountingService.getAllClientAccountingRecords(authentication.getName());
        }
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/client/{clientId}")
    @Operation(
        summary = "Get accounting records by client",
        description = "Retrieves all accounting records for a specific client, optionally filtered by accounting type"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Client accounting records retrieved successfully",
            content = @Content(schema = @Schema(implementation = ClientAccountingResponse.class))
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "402",
            description = "Subscription required"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Client not found"
        )
    })
    public ResponseEntity<List<ClientAccountingResponse>> getClientAccountingRecordsByClient(
            @Parameter(description = "Client ID", required = true)
            @PathVariable Long clientId,
            @Parameter(description = "Filter by accounting type (RECEIVED, RECEIVABLE, REFUND)", required = false)
            @RequestParam(required = false) AccountingType accountingType,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Getting client accounting records for client ID: {}", clientId);
        
        List<ClientAccountingResponse> response;
        if (accountingType != null) {
            response = clientAccountingService.getClientAccountingRecordsByClientAndType(clientId, accountingType, authentication.getName());
        } else {
            response = clientAccountingService.getClientAccountingRecordsByClient(clientId, authentication.getName());
        }
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get accounting record by ID",
        description = "Retrieves a specific accounting record by ID"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Accounting record retrieved successfully",
            content = @Content(schema = @Schema(implementation = ClientAccountingResponse.class))
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "402",
            description = "Subscription required"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Accounting record not found"
        )
    })
    public ResponseEntity<ClientAccountingResponse> getClientAccountingRecordById(
            @Parameter(description = "Accounting record ID", required = true)
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Getting client accounting record with ID: {}", id);
        ClientAccountingResponse response = clientAccountingService.getClientAccountingRecordById(id, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update accounting record",
        description = "Updates an existing accounting record"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Accounting record updated successfully",
            content = @Content(schema = @Schema(implementation = ClientAccountingResponse.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "402",
            description = "Subscription required"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Accounting record not found"
        )
    })
    public ResponseEntity<ClientAccountingResponse> updateClientAccounting(
            @Parameter(description = "Accounting record ID", required = true)
            @PathVariable Long id,
            @Parameter(description = "Updated accounting data", required = true)
            @Valid @RequestBody ClientAccountingUpdateRequest request,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Updating client accounting record with ID: {}", id);
        ClientAccountingResponse response = clientAccountingService.updateClientAccounting(id, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete accounting record",
        description = "Deletes an accounting record"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Accounting record deleted successfully"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "402",
            description = "Subscription required"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Accounting record not found"
        )
    })
    public ResponseEntity<BaseResponse> deleteClientAccounting(
            @Parameter(description = "Accounting record ID", required = true)
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Deleting client accounting record with ID: {}", id);
        clientAccountingService.deleteClientAccounting(id, authentication.getName());
        
        return ResponseEntity.ok(BaseResponse.builder()
                .responseMessage("Muhasebe kaydı başarıyla silindi")
                .build());
    }

    @GetMapping("/client/{clientId}/total")
    @Operation(
        summary = "Get total amount by client and type",
        description = "Calculates total amount for a specific client and accounting type"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Total amount calculated successfully"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        ),
        @ApiResponse(
            responseCode = "402",
            description = "Subscription required"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Client not found"
        )
    })
    public ResponseEntity<Double> getTotalAmountByClientAndType(
            @Parameter(description = "Client ID", required = true)
            @PathVariable Long clientId,
            @Parameter(description = "Accounting type (RECEIVED, RECEIVABLE, REFUND)", required = true)
            @RequestParam AccountingType accountingType,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Getting total amount for client ID: {} and type: {}", clientId, accountingType);
        Double totalAmount = clientAccountingService.getTotalAmountByClientAndType(clientId, accountingType, authentication.getName());
        return ResponseEntity.ok(totalAmount);
    }

    /**
     * Get all client accounting records by accounting type
     */
    @GetMapping("/by-type")
    @Operation(summary = "Get all client accounting records by type")
    public ResponseEntity<List<ClientAccountingResponse>> getAllClientAccountingRecordsByType(
            @Parameter(description = "Accounting type (RECEIVED, RECEIVABLE, REFUND)", required = true)
            @RequestParam AccountingType accountingType,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Getting all client accounting records by type: {}", accountingType);
        List<ClientAccountingResponse> response = clientAccountingService.getAllClientAccountingRecordsByType(accountingType, authentication.getName());
        return ResponseEntity.ok(response);
    }   
    

}
