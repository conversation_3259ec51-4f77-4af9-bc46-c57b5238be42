package com.hukapp.service.auth.modules.audit.aop;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hukapp.service.auth.modules.audit.config.AuditLoggingProperties;
import com.hukapp.service.auth.modules.audit.dto.request.AuditLogCreateRequest;
import com.hukapp.service.auth.modules.audit.service.AuditLogService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.Instant;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Context object to hold audit information and reduce method parameters
 */
record AuditContext(
    Instant requestTimestamp,
    Instant responseTimestamp,
    long processingTime,
    String userEmail,
    String userRoles,
    Integer responseStatusCode,
    String errorMessage
) {}


/**
 * AOP Aspect for logging user activity on all API endpoints
 * Uses @Around advice to capture comprehensive request/response information
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class UserActivityLoggingAspect {

    private final AuditLogService auditLogService;
    private final AuditLoggingProperties auditLoggingProperties;
    private final ObjectMapper objectMapper;

    /**
     * Pointcut for all controller methods in the API
     * Excludes admin endpoints and auth endpoints if configured
     */
    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *) && " +
              "execution(* com.hukapp.service.auth.modules.*.controller.*.*(..))")
    public void controllerMethods() {}

    /**
     * Around advice that captures all API operations
     */
    @Around("controllerMethods()")
    public Object logUserActivity(ProceedingJoinPoint joinPoint) throws Throwable {
        // Check if audit logging is enabled
        if (!auditLoggingProperties.isEnabled()) {
            return joinPoint.proceed();
        }

        HttpServletRequest request = getCurrentHttpRequest();
        if (request == null) {
            return joinPoint.proceed();
        }

        String requestUri = request.getRequestURI();
        
        // Check if this endpoint should be excluded
        if (shouldExcludeEndpoint(requestUri)) {
            return joinPoint.proceed();
        }

        Instant requestTimestamp = Instant.now();
        long startTime = System.currentTimeMillis();
        
        // Extract user information
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String userEmail = null;
        String userRoles = null;
        
        if (authentication != null && authentication.isAuthenticated() && 
            !"anonymousUser".equals(authentication.getName())) {
            userEmail = authentication.getName();
            userRoles = extractUserRoles(authentication);
        }

        Object result = null;
        Integer responseStatusCode = 200;
        String errorMessage = null;
        
        try {
            result = joinPoint.proceed();
            
            // Extract status code from ResponseEntity if available
            if (result instanceof ResponseEntity) {
                ResponseEntity<?> responseEntity = (ResponseEntity<?>) result;
                responseStatusCode = responseEntity.getStatusCode().value();
            }
            
            return result;
        } catch (Exception e) {
            responseStatusCode = 500;

            // if the exception under the package "com.hukapp.service.auth" then set response status again
            if (e.getClass().getPackage().getName().startsWith("com.hukapp.service.auth")) {
                responseStatusCode = 299;
            }

            errorMessage = e.getMessage();
            throw e;
        } finally {
            try {
                Instant responseTimestamp = Instant.now();
                long processingTime = System.currentTimeMillis() - startTime;
                
                // Only log if user is authenticated (skip anonymous requests)
                if (userEmail != null) {
                    AuditContext auditContext = new AuditContext(
                        requestTimestamp, responseTimestamp, processingTime,
                        userEmail, userRoles, responseStatusCode, errorMessage
                    );
                    logAuditEntry(request, auditContext, result);
                }
            } catch (Exception e) {
                // Log the error but don't affect the main operation
                log.error("Failed to log audit entry: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Create and save audit log entry asynchronously
     */
    private void logAuditEntry(HttpServletRequest request, AuditContext auditContext, Object result) {
        try {
            AuditLogCreateRequest auditRequest = AuditLogCreateRequest.builder()
                    .endpointUrl(request.getRequestURI())
                    .httpMethod(request.getMethod())
                    .queryParameters(extractQueryParameters(request))
                    .pathParameters(extractPathParameters(request))
                    .responseStatusCode(auditContext.responseStatusCode())
                    .processingTimeMs(auditContext.processingTime())
                    .userEmail(auditContext.userEmail())
                    .userRoles(auditContext.userRoles())
                    .clientIpAddress(extractClientIpAddress(request))
                    .userAgent(request.getHeader("User-Agent"))
                    .requestTimestamp(auditContext.requestTimestamp())
                    .responseTimestamp(auditContext.responseTimestamp())
                    .requestHeaders(extractRequestHeaders(request))
                    .requestBodySize(getContentLength(request))
                    .responseBodySize(calculateResponseSize(result))
                    .sessionId(request.getSession(false) != null ? request.getSession().getId() : null)
                    .errorMessage(auditContext.errorMessage())
                    .build();

            auditLogService.saveAuditLogAsync(auditRequest);
        } catch (Exception e) {
            log.error("Failed to create audit log request: {}", e.getMessage(), e);
        }
    }

    /**
     * Extract user roles from authentication
     */
    private String extractUserRoles(Authentication authentication) {
        try {
            List<String> roles = authentication.getAuthorities().stream()
                    .map(GrantedAuthority::getAuthority)
                    .toList();
            return objectMapper.writeValueAsString(roles);
        } catch (Exception e) {
            log.warn("Failed to extract user roles: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Extract query parameters as JSON
     */
    private String extractQueryParameters(HttpServletRequest request) {
        try {
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (parameterMap.isEmpty()) {
                return null;
            }
            
            Map<String, Object> params = new HashMap<>();
            parameterMap.forEach((key, values) -> {
                if (values.length == 1) {
                    params.put(key, values[0]);
                } else {
                    params.put(key, values);
                }
            });
            
            return objectMapper.writeValueAsString(params);
        } catch (Exception e) {
            log.warn("Failed to extract query parameters: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Extract path parameters (this is a simplified version)
     */
    private String extractPathParameters(HttpServletRequest request) {
        try {
            // Path parameters are typically handled by Spring MVC
            // This is a placeholder for more sophisticated path parameter extraction
            String pathInfo = request.getPathInfo();
            if (pathInfo != null && !pathInfo.isEmpty()) {
                Map<String, String> pathParams = new HashMap<>();
                pathParams.put("pathInfo", pathInfo);
                return objectMapper.writeValueAsString(pathParams);
            }
            return null;
        } catch (Exception e) {
            log.warn("Failed to extract path parameters: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Extract client IP address considering proxy headers
     */
    private String extractClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * Extract request headers as JSON
     */
    private String extractRequestHeaders(HttpServletRequest request) {
        try {
            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                // Skip sensitive headers
                if (!isSensitiveHeader(headerName)) {
                    headers.put(headerName, request.getHeader(headerName));
                }
            }
            
            return headers.isEmpty() ? null : objectMapper.writeValueAsString(headers);
        } catch (Exception e) {
            log.warn("Failed to extract request headers: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Check if header contains sensitive information
     */
    private boolean isSensitiveHeader(String headerName) {
        String lowerCaseName = headerName.toLowerCase();
        return lowerCaseName.contains("authorization") || 
               lowerCaseName.contains("cookie") ||
               lowerCaseName.contains("password") ||
               lowerCaseName.contains("token");
    }

    /**
     * Get content length from request
     */
    private Long getContentLength(HttpServletRequest request) {
        int contentLength = request.getContentLength();
        return contentLength > 0 ? (long) contentLength : null;
    }

    /**
     * Calculate response size (simplified)
     */
    private Long calculateResponseSize(Object result) {
        try {
            if (result != null) {
                String jsonResult = objectMapper.writeValueAsString(result);
                return (long) jsonResult.length();
            }
        } catch (Exception e) {
            log.debug("Failed to calculate response size: {}", e.getMessage());
        }
        return null;
    }

    /**
     * Check if endpoint should be excluded from logging
     */
    private boolean shouldExcludeEndpoint(String requestUri) {
        return auditLoggingProperties.getExcludedEndpoints().stream()
                .anyMatch(requestUri::startsWith);
    }

    /**
     * Get current HTTP request
     */
    private HttpServletRequest getCurrentHttpRequest() {
        try {
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            log.debug("No HTTP request context available: {}", e.getMessage());
            return null;
        }
    }
}
