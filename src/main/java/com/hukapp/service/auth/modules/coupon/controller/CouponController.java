package com.hukapp.service.auth.modules.coupon.controller;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.coupon.dto.request.CreateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.request.UpdateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.response.CouponResponseDto;
import com.hukapp.service.auth.modules.coupon.service.CouponService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * REST controller for coupon management operations
 */
@RestController
@RequestMapping("api/admin/coupons")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "Admin Coupon Management", description = "CRUD operations for discount coupons")
public class CouponController {

    private final CouponService couponService;

    @Operation(
        summary = "Create new coupon",
        description = "Create a new discount coupon with specified parameters"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Coupon created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "409", description = "Coupon code already exists")
    })
    @PostMapping
    public ResponseEntity<CouponResponseDto> createCoupon(
            @Parameter(description = "Coupon creation data", required = true)
            @Valid @RequestBody CreateCouponDto createDto) {

        log.debug("Creating coupon with code: {}", createDto.getCode());
        CouponResponseDto response = couponService.createCoupon(createDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(
        summary = "Get all coupons",
        description = "Retrieve all coupons with pagination support"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Coupons retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping
    public ResponseEntity<Page<CouponResponseDto>> getAllCoupons(
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") @Min(0) int page,
            
            @Parameter(description = "Page size", example = "20")
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size,
            
            @Parameter(description = "Sort field", example = "createdAt")
            @RequestParam(defaultValue = "createdAt") String sortBy,
            
            @Parameter(description = "Sort direction", example = "desc")
            @RequestParam(defaultValue = "desc") String sortDir,
            
            @Parameter(description = "Filter by active status")
            @RequestParam(required = false) Boolean active,
            
            @Parameter(description = "Search by coupon code")
            @RequestParam(required = false) String search) {

        log.debug("Getting coupons - page: {}, size: {}, active: {}, search: {}", page, size, active, search);

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<CouponResponseDto> coupons;
        if (search != null && !search.trim().isEmpty()) {
            coupons = couponService.searchCouponsByCode(search.trim(), pageable);
        } else if (active != null && active) {
            coupons = couponService.getActiveCoupons(pageable);
        } else {
            coupons = couponService.getAllCoupons(pageable);
        }

        return ResponseEntity.ok(coupons);
    }

    @Operation(
        summary = "Get coupon by ID",
        description = "Retrieve a specific coupon by its ID"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Coupon retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Coupon not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<CouponResponseDto> getCouponById(
            @Parameter(description = "Coupon ID", required = true, example = "1")
            @PathVariable Long id) {

        log.debug("Getting coupon by ID: {}", id);
        CouponResponseDto coupon = couponService.getCouponById(id);
        return ResponseEntity.ok(coupon);
    }

    @Operation(
        summary = "Get coupon by code",
        description = "Retrieve a specific coupon by its code"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Coupon retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Coupon not found")
    })
    @GetMapping("/code/{code}")
    public ResponseEntity<CouponResponseDto> getCouponByCode(
            @Parameter(description = "Coupon code", required = true, example = "SUMMER2024")
            @PathVariable String code) {

        log.debug("Getting coupon by code: {}", code);
        CouponResponseDto coupon = couponService.getCouponByCode(code);
        return ResponseEntity.ok(coupon);
    }

    @Operation(
        summary = "Update coupon",
        description = "Update an existing coupon's properties"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Coupon updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Coupon not found")
    })
    @PutMapping("/{id}")
    public ResponseEntity<CouponResponseDto> updateCoupon(
            @Parameter(description = "Coupon ID", required = true, example = "1")
            @PathVariable Long id,
            
            @Parameter(description = "Coupon update data", required = true)
            @Valid @RequestBody UpdateCouponDto updateDto) {

        log.debug("Updating coupon with ID: {}", id);
        CouponResponseDto response = couponService.updateCoupon(id, updateDto);
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Delete/deactivate coupon",
        description = "Deactivate a coupon (soft delete)"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Coupon deactivated successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Coupon not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCoupon(
            @Parameter(description = "Coupon ID", required = true, example = "1")
            @PathVariable Long id) {

        log.debug("Deleting coupon with ID: {}", id);
        couponService.deleteCoupon(id);
        return ResponseEntity.noContent().build();
    }
}
