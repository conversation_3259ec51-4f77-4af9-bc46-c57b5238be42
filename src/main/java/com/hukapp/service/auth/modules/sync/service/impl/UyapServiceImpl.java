package com.hukapp.service.auth.modules.sync.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDate;
import java.util.List;

import org.apache.commons.lang3.ThreadUtils;
import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.dto.response.BaseResponse;
import com.hukapp.service.auth.common.exception.custom.AuthException;
import com.hukapp.service.auth.common.util.UyapUtil;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.sync.dto.request.uyap.UyapSearchCaseDetailRequest;
import com.hukapp.service.auth.modules.sync.dto.request.uyap.UyapSearchCaseRequest;
import com.hukapp.service.auth.modules.sync.dto.request.uyap.UyapSearchTrialsRequest;
import com.hukapp.service.auth.modules.sync.dto.request.uyap.UyapSearchYargiTuruRequest;
import com.hukapp.service.auth.modules.sync.entity.NoSqlLikeData;
import com.hukapp.service.auth.modules.sync.entity.NoSqlLikeDataCaseDetails;
import com.hukapp.service.auth.modules.sync.enums.DataSourceEnum;
import com.hukapp.service.auth.modules.sync.enums.YargiTuruEnum;
import com.hukapp.service.auth.modules.sync.service.NoSqlLikeDataService;
import com.hukapp.service.auth.modules.sync.service.UyapService;

import kong.unirest.core.HttpResponse;
import kong.unirest.core.JsonNode;
import kong.unirest.core.json.JSONArray;
import kong.unirest.core.json.JSONObject;

@Service
@Slf4j
@AllArgsConstructor
public class UyapServiceImpl implements UyapService {

    private final NoSqlLikeDataService noSqlLikeDataService;
    private final PersonService personService;
    private static final long WAIT_TIME_BETWEEN_REQUESTS = 200;

    @Override
    public BaseResponse syncWithUyap(String subject, String jsid) {

        /* checking UYAP connectivity, if it is not successful, throw an exception. */
        checkUyapConnectivity(subject, jsid);

        log.debug("Started to sync for user '{}'", subject);

        // ===========================================================================
        /* syncing no-request-body URLs. */
        syncUrlsWithNoRequestBody(subject, jsid);
        syncUserPhoto(subject, jsid);

        /* syncing URLs which requires request body. */
        syncTrials(subject, jsid);
        syncUserCasesv2(subject, jsid, 0);
        syncUserCasesv2(subject, jsid, 1);
        // ===========================================================================

        log.debug("Finished syncing for user '{}'", subject);

        // Update isNewUser to false after successful sync
        updateUserAsNotNew(subject);

        return BaseResponse.builder()
                .responseMessage("Senkronizasyon işlemi tamamlandı. Kullanıcı: " + getUserNameAndSurname(jsid)).build();
    }

    private void syncUserCasesv2(String subject, String jsid, int dosyaDurumKod) {

        List<YargiTuruEnum> yargiTuruEnums = YargiTuruEnum.getAllEnumValuesExceptCBS();

        DataSourceEnum dataSource = dosyaDurumKod == 0 ? DataSourceEnum.UYAP_ACTIVE_CASE_SEARCH
                : DataSourceEnum.UYAP_CLOSED_CASE_SEARCH;

        boolean isClosedCase = dosyaDurumKod != 0;

        JSONArray cases = new JSONArray();
        for (YargiTuruEnum yargiTuruEnum : yargiTuruEnums) {

            HttpResponse<JsonNode> yargiBirimleriResponse = UyapUtil.callUyap(subject, jsid,
                    DataSourceEnum.UYAP_YARGI_BIRIMLERI_SORGULA,
                    UyapSearchYargiTuruRequest.builder().yargiTuru(yargiTuruEnum.yargiTuru()).build());

            JsonNode yargiBirimleri = yargiBirimleriResponse.getBody();

            for (int i = 0; i < yargiBirimleri.getArray().length(); i++) {

                JSONObject yargiBirimi = yargiBirimleri.getArray().getJSONObject(i);
                String birimTuru2 = yargiBirimi.get("tablo").toString();
                String birimTuru3 = yargiBirimi.get("kod").toString();

                HttpResponse<JsonNode> casesForDosyaDurumKodResponse = UyapUtil.callUyap(subject, jsid,
                        DataSourceEnum.UYAP_CASE_SEACRH_BY_YARGI_BIRIMI_AND_TURU,
                        UyapSearchCaseRequest.caseSearchRequestByYargiBirimiAndTuru(birimTuru3, birimTuru2,
                                dosyaDurumKod));

                JsonNode casesForDosyaDurumKodResponseBody = casesForDosyaDurumKodResponse.getBody();

                for (int j = 0; j < casesForDosyaDurumKodResponseBody.getArray().getJSONArray(0).length(); j++) {

                    JSONObject caseInfo = casesForDosyaDurumKodResponseBody.getArray().getJSONArray(0).getJSONObject(j);
                    String caseNumber = caseInfo.get("dosyaNo").toString();
                    String caseId = caseInfo.get("dosyaId").toString();

                    log.debug("Case id   : {}", caseId);
                    log.debug("Case info : {}", caseNumber);
                    log.debug("Started to sync case details for case number: {}", caseInfo.getString("dosyaNo"));

                    HttpResponse<JsonNode> caseHistoryResponse = UyapUtil.callUyap(subject, jsid,
                            DataSourceEnum.UYAP_CASE_HISTORY,
                            new UyapSearchCaseDetailRequest(caseId));

                    saveToNoSqlLikeDataCaseDetails(subject, DataSourceEnum.UYAP_CASE_HISTORY, caseNumber,
                            caseHistoryResponse, isClosedCase);

                    HttpResponse<JsonNode> caseTahsilatReddiyatResponse = UyapUtil.callUyap(subject, jsid,
                            DataSourceEnum.UYAP_CASE_TAHSILAT_REDDIYAT,
                            new UyapSearchCaseDetailRequest(caseId));

                    saveToNoSqlLikeDataCaseDetails(subject, DataSourceEnum.UYAP_CASE_TAHSILAT_REDDIYAT, caseNumber,
                            caseTahsilatReddiyatResponse, isClosedCase);

                    HttpResponse<JsonNode> caseTaraflarResponse = UyapUtil.callUyap(subject, jsid,
                            DataSourceEnum.UYAP_CASE_TARAFLAR,
                            new UyapSearchCaseDetailRequest(caseId));

                    saveToNoSqlLikeDataCaseDetails(subject, DataSourceEnum.UYAP_CASE_TARAFLAR, caseNumber,
                            caseTaraflarResponse, isClosedCase);

                    ThreadUtils.sleepQuietly(Duration.ofMillis(WAIT_TIME_BETWEEN_REQUESTS));

                    cases.put(caseInfo);
                }
            }
        }
        JSONArray casesForDosyaDurumKod = new JSONArray();
        casesForDosyaDurumKod.put(cases);
        casesForDosyaDurumKod.put(cases.length());
        noSqlLikeDataService.saveOrUpdate(new NoSqlLikeData(subject, dataSource, casesForDosyaDurumKod.toString()));
    }

    private void syncTrials(String subject, String jsid) {

        DataSourceEnum dataSource = DataSourceEnum.UYAP_TRIAL_SEARCH;

        UyapSearchTrialsRequest request = new UyapSearchTrialsRequest.Builder()
                .baslangicTarihi(LocalDate.now())
                .bitisTarihi(LocalDate.now().plusDays(30))
                .build();

        HttpResponse<JsonNode> response = UyapUtil.callUyap(subject, jsid, dataSource, request);

        saveToNoSqlLikeData(subject, dataSource, response);
    }

    private void syncUrlsWithNoRequestBody(String subject, String jsid) {
        for (DataSourceEnum dataSource : DataSourceEnum.getNoRequestBodyEnums()) {

            log.debug("Started to sync {} for user '{}'", dataSource, subject);

            HttpResponse<JsonNode> response = UyapUtil.callUyap(subject, jsid, dataSource, null);

            saveToNoSqlLikeData(subject, dataSource, response);
        }
    }

    private void syncUserPhoto(String subject, String jsid) {
        /**
         * Fotograf direk string olarak geldigi icin syncUrlsWithNoRequestBody
         * metodundan ayrildi.
         * 
         * @param subject
         * @param jsid
         */

        DataSourceEnum dataSource = DataSourceEnum.UYAP_USER_PHOTO;

        HttpResponse<String> response = UyapUtil
                .createUyapHttpPostRequest(jsid, dataSource.url())
                .asString();

        saveToNoSqlLikeData(subject, dataSource, response);
    }

    @Override
    public void checkUyapConnectivity(String subject, String jsid) {

        log.debug("Checking Uyap connectivity for user '{}'", subject);

        HttpResponse<JsonNode> response = UyapUtil.callUyap(subject, jsid, DataSourceEnum.UYAP_USER_DETAILS, null);

        if (!response.isSuccess() || !response.getBody().getObject().has("tcKimlikNo")) {
            throw new AuthException("UYAP entegrasyon basarisiz. Email: " + subject);
        }

        log.debug("Uyap connectivity check is successful for user '{}'", subject);

    }

    @Override
    public String getUserNameAndSurname(String jsid) {
        HttpResponse<JsonNode> response = UyapUtil.callUyap(null, jsid, DataSourceEnum.UYAP_USER_DETAILS, null);

        return response.getBody().getObject().getString("adi") + " "
                + response.getBody().getObject().getString("soyadi");
    }

    private <T> void saveToNoSqlLikeData(String subject, DataSourceEnum dataSource, HttpResponse<T> response) {
        log.debug("Response status: {} isSuccess {}", response.getStatus(), response.isSuccess());
        if (response.isSuccess() || response.getStatus() == 200) {
            log.debug("Sync is successful for {} for user '{}'", dataSource, subject);
            log.debug("Response: {}", response.getBody());
            noSqlLikeDataService.saveOrUpdate(new NoSqlLikeData(subject, dataSource, response.getBody().toString()));
        } else {
            log.error("Sync is failed for {} for user '{}'", dataSource, subject);
            log.debug("Response: {}", response.getBody());
        }
    }

    private void saveToNoSqlLikeDataCaseDetails(String subject, DataSourceEnum dataSource, String caseNumber,
            HttpResponse<JsonNode> response, boolean isClosedCase) {
        if (response.isSuccess()) {
            log.debug("Sync is successful for {} for user '{}'", dataSource, subject);
            log.debug("Response: {}", response.getBody());
            noSqlLikeDataService.saveOrUpdate(new NoSqlLikeDataCaseDetails(subject, dataSource, caseNumber,
                    response.getBody().toString(),isClosedCase));
        } else {
            log.error("Sync is failed for {} for user '{}'", dataSource, subject);
            log.debug("Response: {}", response.getBody());
        }
    }

    /**
     * Updates the user's isNewUser field to false after successful sync
     * 
     * @param email The email of the user to update
     */
    private void updateUserAsNotNew(String email) {
        try {
            Person person = personService.getPersonByEmailOrElseThrow(email);
            if (person.isNewUser()) {
                log.debug("Updating isNewUser to false for user '{}'", email);
                person.setNewUser(false);
                personService.updatePerson(person);
                log.debug("Successfully updated isNewUser to false for user '{}'", email);
            } else {
                log.debug("User '{}' is already marked as not new", email);
            }
        } catch (Exception e) {
            log.error("Failed to update isNewUser for user '{}': {}", email, e.getMessage());
        }
    }
}
