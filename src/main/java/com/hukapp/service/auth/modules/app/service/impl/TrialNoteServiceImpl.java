package com.hukapp.service.auth.modules.app.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.app.dto.request.TrialNoteCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.TrialNoteUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.TrialNoteResponseDto;
import com.hukapp.service.auth.modules.app.entity.TrialNote;
import com.hukapp.service.auth.modules.app.mapper.TrialNoteMapper;
import com.hukapp.service.auth.modules.app.repository.TrialNoteRepository;
import com.hukapp.service.auth.modules.app.service.TrialNoteService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TrialNoteServiceImpl implements TrialNoteService {

    private static final String TRIAL_NOTE_NOT_FOUND_MESSAGE = "Trial note not found with ID: ";

    private final TrialNoteRepository trialNoteRepository;
    private final PersonService personService;
    private final TrialNoteMapper trialNoteMapper;

    @Override
    public TrialNoteResponseDto createTrialNote(TrialNoteCreateDto trialNoteCreateDto, String userEmail) {
        log.debug("Creating trial note for case number: {} by user: {}", trialNoteCreateDto.getCaseNumber(), userEmail);
        
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Create and save the trial note
        TrialNote trialNote = trialNoteMapper.toEntity(trialNoteCreateDto);
        trialNote.setOwner(owner);
        
        trialNote = trialNoteRepository.save(trialNote);
        log.debug("Created trial note with ID: {} for case number: {}", trialNote.getId(), trialNote.getCaseNumber());
        
        return trialNoteMapper.toResponseDto(trialNote);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TrialNoteResponseDto> getTrialNotesByCaseNumber(String caseNumber, String userEmail) {
        log.debug("Retrieving trial notes for case number: {} by user: {}", caseNumber, userEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        List<TrialNote> notes = trialNoteRepository.findByCaseNumberAndOwner(caseNumber, owner);
        return notes.stream()
                .map(trialNoteMapper::toResponseDto)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<TrialNoteResponseDto> getAllTrialNotes(String userEmail) {
        log.debug("Retrieving all trial notes for user: {}", userEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        List<TrialNote> notes = trialNoteRepository.findByOwner(owner);
        return notes.stream()
                .map(trialNoteMapper::toResponseDto)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public TrialNoteResponseDto getTrialNoteById(Long id, String userEmail) {
        log.debug("Retrieving trial note with ID: {} for user: {}", id, userEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        TrialNote trialNote = trialNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(TRIAL_NOTE_NOT_FOUND_MESSAGE + id));
        
        return trialNoteMapper.toResponseDto(trialNote);
    }

    @Override
    public TrialNoteResponseDto updateTrialNote(Long id, TrialNoteUpdateDto trialNoteUpdateDto, String userEmail) {
        log.debug("Updating trial note with ID: {} for user: {}", id, userEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Find the existing trial note
        TrialNote existingNote = trialNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(TRIAL_NOTE_NOT_FOUND_MESSAGE + id));
        
        // Update the trial note using mapper
        trialNoteMapper.updateEntityFromDto(trialNoteUpdateDto, existingNote);
        
        existingNote = trialNoteRepository.save(existingNote);
        log.debug("Updated trial note with ID: {}", existingNote.getId());
        
        return trialNoteMapper.toResponseDto(existingNote);
    }

    @Override
    public void deleteTrialNote(Long id, String userEmail) {
        log.debug("Deleting trial note with ID: {} for user: {}", id, userEmail);
        
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        TrialNote trialNote = trialNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(TRIAL_NOTE_NOT_FOUND_MESSAGE + id));
        
        trialNoteRepository.delete(trialNote);
        log.debug("Deleted trial note with ID: {}", id);
    }
    
}
