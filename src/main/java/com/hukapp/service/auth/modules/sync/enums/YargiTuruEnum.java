package com.hukapp.service.auth.modules.sync.enums;

import java.util.Arrays;
import java.util.List;

public enum YargiTuruEnum {

    CEZA("0"),
    HUKUK("1"),
    ICRA("2"),
    IDARI_YARGI("6"),
    SATIS_MEMURLUGU("11"),
    ARABULUCULUK("25"),
    CBS("3");

    private final String yargiTuru;

    YargiTuruEnum(String yargiTuru) {
        this.yargiTuru = yargiTuru;
    }

    public String yargiTuru() {
        return yargiTuru;
    }

    public static List<YargiTuruEnum> getAllEnumValuesExceptCBS() {
        return Arrays.stream(YargiTuruEnum.values())
                .filter(t -> !t.yargiTuru().equals(CBS.yargiTuru()))
                .toList();
    }

}
