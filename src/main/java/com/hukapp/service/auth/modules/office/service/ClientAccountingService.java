package com.hukapp.service.auth.modules.office.service;

import java.util.List;

import com.hukapp.service.auth.modules.office.dto.ClientAccountingCreateRequest;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingResponse;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingUpdateRequest;
import com.hukapp.service.auth.modules.office.enums.AccountingType;

public interface ClientAccountingService {

    /**
     * Create a new client accounting record
     * @param request the create request
     * @param ownerEmail the owner's email
     * @return created client accounting response
     */
    ClientAccountingResponse createClientAccounting(ClientAccountingCreateRequest request, String ownerEmail);

    /**
     * Get all client accounting records for the owner
     * @param ownerEmail the owner's email
     * @return list of client accounting responses
     */
    List<ClientAccountingResponse> getAllClientAccountingRecords(String ownerEmail);

    /**
     * Get all client accounting records by accounting type
     * @param accountingType the accounting type
     * @param ownerEmail the owner's email
     * @return list of client accounting responses
     */
    List<ClientAccountingResponse> getAllClientAccountingRecordsByType(AccountingType accountingType, String ownerEmail);

    /**
     * Get client accounting records by client ID
     * @param clientId the client ID
     * @param ownerEmail the owner's email
     * @return list of client accounting responses
     */
    List<ClientAccountingResponse> getClientAccountingRecordsByClient(Long clientId, String ownerEmail);

    /**
     * Get client accounting records by accounting type
     * @param accountingType the accounting type
     * @param ownerEmail the owner's email
     * @return list of client accounting responses
     */
    List<ClientAccountingResponse> getClientAccountingRecordsByType(AccountingType accountingType, String ownerEmail);

    /**
     * Get client accounting records by client ID and accounting type
     * @param clientId the client ID
     * @param accountingType the accounting type
     * @param ownerEmail the owner's email
     * @return list of client accounting responses
     */
    List<ClientAccountingResponse> getClientAccountingRecordsByClientAndType(Long clientId, AccountingType accountingType, String ownerEmail);

    /**
     * Get client accounting record by ID
     * @param id the record ID
     * @param ownerEmail the owner's email
     * @return client accounting response
     */
    ClientAccountingResponse getClientAccountingRecordById(Long id, String ownerEmail);

    /**
     * Update client accounting record
     * @param id the record ID
     * @param request the update request
     * @param ownerEmail the owner's email
     * @return updated client accounting response
     */
    ClientAccountingResponse updateClientAccounting(Long id, ClientAccountingUpdateRequest request, String ownerEmail);

    /**
     * Delete client accounting record
     * @param id the record ID
     * @param ownerEmail the owner's email
     */
    void deleteClientAccounting(Long id, String ownerEmail);

    /**
     * Get total amount by client and accounting type
     * @param clientId the client ID
     * @param accountingType the accounting type
     * @param ownerEmail the owner's email
     * @return total amount
     */
    Double getTotalAmountByClientAndType(Long clientId, AccountingType accountingType, String ownerEmail);

}
