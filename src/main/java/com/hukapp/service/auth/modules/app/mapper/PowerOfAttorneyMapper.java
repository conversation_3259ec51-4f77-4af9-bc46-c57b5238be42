package com.hukapp.service.auth.modules.app.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.PowerOfAttorneyResponse;
import com.hukapp.service.auth.modules.office.dto.response.ClientResponseDto;
import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorney;
import com.hukapp.service.auth.modules.person.entity.Person;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true),
    imports = {Person.class}
)
public interface PowerOfAttorneyMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "clientList", ignore = true)
    PowerOfAttorney toEntity(PowerOfAttorneyCreateDto powerOfAttorneyCreateDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "clientList", ignore = true)
    PowerOfAttorney toEntity(PowerOfAttorneyUpdateDto powerOfAttorneyUpdateDto);

    @Mapping(target = "ownerName", expression = "java(powerOfAttorney.getOwner().getName() + ' ' + powerOfAttorney.getOwner().getSurname())")
    @Mapping(target = "ownerEmail", expression = "java(powerOfAttorney.getOwner().getEmail())")
    @Mapping(target = "ownerId", expression = "java(powerOfAttorney.getOwner().getId())")
    @Mapping(target = "clients", source = "clientList")
    PowerOfAttorneyResponse toDTO(PowerOfAttorney powerOfAttorney);

    // Helper method to map Client to ClientResponseDto
    default ClientResponseDto mapClientToResponseDto(Client client) {
        if (client == null) {
            return null;
        }
        return ClientResponseDto.builder()
                .id(client.getId())
                .name(client.getName())
                .identityOrTaxNumber(client.getIdentityOrTaxNumber())
                .address(client.getAddress())
                .phoneNumber(client.getPhoneNumber())
                .email(client.getEmail())
                .clientType(client.getClientType())
                .build();
    }
}
