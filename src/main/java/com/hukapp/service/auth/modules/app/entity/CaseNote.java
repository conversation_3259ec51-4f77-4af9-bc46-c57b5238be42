package com.hukapp.service.auth.modules.app.entity;

import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Table(name = "case_notes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class CaseNote extends BaseEntity {

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String content;

    @Column(nullable = false)
    private String caseNumber;

    @ManyToOne
    @JoinColumn(name = "owner_id", nullable = false)
    private Person owner;
}
