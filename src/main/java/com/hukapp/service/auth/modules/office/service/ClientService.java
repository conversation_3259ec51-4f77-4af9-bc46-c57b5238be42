package com.hukapp.service.auth.modules.office.service;

import java.util.List;

import com.hukapp.service.auth.modules.office.dto.request.ClientCreateDto;
import com.hukapp.service.auth.modules.office.dto.request.ClientUpdateDto;
import com.hukapp.service.auth.modules.office.dto.response.ClientResponseDto;

/**
 * Service interface for Client operations
 */
public interface ClientService {

    /**
     * Create a new client
     * @param createDto the client creation data
     * @param ownerEmail the email of the owner (authenticated user)
     * @return the created client response
     */
    ClientResponseDto createClient(ClientCreateDto createDto, String ownerEmail);

    /**
     * Get a client by ID for the authenticated user
     * @param id the client ID
     * @param ownerEmail the email of the owner (authenticated user)
     * @return the client response
     */
    ClientResponseDto getClientById(Long id, String ownerEmail);

    /**
     * Get all clients for the authenticated user
     * @param ownerEmail the email of the owner (authenticated user)
     * @return list of client responses
     */
    List<ClientResponseDto> getAllClients(String ownerEmail);

    /**
     * Update an existing client
     * @param id the client ID
     * @param updateDto the client update data
     * @param ownerEmail the email of the owner (authenticated user)
     * @return the updated client response
     */
    ClientResponseDto updateClient(Long id, ClientUpdateDto updateDto, String ownerEmail);

    /**
     * Delete a client (soft delete)
     * @param id the client ID
     * @param ownerEmail the email of the owner (authenticated user)
     */
    void deleteClient(Long id, String ownerEmail);

}
