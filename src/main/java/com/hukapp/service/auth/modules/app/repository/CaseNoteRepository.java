package com.hukapp.service.auth.modules.app.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.app.entity.CaseNote;
import com.hukapp.service.auth.modules.person.entity.Person;

@Repository
public interface CaseNoteRepository extends JpaRepository<CaseNote, Long> {
    
    List<CaseNote> findByCaseNumberAndOwner(String caseNumber, Person owner);
    
    List<CaseNote> findByOwner(Person owner);
    
    Optional<CaseNote> findByIdAndOwner(Long id, Person owner);
    
    Optional<CaseNote> findByIdAndCaseNumberAndOwner(Long id, String caseNumber, Person owner);
}
