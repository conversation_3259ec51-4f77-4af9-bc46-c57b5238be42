package com.hukapp.service.auth.modules.office.dto;

import com.hukapp.service.auth.modules.office.enums.TransactionCategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Transaction Type Create Or Update Response")
public class TransactionTypeResponse {

    private Long id;
    private String name;
    private TransactionCategory category;

}
