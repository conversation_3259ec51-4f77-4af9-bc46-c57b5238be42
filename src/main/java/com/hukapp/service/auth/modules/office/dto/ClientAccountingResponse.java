package com.hukapp.service.auth.modules.office.dto;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.modules.office.enums.AccountingType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Client Accounting Response")
public class ClientAccountingResponse {

    @Schema(description = "Record ID", example = "1")
    private Long id;

    @Schema(description = "Client ID", example = "1")
    private Long clientId;

    @Schema(description = "Client name", example = "Ahmet Yılmaz")
    private String clientName;

    @Schema(description = "Accounting type", example = "RECEIVED")
    private AccountingType accountingType;

    @Schema(description = "Amount", example = "1500.50")
    private BigDecimal amount;

    @Schema(description = "Description", example = "Dava ücreti ödemesi")
    private String description;

    @Schema(description = "Record date")
    private Instant recordDate;

    @Schema(description = "Case number", example = "2024/123")
    private String caseNumber;

    @Schema(description = "Created date")
    private Instant createdAt;

    @Schema(description = "Updated date")
    private Instant updatedAt;

    @Schema(description = "Owner name", example = "Av. Mehmet Demir")
    private String ownerName;

    @Schema(description = "Owner email", example = "<EMAIL>")
    private String ownerEmail;

}
