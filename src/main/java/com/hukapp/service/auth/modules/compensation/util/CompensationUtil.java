package com.hukapp.service.auth.modules.compensation.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

import org.springframework.stereotype.Component;

import com.hukapp.service.auth.common.exception.custom.UnexpectedStatusException;

@Component
public class CompensationUtil {

    private CompensationUtil() {
        // Private constructor to prevent instantiation
    }

    public static String formatBigDecimalToString(BigDecimal value) {

        // Create custom symbols: '.' for decimals and ',' for grouping
        DecimalFormatSymbols symbols = new DecimalFormatSymbols();
        symbols.setDecimalSeparator('.');
        symbols.setGroupingSeparator(',');

        // Define the pattern (here, grouping enabled with two decimal places)
        DecimalFormat formatter = new DecimalFormat("#,##0.00", symbols);

        // Format the BigDecimal to String
        return formatter.format(value);
    }

    public static long calculateDaysBetweenTwoDates(LocalDate startDate, LocalDate endDate) {

        // Check if the dates are null or if startDate is after endDate
        if (startDate == null || endDate == null) {
            throw new UnexpectedStatusException("Başlangıç tarihi ve bitiş tarihi gereklidir");
        }
        if (startDate.isEqual(endDate) || startDate.isAfter(endDate)) {
            throw new UnexpectedStatusException("Bitiş tarihi başlangıç tarihinden sonra olmalıdır");
        }
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

}
