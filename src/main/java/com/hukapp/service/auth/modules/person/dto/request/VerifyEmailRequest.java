package com.hukapp.service.auth.modules.person.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Email verification request")
public class VerifyEmailRequest {

    @NotBlank(message = "OTP boş olamaz")
    @Size(min = 8, max = 8, message = "OTP 8 haneli olmalıdır")
    @Schema(description = "One-time password sent to the email", example = "12345678")
    private String otp;

    @NotBlank(message = "Email boş olamaz")
    @Email(message = "Geçersiz email")
    @Schema(description = "Email address to verify", example = "<EMAIL>")
    private String email;
}
