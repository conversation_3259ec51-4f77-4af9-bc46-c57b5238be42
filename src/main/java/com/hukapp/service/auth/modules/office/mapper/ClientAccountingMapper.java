package com.hukapp.service.auth.modules.office.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.hukapp.service.auth.modules.office.dto.ClientAccountingCreateRequest;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingResponse;
import com.hukapp.service.auth.modules.office.dto.ClientAccountingUpdateRequest;
import com.hukapp.service.auth.modules.office.entity.ClientAccounting;

@Mapper(componentModel = "spring")
public interface ClientAccountingMapper {
    
    /**
     * Convert create request to entity
     * @param request the create request
     * @return client accounting entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "client", ignore = true)
    @Mapping(target = "owner", ignore = true)
    ClientAccounting toEntity(ClientAccountingCreateRequest request);
    
    /**
     * Convert entity to response DTO
     * @param entity the client accounting entity
     * @return client accounting response
     */
    @Mapping(target = "clientId", source = "client.id")
    @Mapping(target = "clientName", source = "client.name")
    @Mapping(target = "ownerName", expression = "java(entity.getOwner().getName() + \" \" + entity.getOwner().getSurname())")
    @Mapping(target = "ownerEmail", source = "owner.email")
    ClientAccountingResponse toResponse(ClientAccounting entity);
    
    /**
     * Update entity from update request
     * @param request the update request
     * @param entity the target entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "client", ignore = true)
    @Mapping(target = "owner", ignore = true)
    void updateEntityFromRequest(ClientAccountingUpdateRequest request, @MappingTarget ClientAccounting entity);
    
}
