package com.hukapp.service.auth.modules.office.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.office.dto.request.ClientNoteRequest;
import com.hukapp.service.auth.modules.office.dto.response.ClientNoteResponse;
import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.office.entity.ClientNote;
import com.hukapp.service.auth.modules.office.mapper.ClientNoteMapper;
import com.hukapp.service.auth.modules.office.repository.ClientNoteRepository;
import com.hukapp.service.auth.modules.office.repository.ClientRepository;
import com.hukapp.service.auth.modules.office.service.ClientNoteService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ClientNoteServiceImpl implements ClientNoteService {

    private static final String CLIENT_NOT_FOUND_MESSAGE = "Müvekkil bulunamadı";
    private static final String CLIENT_NOTE_NOT_FOUND_MESSAGE = "Müvekkil notu bulunamadı";

    private final ClientNoteRepository clientNoteRepository;
    private final ClientRepository clientRepository;
    private final PersonService personService;
    private final ClientNoteMapper clientNoteMapper;

    @Override
    public ClientNoteResponse createClientNote(Long clientId, ClientNoteRequest clientNoteRequest, String userEmail) {
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Verify the client exists and belongs to the user
        Client client = verifyClientExists(clientId, owner);
        
        // Create and save the note
        ClientNote clientNote = clientNoteMapper.toEntity(clientNoteRequest);
        clientNote.setClient(client);
        clientNote.setOwner(owner);
        
        clientNote = clientNoteRepository.save(clientNote);
        log.debug("Created client note with ID: {} for client ID: {}", clientNote.getId(), clientId);
        
        return clientNoteMapper.toDTO(clientNote);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ClientNoteResponse> getClientNotesByClientId(Long clientId, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Verify the client exists and belongs to the user
        Client client = verifyClientExists(clientId, owner);
        
        List<ClientNote> notes = clientNoteRepository.findByClientAndOwner(client, owner);
        log.debug("Found {} notes for client ID: {}", notes.size(), clientId);
        
        return notes.stream()
                .map(clientNoteMapper::toDTO)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ClientNoteResponse> getAllClientNotes(String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        List<ClientNote> notes = clientNoteRepository.findByOwner(owner);
        log.debug("Found {} total notes for user: {}", notes.size(), userEmail);
        
        return notes.stream()
                .map(clientNoteMapper::toDTO)
                .toList();
    }

    @Override
    public ClientNoteResponse updateClientNote(Long id, ClientNoteRequest clientNoteRequest, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        ClientNote existingNote = clientNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(CLIENT_NOTE_NOT_FOUND_MESSAGE));
        
        // Update the note
        clientNoteMapper.updateEntityFromDto(clientNoteRequest, existingNote);
        
        existingNote = clientNoteRepository.save(existingNote);
        log.debug("Updated client note with ID: {}", existingNote.getId());
        
        return clientNoteMapper.toDTO(existingNote);
    }

    @Override
    public void deleteClientNote(Long id, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        ClientNote clientNote = clientNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException(CLIENT_NOTE_NOT_FOUND_MESSAGE));
        
        clientNoteRepository.delete(clientNote);
        log.debug("Deleted client note with ID: {}", id);
    }

    /**
     * Verify that the client exists and belongs to the owner
     * @param clientId the client ID
     * @param owner the owner
     * @return the client entity
     * @throws ResourceNotFoundException if client not found
     */
    private Client verifyClientExists(Long clientId, Person owner) {
        return clientRepository.findByIdAndOwnerId(clientId, owner.getId())
                .orElseThrow(() -> new ResourceNotFoundException(CLIENT_NOT_FOUND_MESSAGE));
    }
}
