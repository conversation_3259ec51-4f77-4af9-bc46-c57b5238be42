package com.hukapp.service.auth.modules.office.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.office.dto.request.ClientNoteRequest;
import com.hukapp.service.auth.modules.office.dto.response.ClientNoteResponse;
import com.hukapp.service.auth.modules.office.service.ClientNoteService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/clients")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Validated
@Tag(name = "Client Notes", description = "API for managing client notes")
public class ClientNoteController {

    private final ClientNoteService clientNoteService;

    @GetMapping("/notes")
    @Operation(
        summary = "Get all client notes",
        description = "Retrieves all client notes for the authenticated user across all clients"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Client notes retrieved successfully"),
        @ApiResponse(responseCode = "402", description = "Subscription required")
    })
    public ResponseEntity<List<ClientNoteResponse>> getAllClientNotes(
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Getting all client notes for user: {}", authentication.getName());
        List<ClientNoteResponse> response = clientNoteService.getAllClientNotes(authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/{clientId}/notes")
    @Operation(
        summary = "Create a new client note",
        description = "Creates a new note for a specific client"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Client note created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Client not found")
    })
    public ResponseEntity<ClientNoteResponse> createClientNote(
            @Parameter(description = "Client ID", required = true)
            @PathVariable Long clientId,
            @Parameter(description = "Client note data", required = true)
            @Valid @RequestBody ClientNoteRequest clientNoteRequest,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Creating client note for client ID: {}", clientId);
        ClientNoteResponse response = clientNoteService.createClientNote(clientId, clientNoteRequest, authentication.getName());
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping("/{clientId}/notes")
    @Operation(
        summary = "Get all notes for a client",
        description = "Retrieves all notes for a specific client"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Client notes retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Client not found")
    })
    public ResponseEntity<List<ClientNoteResponse>> getClientNotes(
            @Parameter(description = "Client ID", required = true)
            @PathVariable Long clientId,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Getting notes for client ID: {}", clientId);
        List<ClientNoteResponse> response = clientNoteService.getClientNotesByClientId(clientId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PutMapping("/notes/{noteId}")
    @Operation(
        summary = "Update a client note",
        description = "Updates an existing client note"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Client note updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Client note not found")
    })
    public ResponseEntity<ClientNoteResponse> updateClientNote(
            @Parameter(description = "Note ID", required = true)
            @PathVariable Long noteId,
            @Parameter(description = "Updated client note data", required = true)
            @Valid @RequestBody ClientNoteRequest clientNoteRequest,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Updating client note with ID: {}", noteId);
        ClientNoteResponse response = clientNoteService.updateClientNote(noteId, clientNoteRequest, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/notes/{noteId}")
    @Operation(
        summary = "Delete a client note",
        description = "Deletes an existing client note"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Client note deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Client note not found")
    })
    public ResponseEntity<Void> deleteClientNote(
            @Parameter(description = "Note ID", required = true)
            @PathVariable Long noteId,
            @Parameter(hidden = true) Authentication authentication) {
        
        log.debug("Deleting client note with ID: {}", noteId);
        clientNoteService.deleteClientNote(noteId, authentication.getName());
        return ResponseEntity.noContent().build();
    }
}
