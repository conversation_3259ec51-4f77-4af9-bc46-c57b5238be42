package com.hukapp.service.auth.modules.app.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hukapp.service.auth.modules.sync.entity.NoSqlLikeDataCaseDetails;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.stereotype.Component;

@Component
@Slf4j
public class JsonMerger {

    private JsonMerger() {
        // private constructor to hide the implicit public one
    }
    
    public static String groupAndMergeJsonByCaseNumber(List<NoSqlLikeDataCaseDetails> models) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode groupedResults = mapper.createObjectNode();

        for (NoSqlLikeDataCaseDetails model : models) {
            String caseNumber = model.getCaseNumber();
            String jsonString = model.getJsonData();
            ArrayNode arrayNode = getOrCreateArrayNode(groupedResults, caseNumber, mapper);

            processJsonString(jsonString, caseNumber, arrayNode, mapper);
        }
        return groupedResults.toString();
    }

    private static ArrayNode getOrCreateArrayNode(ObjectNode groupedResults, String caseNumber, ObjectMapper mapper) {
        if (groupedResults.has(caseNumber)) {
            return (ArrayNode) groupedResults.get(caseNumber);
        } else {
            ArrayNode arrayNode = mapper.createArrayNode();
            groupedResults.set(caseNumber, arrayNode);
            return arrayNode;
        }
    }

    private static void processJsonString(String jsonString, String caseNumber, ArrayNode arrayNode, ObjectMapper mapper) {
        try {
            JsonNode rootNode = mapper.readTree(jsonString);

            if (rootNode.isArray()) {
                rootNode.forEach(arrayNode::add);
            } else if (rootNode.isObject()) {
                handleJsonObject(rootNode, caseNumber, arrayNode);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    private static void handleJsonObject(JsonNode rootNode, String caseNumber, ArrayNode arrayNode) {
        if (rootNode.has("errorCode") || rootNode.has("error")) {
            log.warn("Skipping error JSON for case {}: {}", caseNumber, rootNode.toString());
        } else {
            arrayNode.add(rootNode);
        }
    }
}