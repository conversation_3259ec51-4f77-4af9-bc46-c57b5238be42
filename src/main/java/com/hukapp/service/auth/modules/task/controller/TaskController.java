package com.hukapp.service.auth.modules.task.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import com.hukapp.service.auth.modules.task.dto.NoteRequest;
import com.hukapp.service.auth.modules.task.dto.NoteResponse;
import com.hukapp.service.auth.modules.task.dto.TaskRequest;
import com.hukapp.service.auth.modules.task.dto.TaskResponse;
import com.hukapp.service.auth.modules.task.service.NoteService;
import com.hukapp.service.auth.modules.task.service.TaskService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import java.util.List;

@RestController
@RequestMapping("/api/tasks")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
public class TaskController {

    private final TaskService taskService;

    private final NoteService noteService;

    @Operation(summary = "Get all tasks of the authenticated user")
    @GetMapping
    public ResponseEntity<List<TaskResponse>> getAllTasks(Authentication authentication) {
        return ResponseEntity.ok(taskService.getAllTasks(authentication));
    }

    @Operation(summary = "Get a task by id of the authenticated user")
    @GetMapping("/{id}")
    public ResponseEntity<TaskResponse> getTaskById(@PathVariable Long id, Authentication authentication) {
        return ResponseEntity.ok(taskService.getTaskById(id, authentication));
    }

    @Operation(summary = "Create a new task")
    @PostMapping
    public ResponseEntity<TaskResponse> createTask(@RequestBody @Valid TaskRequest entity, Authentication authentication) {
        return ResponseEntity.ok(taskService.createTask(entity, authentication));
    }

    @Operation(summary = "Update an existing task of the authenticated user by id")
    @PutMapping("/{id}")
    public ResponseEntity<TaskResponse> updateTask(@PathVariable Long id, @RequestBody @Valid TaskRequest entity, Authentication authentication) {
        return ResponseEntity.ok(taskService.updateTask(id, entity, authentication));
    }

    @Operation(summary = "Delete a task of the authenticated user by id")
    @DeleteMapping("/{id}")
    public void deleteTask(@PathVariable Long id, Authentication authentication) {
        taskService.deleteTask(id, authentication);
    }

    @Operation(summary = "Create a new note for a task of the authenticated user")
    @PostMapping("/{taskId}/notes")
    public ResponseEntity<NoteResponse> createNoteForTask(@PathVariable Long taskId, @RequestBody @Valid NoteRequest entity, Authentication authentication) {
        return ResponseEntity.ok(noteService.createNoteForTask(taskId, entity, authentication));
    }

    @Operation(summary = "Updates a note of the authenticated user by id")
    @PutMapping("/notes/{noteId}")
    public ResponseEntity<NoteResponse> updateNote(@PathVariable Long noteId, @RequestBody @Valid NoteRequest entity, Authentication authentication) {
        return ResponseEntity.ok(noteService.updateNote(noteId, entity, authentication));
    }

    @Operation(summary = "Delete a note of the authenticated user by id")
    @DeleteMapping("/notes/{noteId}")
    public void deleteNote(@PathVariable Long noteId, Authentication authentication) {
        noteService.deleteNote(noteId, authentication);
    }
}