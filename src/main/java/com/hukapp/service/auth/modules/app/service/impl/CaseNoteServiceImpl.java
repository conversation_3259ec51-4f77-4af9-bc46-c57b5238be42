package com.hukapp.service.auth.modules.app.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.app.dto.request.CaseNoteRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseNoteResponse;
import com.hukapp.service.auth.modules.app.entity.CaseNote;
import com.hukapp.service.auth.modules.app.mapper.CaseNoteMapper;
import com.hukapp.service.auth.modules.app.repository.CaseNoteRepository;
import com.hukapp.service.auth.modules.app.repository.CaseRepository;
import com.hukapp.service.auth.modules.app.service.CaseNoteService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.sync.enums.DataSourceEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class CaseNoteServiceImpl implements CaseNoteService {

    private final CaseNoteRepository caseNoteRepository;
    private final CaseRepository caseRepository;
    private final PersonService personService;
    private final CaseNoteMapper caseNoteMapper;

    @Override
    public CaseNoteResponse createCaseNote(CaseNoteRequest caseNoteRequest, String userEmail) {
        // Verify the user exists
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Verify the case exists for this user
        verifyCaseExists(caseNoteRequest.getCaseNumber(), userEmail);
        
        // Create and save the note
        CaseNote caseNote = caseNoteMapper.toEntity(caseNoteRequest);
        caseNote.setOwner(owner);
        
        caseNote = caseNoteRepository.save(caseNote);
        log.debug("Created case note with ID: {} for case number: {}", caseNote.getId(), caseNote.getCaseNumber());
        
        return caseNoteMapper.toDTO(caseNote);
    }

    @Override
    public List<CaseNoteResponse> getCaseNotesByCaseNumber(String caseNumber, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Verify the case exists for this user
        verifyCaseExists(caseNumber, userEmail);
        
        List<CaseNote> notes = caseNoteRepository.findByCaseNumberAndOwner(caseNumber, owner);
        return notes.stream()
                .map(caseNoteMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<CaseNoteResponse> getAllCaseNotes(String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        List<CaseNote> notes = caseNoteRepository.findByOwner(owner);
        return notes.stream()
                .map(caseNoteMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public CaseNoteResponse getCaseNoteById(Long id, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        CaseNote caseNote = caseNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Case note not found with ID: " + id));
        
        return caseNoteMapper.toDTO(caseNote);
    }

    @Override
    public CaseNoteResponse updateCaseNote(Long id, CaseNoteRequest caseNoteRequest, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Verify the case exists for this user
        verifyCaseExists(caseNoteRequest.getCaseNumber(), userEmail);
        
        // Find the existing note
        CaseNote existingNote = caseNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Case note not found with ID: " + id));
        
        // Update the note
        existingNote.setContent(caseNoteRequest.getContent());
        existingNote.setCaseNumber(caseNoteRequest.getCaseNumber());
        
        existingNote = caseNoteRepository.save(existingNote);
        log.debug("Updated case note with ID: {}", existingNote.getId());
        
        return caseNoteMapper.toDTO(existingNote);
    }

    @Override
    public void deleteCaseNote(Long id, String userEmail) {
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);
        
        CaseNote caseNote = caseNoteRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Case note not found with ID: " + id));
        
        caseNoteRepository.delete(caseNote);
        log.debug("Deleted case note with ID: {}", id);
    }
    
    /**
     * Verifies that a case with the given case number exists for the user
     * 
     * @param caseNumber The case number to verify
     * @param userEmail The email of the user
     * @throws ResourceNotFoundException if the case does not exist
     */
    private void verifyCaseExists(String caseNumber, String userEmail) {
        // Check if the case exists in any of the case data sources
        boolean caseExists = false;
        
        // Try to find the case in UYAP_CASE_TARAFLAR
        caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                userEmail, DataSourceEnum.UYAP_CASE_TARAFLAR, caseNumber).isPresent();
        
        if (!caseExists) {
            // Try to find the case in UYAP_CASE_HISTORY
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_HISTORY, caseNumber).isPresent();
        }
        
        if (!caseExists) {
            // Try to find the case in UYAP_CASE_TAHSILAT_REDDIYAT
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_TAHSILAT_REDDIYAT, caseNumber).isPresent();
        }
        
        if (!caseExists) {
            throw new ResourceNotFoundException("Dosya bulunamadı, dosya yıl/esas: " + caseNumber);
        }
    }
}
