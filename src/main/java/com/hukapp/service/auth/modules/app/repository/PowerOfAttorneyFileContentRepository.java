package com.hukapp.service.auth.modules.app.repository;

import com.hukapp.service.auth.modules.app.entity.PowerOfAttorneyFileContent;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for PowerOfAttorneyFileContent
 */
@Repository
public interface PowerOfAttorneyFileContentRepository extends JpaRepository<PowerOfAttorneyFileContent, Long> {
    // Basic CRUD operations are inherited from JpaRepository
    // Additional custom queries can be added here if needed

    /**
     * Delete file content by ID (hard delete)
     */
    @Modifying
    @Query("DELETE FROM PowerOfAttorneyFileContent f WHERE f.id = :id")
    void deleteById(@NonNull @Param("id") Long id);
}
