package com.hukapp.service.auth.modules.app.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.app.dto.request.CaseNoteRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseNoteResponse;
import com.hukapp.service.auth.modules.app.service.CaseNoteService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/cases/notes")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Case Notes", description = "API for managing case notes")
public class CaseNoteController {

    private final CaseNoteService caseNoteService;

    @PostMapping
    @Operation(summary = "Create a new case note", description = "Creates a new note for a specific case")
    public ResponseEntity<CaseNoteResponse> createCaseNote(
            @Valid @RequestBody CaseNoteRequest caseNoteRequest,
            Authentication authentication) {
        
        log.debug("Creating case note for case number: {}", caseNoteRequest.getCaseNumber());
        CaseNoteResponse response = caseNoteService.createCaseNote(caseNoteRequest, authentication.getName());
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping
    @Operation(summary = "Get all case notes", description = "Retrieves all case notes for the authenticated user")
    public ResponseEntity<List<CaseNoteResponse>> getAllCaseNotes(Authentication authentication) {
        log.debug("Retrieving all case notes for user: {}", authentication.getName());
        List<CaseNoteResponse> notes = caseNoteService.getAllCaseNotes(authentication.getName());
        return ResponseEntity.ok(notes);
    }

    @GetMapping("/case")
    @Operation(summary = "Get notes for a specific case", description = "Retrieves all notes for a specific case number")
    public ResponseEntity<List<CaseNoteResponse>> getCaseNotesByCaseNumber(
            @RequestParam String caseNumber,
            Authentication authentication) {
        
        log.debug("Retrieving notes for case number: {}", caseNumber);
        List<CaseNoteResponse> notes = caseNoteService.getCaseNotesByCaseNumber(caseNumber, authentication.getName());
        return ResponseEntity.ok(notes);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get a specific case note", description = "Retrieves a specific case note by its ID")
    public ResponseEntity<CaseNoteResponse> getCaseNoteById(
            @PathVariable Long id,
            Authentication authentication) {
        
        log.debug("Retrieving case note with ID: {}", id);
        CaseNoteResponse note = caseNoteService.getCaseNoteById(id, authentication.getName());
        return ResponseEntity.ok(note);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update a case note", description = "Updates an existing case note")
    public ResponseEntity<CaseNoteResponse> updateCaseNote(
            @PathVariable Long id,
            @Valid @RequestBody CaseNoteRequest caseNoteRequest,
            Authentication authentication) {
        
        log.debug("Updating case note with ID: {}", id);
        CaseNoteResponse response = caseNoteService.updateCaseNote(id, caseNoteRequest, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a case note", description = "Deletes a case note by its ID")
    public ResponseEntity<Void> deleteCaseNote(
            @PathVariable Long id,
            Authentication authentication) {
        
        log.debug("Deleting case note with ID: {}", id);
        caseNoteService.deleteCaseNote(id, authentication.getName());
        return ResponseEntity.noContent().build();
    }
}
