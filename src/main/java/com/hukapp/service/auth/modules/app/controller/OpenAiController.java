package com.hukapp.service.auth.modules.app.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.common.cache.ApplicationParameterCache;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;

@Slf4j
@RestController
@RequestMapping("api/user/chat")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "OPEN AI Key Retrieveal", description = "API for retrieving OPEN AI Key ")
public class OpenAiController {

    private final ApplicationParameterCache applicationParameterCache;

    @GetMapping("openai")
    public String returnOpenAiApiKey() {
        return applicationParameterCache.getParameterValue("open.ai.api.key");
    }

}
