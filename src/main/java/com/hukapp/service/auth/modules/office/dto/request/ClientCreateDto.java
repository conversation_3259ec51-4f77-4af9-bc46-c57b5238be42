package com.hukapp.service.auth.modules.office.dto.request;

import com.hukapp.service.auth.modules.office.enums.ClientType;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Client creation request DTO")
public class ClientCreateDto {

    @NotBlank(message = "Müvekkil adı gereklidir")
    @Size(min = 2, max = 100, message = "Müvekkil adı 2-100 karakter arasında olmalıdır")
    @Schema(description = "Client name", example = "<PERSON><PERSON> Yılmaz")
    private String name;

    @Schema(description = "Identity number for individuals or tax number for corporations", example = "12345678901")
    private Long identityOrTaxNumber;

    @Size(max = 500, message = "Adres en fazla 500 karakter olmalıdır")
    @Schema(description = "Client address", example = "Atatürk Cad. No:123 Çankaya/Ankara")
    private String address;

    @Size(max = 15, message = "Telefon numarası en fazla 15 karakter olmalıdır")
    @Schema(description = "Client phone number", example = "05551234567")
    private String phoneNumber;

    @Email(message = "Geçerli bir email adresi giriniz")
    @Schema(description = "Client email address", example = "<EMAIL>")
    private String email;

    @NotNull(message = "Müvekkil tipi gereklidir")
    @Schema(description = "Client type", example = "INDIVIDUAL")
    private ClientType clientType;
}
