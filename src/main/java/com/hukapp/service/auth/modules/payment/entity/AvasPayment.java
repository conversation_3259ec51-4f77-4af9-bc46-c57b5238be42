package com.hukapp.service.auth.modules.payment.entity;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class AvasPayment extends BaseEntity {

    @Column(nullable = false)
    private String conversationId;

    @Column(nullable = false)
    private BigDecimal price;

    @Column(nullable = false)
    private BigDecimal paidPrice;

    @Column(nullable = false)
    private String currency;

    @Column(nullable = false)
    private String basketId;

    @Column(nullable = false)
    private String paymentId;

    private String paymentTransactionId;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private PaymentStatus status;

    private Instant paymentDate;

    private String errorCode;

    private String errorMessage;

    private String errorGroup;

    @ManyToOne(optional = false)
    @JoinColumn(name = "owner_id", nullable = false)
    private Person owner;

    @Column(nullable = false)
    private String paymentPageUrl;

    @ManyToOne(optional = false)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;

    private Instant validUntil;

    private String appliedCouponCode;

    private BigDecimal originalPrice;

    private BigDecimal discountAmount;

}
