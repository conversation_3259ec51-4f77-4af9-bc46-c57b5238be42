package com.hukapp.service.auth.modules.email.service.impl;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.hukapp.service.auth.modules.email.config.EmailProperties;
import com.hukapp.service.auth.modules.email.exception.EmailSendException;
import com.hukapp.service.auth.modules.email.service.EmailService;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of the EmailService interface.
 * Provides methods to send different types of emails using JavaMailSender.
 * Supports plain text emails, HTML emails, template-based emails, and OTP emails.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailServiceImpl implements EmailService {

    private final JavaMailSender mailSender;
    private final EmailProperties emailProperties;

    @Override
    public void sendSimpleEmail(String to, String subject, String text) {
        try {
            log.info("Preparing to send simple email to: {}", to);
            log.debug("Email details - Subject: {}, From: {}", subject, emailProperties.from());

            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(emailProperties.from());
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);

            log.info("Sending email via SMTP server: {}:{}", emailProperties.host(), emailProperties.port());
            mailSender.send(message);
            log.info("Simple email sent successfully to: {}", to);
        } catch (MailException e) {
            log.error("Mail server error when sending email to: {} - {}", to, e.getMessage(), e);
            throw new EmailSendException("Failed to send simple email to: " + to + " - " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error when sending simple email to: {} - {}", to, e.getMessage(), e);
            throw new EmailSendException("Unexpected error when sending email to: " + to + " - " + e.getMessage(), e);
        }
    }

    @Override
    public void sendHtmlEmail(String to, String subject, String htmlContent) {
        try {
            log.info("Preparing to send HTML email to: {}", to);
            log.debug("Email details - Subject: {}, From: {}", subject, emailProperties.from());

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(emailProperties.from());
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);

            log.info("Sending HTML email via SMTP server: {}:{}", emailProperties.host(), emailProperties.port());
            mailSender.send(message);
            log.info("HTML email sent successfully to: {}", to);
        } catch (MessagingException e) {
            log.error("Failed to create HTML email message for recipient: {} - {}", to, e.getMessage(), e);
            throw new EmailSendException("Failed to create HTML email message - " + e.getMessage(), e);
        } catch (MailException e) {
            log.error("Mail server error when sending HTML email to: {} - {}", to, e.getMessage(), e);
            throw new EmailSendException("Failed to send HTML email to: " + to + " - " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error when sending HTML email to: {} - {}", to, e.getMessage(), e);
            throw new EmailSendException("Unexpected error when sending HTML email - " + e.getMessage(), e);
        }
    }

    @Override
    public void sendTemplateEmail(String to, String subject, String template, Map<String, Object> templateModel) {
        // In a real implementation, this would use a template engine like Thymeleaf
        // For now, we'll just create a simple HTML email with the template variables
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<html><body>");
        htmlBuilder.append("<h1>").append(subject).append("</h1>");

        // Add template variables to the email
        for (Map.Entry<String, Object> entry : templateModel.entrySet()) {
            htmlBuilder.append("<p><strong>").append(entry.getKey()).append(":</strong> ")
                      .append(entry.getValue()).append("</p>");
        }

        htmlBuilder.append("</body></html>");

        sendHtmlEmail(to, subject, htmlBuilder.toString());
    }

    @Override
    public void sendOtpEmail(String to, String otp) {
        String subject = "Tek Kullanımlık Şifre: " + otp;
        String htmlContent = "<html><body>" +
                "<h1>Tek Kullanımlık Şifre</h1>" +
                "<p>Lütfen aşağıdaki kodu girerek talebinizi tamamlayın:</p>" +
                "<h2 style='color: #007bff; font-size: 24px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;'>" + otp + "</h2>" +
                "<p>Bu kod 3 dakika sonra geçersiz olacaktır.</p>" +
                "<p>Eğer kodu siz talep etmediyseniz, lütfen bu e-postayı dikkate almayın.</p>" +
                "</body></html>";

        sendHtmlEmail(to, subject, htmlContent);
    }

    @Override
    public void sendLoginNotification(String toEmail, String ipAddress, String userAgent) {
        String subject = "AVAS Login Bildirimi: " + ipAddress;
        String body = String.format(
            "Hesabınızdan yeni bir giriş tespit edildi. IP Adresi: %s, Cihaz: %s, Zaman: %s",
            ipAddress, userAgent, java.time.LocalDateTime.now()
        );
        // sendEmail is your existing method to send emails
        sendHtmlEmail(toEmail, subject, body);
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Void> sendLoginNotificationAsync(String toEmail, String ipAddress, String userAgent) {
        try {
            log.info("Sending login notification email asynchronously to: {}", toEmail);
            sendLoginNotification(toEmail, ipAddress, userAgent);
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("Failed to send login notification email asynchronously to: {} - {}", toEmail, e.getMessage(), e);
            // We don't rethrow the exception since this is async and we want to log it instead
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }
}
