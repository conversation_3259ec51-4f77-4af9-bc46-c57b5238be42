package com.hukapp.service.auth.modules.app.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.PowerOfAttorneyResponse;
import com.hukapp.service.auth.modules.app.service.PowerOfAttorneyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/cases/power-of-attorneys")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Power of Attorneys", description = "API for managing power of attorneys for legal cases")
public class PowerOfAttorneyController {

    private final PowerOfAttorneyService powerOfAttorneyService;

    @PostMapping
    @Operation(
        summary = "Create a new power of attorney",
        description = "Creates a new power of attorney document for a specific legal case. Requires authentication."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Power of attorney created successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PowerOfAttorneyResponse.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid input data", content = @Content),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
        @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content)
    })
    public ResponseEntity<PowerOfAttorneyResponse> createPowerOfAttorney(
            @Parameter(description = "Power of attorney data", required = true)
            @Valid @RequestBody PowerOfAttorneyCreateDto powerOfAttorneyCreateDto,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Creating power of attorney for notary: {}", powerOfAttorneyCreateDto.getNotaryName());
        PowerOfAttorneyResponse response = powerOfAttorneyService.createPowerOfAttorney(
                powerOfAttorneyCreateDto, authentication.getName());
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping
    @Operation(
        summary = "Get all power of attorneys",
        description = "Retrieves all power of attorneys for the authenticated user. Results are sorted by creation date."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "List of power of attorneys retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PowerOfAttorneyResponse.class))
        ),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
        @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content)
    })
    public ResponseEntity<List<PowerOfAttorneyResponse>> getAllPowerOfAttorneys(
            @Parameter(hidden = true) Authentication authentication) {
        log.debug("Retrieving all power of attorneys for user: {}", authentication.getName());
        List<PowerOfAttorneyResponse> powerOfAttorneys = powerOfAttorneyService.getAllPowerOfAttorneys(authentication.getName());
        return ResponseEntity.ok(powerOfAttorneys);
    }

    @GetMapping("/case")
    @Operation(
        summary = "Get power of attorneys for a specific case",
        description = "Retrieves all power of attorneys associated with a specific case number for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "List of power of attorneys for the specified case retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PowerOfAttorneyResponse.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid case number", content = @Content),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
        @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content)
    })
    public ResponseEntity<List<PowerOfAttorneyResponse>> getPowerOfAttorneysByCaseNumber(
            @Parameter(description = "Case number to filter power of attorneys", required = true, example = "2023/123")
            @RequestParam String caseNumber,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Retrieving power of attorneys for case number: {}", caseNumber);
        List<PowerOfAttorneyResponse> powerOfAttorneys = powerOfAttorneyService.getPowerOfAttorneysByCaseNumber(
                caseNumber, authentication.getName());
        return ResponseEntity.ok(powerOfAttorneys);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get a specific power of attorney",
        description = "Retrieves a specific power of attorney by its ID for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Power of attorney retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PowerOfAttorneyResponse.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid ID format", content = @Content),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
        @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
        @ApiResponse(responseCode = "404", description = "Power of attorney not found", content = @Content)
    })
    public ResponseEntity<PowerOfAttorneyResponse> getPowerOfAttorneyById(
            @Parameter(description = "ID of the power of attorney to retrieve", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Retrieving power of attorney with ID: {}", id);
        PowerOfAttorneyResponse powerOfAttorney = powerOfAttorneyService.getPowerOfAttorneyById(
                id, authentication.getName());
        return ResponseEntity.ok(powerOfAttorney);
    }

    @GetMapping("/number/{powerOfAttorneyNumber}")
    @Operation(
        summary = "Get a power of attorney by its number",
        description = "Retrieves a specific power of attorney by its power of attorney number for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Power of attorney retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PowerOfAttorneyResponse.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid power of attorney number format", content = @Content),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
        @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
        @ApiResponse(responseCode = "404", description = "Power of attorney not found", content = @Content)
    })
    public ResponseEntity<PowerOfAttorneyResponse> getPowerOfAttorneyByNumber(
            @Parameter(description = "Power of attorney number to retrieve", required = true, example = "POA-2023-12345")
            @PathVariable String powerOfAttorneyNumber,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Retrieving power of attorney with number: {}", powerOfAttorneyNumber);
        PowerOfAttorneyResponse powerOfAttorney = powerOfAttorneyService.getPowerOfAttorneyByNumber(
                powerOfAttorneyNumber, authentication.getName());
        return ResponseEntity.ok(powerOfAttorney);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update a power of attorney",
        description = "Updates an existing power of attorney with the provided data. Only the owner can update their power of attorney."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Power of attorney updated successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PowerOfAttorneyResponse.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid input data or ID format", content = @Content),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
        @ApiResponse(responseCode = "403", description = "Forbidden - not the owner of the resource", content = @Content),
        @ApiResponse(responseCode = "404", description = "Power of attorney not found", content = @Content)
    })
    public ResponseEntity<PowerOfAttorneyResponse> updatePowerOfAttorney(
            @Parameter(description = "ID of the power of attorney to update", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(description = "Updated power of attorney data", required = true)
            @Valid @RequestBody PowerOfAttorneyUpdateDto powerOfAttorneyUpdateDto,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Updating power of attorney with ID: {}", id);
        PowerOfAttorneyResponse response = powerOfAttorneyService.updatePowerOfAttorney(
                id, powerOfAttorneyUpdateDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a power of attorney",
        description = "Deletes a power of attorney by its ID. Only the owner can delete their power of attorney."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "204",
            description = "Power of attorney deleted successfully",
            content = @Content
        ),
        @ApiResponse(responseCode = "400", description = "Invalid ID format", content = @Content),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
        @ApiResponse(responseCode = "403", description = "Forbidden - not the owner of the resource", content = @Content),
        @ApiResponse(responseCode = "404", description = "Power of attorney not found", content = @Content)
    })
    public ResponseEntity<Void> deletePowerOfAttorney(
            @Parameter(description = "ID of the power of attorney to delete", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Deleting power of attorney with ID: {}", id);
        powerOfAttorneyService.deletePowerOfAttorney(id, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/client/{clientId}")
    @Operation(
        summary = "Get power of attorneys for a specific client",
        description = "Retrieves all power of attorneys associated with a specific client ID for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Power of attorneys retrieved successfully",
            content = @Content(schema = @Schema(implementation = PowerOfAttorneyResponse.class))
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Client not found"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized"
        )
    })
    public ResponseEntity<List<PowerOfAttorneyResponse>> getPowerOfAttorneysByClientId(
            @Parameter(description = "ID of the client to retrieve power of attorneys for", required = true, example = "1")
            @PathVariable Long clientId,
            @Parameter(hidden = true) Authentication authentication) {

        log.debug("Retrieving power of attorneys for client ID: {}", clientId);
        List<PowerOfAttorneyResponse> powerOfAttorneys = powerOfAttorneyService.getPowerOfAttorneysByClientId(
                clientId, authentication.getName());
        return ResponseEntity.ok(powerOfAttorneys);
    }
}
