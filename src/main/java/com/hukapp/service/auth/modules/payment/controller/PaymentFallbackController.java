package com.hukapp.service.auth.modules.payment.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.hukapp.service.auth.modules.payment.dto.PaymentStatusResponse;
import com.hukapp.service.auth.modules.payment.service.PaymentService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@Controller
@RequestMapping("webhooks/payment")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Payment Fallback", description = "Payment fallback operations with iyzico")
public class PaymentFallbackController {

    private final PaymentService paymentService;

    @PostMapping("fallback")
    public String fallbackPayment(@RequestBody String token, Model model) {

        PaymentStatusResponse paymentStatus = paymentService.retrievePaymentByPaymentId(token.split("=")[1]);

        log.debug("Payment fallback endpoint called with token: {}", token);

        // Add payment status data to model for the view
        model.addAttribute("paymentStatus", paymentStatus);
        model.addAttribute("token", token);
        model.addAttribute("retryPaymentUrl", "/webhooks/payment/retry-payment?id=" + token.split("=")[1]);
        model.addAttribute("avasDashboard", "http://localhost:8081");

        return "payment-fallback";
    }

    @GetMapping("retry-payment")
    public String retryPaymentStatusRetrieval(@RequestParam("id") String paymentId, Model model) {
        PaymentStatusResponse paymentStatus = paymentService.retrievePaymentByPaymentId(paymentId);
        
        log.debug("Retry payment status retrieval called with payment ID: {}", paymentId);
        
        // Add payment status data to model for the view
        model.addAttribute("paymentStatus", paymentStatus);
        model.addAttribute("token", paymentId);
        model.addAttribute("retryPaymentUrl", "/webhooks/payment/retry-payment?id=" + paymentId);
        model.addAttribute("avasDashboard", "http://localhost:8081");
        
        return "payment-fallback";
        
    }
    
}
