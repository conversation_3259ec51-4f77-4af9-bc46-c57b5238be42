package com.hukapp.service.auth.modules.payment.service.impl;

import java.time.Instant;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.modules.payment.entity.AvasPayment;
import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;
import com.hukapp.service.auth.modules.payment.enums.ProductType;
import com.hukapp.service.auth.modules.payment.repository.PaymentRepository;
import com.hukapp.service.auth.modules.payment.service.SubscriptionValidationService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of SubscriptionValidationService that handles subscription validation logic.
 * This service checks user payment status, subscription levels, and validity periods.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class SubscriptionValidationServiceImpl implements SubscriptionValidationService {
    
    private final PaymentRepository paymentRepository;
    private final PersonService personService;
    
    @Override
    public boolean hasValidSubscription(String userEmail, ProductType requiredLevel) {
        try {
            Person person = personService.getPersonByEmailOrElseThrow(userEmail);
            return hasValidSubscription(person, requiredLevel);
        } catch (Exception e) {
            log.warn("Failed to validate subscription for user {}: {}", userEmail, e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean hasValidSubscription(Person person, ProductType requiredLevel) {
        if (person == null || requiredLevel == null) {
            log.debug("Invalid parameters: person={}, requiredLevel={}", person, requiredLevel);
            return false;
        }
        
        try {
            Instant currentTime = Instant.now();
            
            // Get all active payments for the user
            List<AvasPayment> activePayments = paymentRepository.findActivePaymentsByOwner(
                person, PaymentStatus.SUCCESS, currentTime);
            
            if (activePayments.isEmpty()) {
                log.debug("No active payments found for user: {}", person.getEmail());
                return false;
            }
            
            // Check if any active payment meets the required subscription level
            boolean hasValidSubscription = activePayments.stream()
                .anyMatch(payment -> {
                    ProductType userLevel = payment.getProduct().getType();
                    boolean meetsRequirement = userLevel.meetsRequirement(requiredLevel);
                    log.debug("User {} has subscription level {}, required {}, meets requirement: {}", 
                        person.getEmail(), userLevel, requiredLevel, meetsRequirement);
                    return meetsRequirement;
                });
            
            log.debug("Subscription validation result for user {}: {}", person.getEmail(), hasValidSubscription);
            return hasValidSubscription;
            
        } catch (Exception e) {
            log.error("Error validating subscription for user {}: {}", person.getEmail(), e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public ProductType getHighestSubscriptionLevel(String userEmail) {
        try {
            Person person = personService.getPersonByEmailOrElseThrow(userEmail);
            return getHighestSubscriptionLevel(person);
        } catch (Exception e) {
            log.warn("Failed to get subscription level for user {}: {}", userEmail, e.getMessage());
            return null;
        }
    }
    
    @Override
    public ProductType getHighestSubscriptionLevel(Person person) {
        if (person == null) {
            return null;
        }
        
        try {
            Instant currentTime = Instant.now();
            
            // Get the highest subscription level from active payments
            return paymentRepository.findHighestActiveSubscriptionLevel(
                person, PaymentStatus.SUCCESS, currentTime).orElse(null);
                
        } catch (Exception e) {
            log.error("Error getting highest subscription level for user {}: {}", 
                person.getEmail(), e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean hasAnyActiveSubscription(String userEmail) {
        try {
            Person person = personService.getPersonByEmailOrElseThrow(userEmail);
            return hasAnyActiveSubscription(person);
        } catch (Exception e) {
            log.warn("Failed to check active subscription for user {}: {}", userEmail, e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean hasAnyActiveSubscription(Person person) {
        if (person == null) {
            return false;
        }
        
        try {
            Instant currentTime = Instant.now();
            
            List<AvasPayment> activePayments = paymentRepository.findActivePaymentsByOwner(
                person, PaymentStatus.SUCCESS, currentTime);
            
            boolean hasActiveSubscription = !activePayments.isEmpty();
            log.debug("User {} has active subscription: {}", person.getEmail(), hasActiveSubscription);
            
            return hasActiveSubscription;
            
        } catch (Exception e) {
            log.error("Error checking active subscription for user {}: {}", 
                person.getEmail(), e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public String getSubscriptionRequirementMessage(ProductType requiredLevel) {
        if (requiredLevel == null) {
            return "Bu özellik için geçerli bir abonelik planına sahip olmanız gerekmektedir.";
        }
        
        return String.format("Bu özellik için %s aboneliğine sahip olmanız gerekmektedir. " +
            "Lütfen abonelik planınızı yükseltin.", requiredLevel.getDisplayName());
    }
}
