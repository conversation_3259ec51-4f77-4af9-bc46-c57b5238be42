package com.hukapp.service.auth.modules.payment.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.payment.dto.ProductCreateRequest;
import com.hukapp.service.auth.modules.payment.dto.ProductResponse;
import com.hukapp.service.auth.modules.payment.dto.ProductUpdateRequest;
import com.hukapp.service.auth.modules.payment.entity.Product;
import com.hukapp.service.auth.modules.payment.mapper.ProductMapper;
import com.hukapp.service.auth.modules.payment.repository.ProductRepository;
import com.hukapp.service.auth.modules.payment.service.ProductService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;
    private final ProductMapper productMapper;

    @Override
    @Transactional(readOnly = true)
    public List<ProductResponse> getAllProducts() {
        log.debug("Getting all products");
        List<Product> products = productRepository.findAll();
        return productMapper.toResponseList(products);
    }

    @Override
    @Transactional(readOnly = true)
    public ProductResponse getProductById(Long productId) {
        log.debug("Getting product by ID: {}", productId);
        Product product = getProductByIdOrElseThrow(productId);
        return productMapper.toResponse(product);
    }

    @Override
    @Transactional(readOnly = true)
    public Product getProductByIdOrElseThrow(Long productId) {
        return productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("Ürün bulunamadı"));
    }

    @Override
    @Transactional
    public ProductResponse createProduct(ProductCreateRequest request) {
        log.debug("Creating new product with name: {}", request.getName());

        Product product = productMapper.toEntity(request);
        Product savedProduct = productRepository.save(product);

        log.info("Product created successfully with ID: {}", savedProduct.getId());
        return productMapper.toResponse(savedProduct);
    }

    @Override
    @Transactional
    public ProductResponse updateProduct(Long productId, ProductUpdateRequest request) {
        log.debug("Updating product with ID: {}", productId);

        Product existingProduct = getProductByIdOrElseThrow(productId);
        productMapper.updateEntity(request, existingProduct);
        Product updatedProduct = productRepository.save(existingProduct);

        log.info("Product updated successfully with ID: {}", updatedProduct.getId());
        return productMapper.toResponse(updatedProduct);
    }

    @Override
    @Transactional
    public void deleteProduct(Long productId) {
        log.debug("Deleting product with ID: {}", productId);

        Product product = getProductByIdOrElseThrow(productId);
        productRepository.delete(product);

        log.info("Product deleted successfully with ID: {}", productId);
    }

}
