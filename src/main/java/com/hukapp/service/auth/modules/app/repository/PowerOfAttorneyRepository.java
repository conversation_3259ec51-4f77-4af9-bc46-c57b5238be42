package com.hukapp.service.auth.modules.app.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.app.entity.PowerOfAttorney;
import com.hukapp.service.auth.modules.person.entity.Person;

@Repository
public interface PowerOfAttorneyRepository extends JpaRepository<PowerOfAttorney, Long> {
    
    List<PowerOfAttorney> findByCaseNumbersContainingAndOwner(String caseNumber, Person owner);
    
    List<PowerOfAttorney> findByOwner(Person owner);
    
    Optional<PowerOfAttorney> findByIdAndOwner(Long id, Person owner);
    
    Optional<PowerOfAttorney> findByIdAndCaseNumbersContainingAndOwner(Long id, String caseNumber, Person owner);
    
    Optional<PowerOfAttorney> findByPowerOfAttorneyNumberAndOwner(String powerOfAttorney<PERSON><PERSON><PERSON>, Person owner);

    List<PowerOfAttorney> findByClientListIdAndOwner(Long clientId, Person owner);
}
