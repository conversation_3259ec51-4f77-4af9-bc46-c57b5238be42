package com.hukapp.service.auth.modules.app.enums;

public enum CrimeType {
    DOLANDIRICILIK("Dolandırıcılık"),
    HIRSIZLIK("Hırsızlık"),
    KASTEN_YARALAMA("Kasten Yaralama"),
    TAKSIRLE_YARALAMA("Taksirle Yaralama"),
    KASTEN_OLDURME("Kasten Öldürme"),
    TAKSIRLE_OLDURME("Taksirle Öldürme"),
    HAKARET("Hakaret"),
    TEHDIT("Tehdit"),
    UYUSTURUCU("Uyuşturucu Madde Ticareti"),
    CINSEL_SUC("Cinsel Suçlar"),
    ZIMMET("Zimmet"),
    RUSVET("Rüşvet"),
    SAHTECILIK("Sahtecilik"),
    VERGI_SUCU("Vergi Suçları"),
    DIGER("<PERSON>ğer");
    
    private final String displayName;
    
    CrimeType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
}
