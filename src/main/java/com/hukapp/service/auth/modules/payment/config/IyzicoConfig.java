package com.hukapp.service.auth.modules.payment.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.iyzipay.Options;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class IyzicoConfig {

    private final IyzicoProperties iyzicoProperties;

    @Bean
    public Options iyzicoOptions() {
        Options options = new Options();
        options.setApiKey(iyzicoProperties.apiKey());
        options.setSecretKey(iyzicoProperties.secretKey());
        options.setBaseUrl(iyzicoProperties.baseUrl());
        return options;
    }
}
