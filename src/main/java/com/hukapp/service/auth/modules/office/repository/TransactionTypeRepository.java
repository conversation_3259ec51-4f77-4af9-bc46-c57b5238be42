package com.hukapp.service.auth.modules.office.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.hukapp.service.auth.modules.office.entity.TransactionType;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;

public interface TransactionTypeRepository extends JpaRepository<TransactionType, Long> {

    List<TransactionType> findByCategory(TransactionCategory category);
    
}
