package com.hukapp.service.auth.modules.app.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.hukapp.service.auth.modules.app.dto.request.TrialNoteCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.TrialNoteUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.TrialNoteResponseDto;
import com.hukapp.service.auth.modules.app.entity.TrialNote;

@Mapper(componentModel = "spring")
public interface TrialNoteMapper {
    
    TrialNoteMapper INSTANCE = Mappers.getMapper(TrialNoteMapper.class);
    
    /**
     * Convert TrialNoteCreateDto to TrialNote entity
     * Ignores audit fields and owner as they are set separately
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "owner", ignore = true)
    TrialNote toEntity(TrialNoteCreateDto trialNoteCreateDto);
    
    /**
     * Convert TrialNoteUpdateDto to TrialNote entity
     * Ignores audit fields and owner as they are set separately
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "owner", ignore = true)
    TrialNote toEntity(TrialNoteUpdateDto trialNoteUpdateDto);
    
    /**
     * Convert TrialNote entity to TrialNoteResponseDto
     * Maps owner information to separate fields
     */
    @Mapping(target = "ownerName", expression = "java(trialNote.getOwner().getName() + ' ' + trialNote.getOwner().getSurname())")
    @Mapping(target = "ownerEmail", expression = "java(trialNote.getOwner().getEmail())")
    @Mapping(target = "ownerId", expression = "java(trialNote.getOwner().getId())")
    TrialNoteResponseDto toResponseDto(TrialNote trialNote);
    
    /**
     * Update existing TrialNote entity with data from TrialNoteUpdateDto
     * Preserves audit fields and owner
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "owner", ignore = true)
    void updateEntityFromDto(TrialNoteUpdateDto trialNoteUpdateDto, @MappingTarget TrialNote trialNote);
}
