package com.hukapp.service.auth.modules.report.service;

import com.hukapp.service.auth.modules.report.dto.response.CaseReportResponse;

public interface CaseReportService {

    /**
     * Generates a comprehensive report for a specific case
     *
     * @param caseNumber The case number to generate the report for
     * @param userEmail The email of the authenticated user
     * @return A detailed report containing case financial information and task statistics
     */
    CaseReportResponse generateCaseReport(String caseNumber, String userEmail);
}
