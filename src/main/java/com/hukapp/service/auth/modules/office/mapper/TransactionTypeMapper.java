package com.hukapp.service.auth.modules.office.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.office.dto.TransactionTypeRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionTypeResponse;
import com.hukapp.service.auth.modules.office.entity.TransactionType;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true)
)
public interface TransactionTypeMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    TransactionType toEntity(TransactionTypeRequest request);

    TransactionTypeResponse toDTO(TransactionType officeTransaction);

}