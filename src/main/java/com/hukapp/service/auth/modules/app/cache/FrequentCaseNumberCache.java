package com.hukapp.service.auth.modules.app.cache;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hukapp.service.auth.modules.app.dto.response.FrequentCaseNumberResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * A bean that provides a cache for frequently used case numbers
 * with automatic expiration after 1 hour.
 */
@Component
@Slf4j
public class FrequentCaseNumberCache {

    private static final long EXPIRATION_HOURS = 1;
    
    // Using ConcurrentHashMap for thread safety
    // Key: userEmail, Value: Map of caseNumber to CacheEntry
    private final Map<String, Map<String, CacheEntry>> cache = new ConcurrentHashMap<>();
    
    /**
     * Adds a case number to the cache for a specific user.
     * The entry will expire after 1 hour.
     * 
     * @param userEmail the user's email
     * @param caseNumber the case number
     * @param response the case number response
     */
    public void put(String userEmail, String caseNumber, FrequentCaseNumberResponse response) {
        Instant expirationTime = Instant.now().plus(EXPIRATION_HOURS, ChronoUnit.HOURS);
        
        // Get or create the user's cache
        Map<String, CacheEntry> userCache = cache.computeIfAbsent(userEmail, k -> new ConcurrentHashMap<>());
        
        // Add the case number to the user's cache
        userCache.put(caseNumber, new CacheEntry(response, expirationTime));
        
        log.debug("Added case number '{}' to cache for user '{}'. Will expire at {}", 
                caseNumber, userEmail, expirationTime);
    }
    
    /**
     * Retrieves a case number from the cache for a specific user.
     * 
     * @param userEmail the user's email
     * @param caseNumber the case number
     * @return the case number response, or null if not found or expired
     */
    public FrequentCaseNumberResponse get(String userEmail, String caseNumber) {
        Map<String, CacheEntry> userCache = cache.get(userEmail);
        if (userCache == null) {
            return null;
        }
        
        CacheEntry entry = userCache.get(caseNumber);
        if (entry == null) {
            return null;
        }
        
        // Check if the entry has expired
        if (entry.isExpired()) {
            userCache.remove(caseNumber);
            log.debug("Case number '{}' for user '{}' was expired during retrieval and has been removed", 
                    caseNumber, userEmail);
            return null;
        }
        
        return entry.getValue();
    }
    
    /**
     * Retrieves all case numbers from the cache for a specific user.
     * 
     * @param userEmail the user's email
     * @return list of case number responses, excluding expired entries
     */
    public List<FrequentCaseNumberResponse> getAllForUser(String userEmail) {
        Map<String, CacheEntry> userCache = cache.get(userEmail);
        if (userCache == null) {
            return new ArrayList<>();
        }
        
        // Filter out expired entries and collect the values
        return userCache.entrySet().stream()
                .filter(entry -> !entry.getValue().isExpired())
                .map(entry -> entry.getValue().getValue())
                .collect(Collectors.toList());
    }
    
    /**
     * Removes a case number from the cache for a specific user.
     * 
     * @param userEmail the user's email
     * @param caseNumber the case number
     */
    public void remove(String userEmail, String caseNumber) {
        Map<String, CacheEntry> userCache = cache.get(userEmail);
        if (userCache != null) {
            userCache.remove(caseNumber);
            log.debug("Removed case number '{}' from cache for user '{}'", caseNumber, userEmail);
        }
    }
    
    /**
     * Removes all case numbers from the cache for a specific user.
     * 
     * @param userEmail the user's email
     */
    public void removeAllForUser(String userEmail) {
        cache.remove(userEmail);
        log.debug("Removed all case numbers from cache for user '{}'", userEmail);
    }
    
    /**
     * Scheduled task that runs every 30 minutes to clean up expired entries.
     */
    @Scheduled(fixedRate = 1800000) // 30 minutes in milliseconds
    public void cleanupExpiredEntries() {
        int initialSize = 0;
        int removedCount = 0;
        
        // Count initial entries
        for (Map<String, CacheEntry> userCache : cache.values()) {
            initialSize += userCache.size();
        }
        
        // Remove expired entries from each user's cache
        for (Map.Entry<String, Map<String, CacheEntry>> userEntry : cache.entrySet()) {
            String userEmail = userEntry.getKey();
            Map<String, CacheEntry> userCache = userEntry.getValue();
            
            int userInitialSize = userCache.size();
            
            // Remove expired entries
            userCache.entrySet().removeIf(entry -> {
                boolean expired = entry.getValue().isExpired();
                if (expired) {
                    log.debug("Removing expired case number '{}' for user '{}'", 
                            entry.getKey(), userEmail);
                }
                return expired;
            });
            
            int userRemovedCount = userInitialSize - userCache.size();
            removedCount += userRemovedCount;
            
            // Remove user entry if all case numbers have been removed
            if (userCache.isEmpty()) {
                cache.remove(userEmail);
                log.debug("Removed empty cache for user '{}'", userEmail);
            }
        }
        
        if (removedCount > 0) {
            log.info("Cleaned up {} expired case number entries from cache. Initial size: {}, Current size: {}", 
                    removedCount, initialSize, initialSize - removedCount);
        }
    }
    
    /**
     * Inner class to store the case number response along with its expiration time.
     */
    private static class CacheEntry {
        private final FrequentCaseNumberResponse value;
        private final Instant expirationTime;
        
        public CacheEntry(FrequentCaseNumberResponse value, Instant expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }
        
        public FrequentCaseNumberResponse getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return Instant.now().isAfter(expirationTime);
        }
    }
}
