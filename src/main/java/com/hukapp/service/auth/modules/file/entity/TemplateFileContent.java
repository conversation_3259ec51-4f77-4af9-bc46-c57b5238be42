package com.hukapp.service.auth.modules.file.entity;

import com.hukapp.service.auth.common.entity.BaseEntity;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.MapsId;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Table(name = "template_file_content")
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class TemplateFileContent extends BaseEntity {

    @Id
    private Long id;

    @OneToOne
    @MapsId
    @JoinColumn(name = "id")
    private TemplateFileMetadata metadata;

    @Lob @Basic(fetch = FetchType.LAZY)
    @Column(nullable = false)
    private byte[] content;
    
}
