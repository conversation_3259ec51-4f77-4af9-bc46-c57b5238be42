package com.hukapp.service.auth.modules.sync.dto.request.uyap;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UyapSearchCaseRequest extends UyapSearchCaseBaseRequest {

    private int dosyaDurumKod;
    private int pageSize;
    private int pageNumber;
    private String birimTuru3;
    private String birimTuru2;
    private String birimId;

    /**
     * @deprecated Use {@link #caseSearchRequestByYargiBirimiAndTuru(String, String, int)} instead.
     */
    @Deprecated(forRemoval = true)
    public static UyapSearchCaseRequest activeCaseSearchRequest() {
        UyapSearchCaseRequest req = caseSearchRequestWithoutCaseStatus();
        req.setDosyaDurumKod(0);
        return req;
    }

    /**
     * @deprecated Use {@link #caseSearchRequestByYargiBirimiAndTuru(String, String, int)} instead.
     */
    @Deprecated(forRemoval = true)
    public static UyapSearchCaseRequest closedCaseSearchRequest() {
        UyapSearchCaseRequest req = caseSearchRequestWithoutCaseStatus();
        req.setDosyaDurumKod(1);
        return req;
    }

    /**
     * @deprecated Use {@link #caseSearchRequestByYargiBirimiAndTuru(String, String, int)} instead.
     */
    @Deprecated(forRemoval = true)
    private static UyapSearchCaseRequest caseSearchRequestWithoutCaseStatus() {
        return UyapSearchCaseRequest.builder()
                .pageSize(1000)
                .pageNumber(1)
                .build();
    }

    /**
     * birimTuru2: Yargi Birimi
     * birimTuru3: Yargi Turu
     * dosyaDurumKod: 0=active, 1=closed
     * 
     * @param birimTuru3
     * @param birimTuru2
     * @param dosyaDurumKod
     * @return
     */
    public static UyapSearchCaseRequest caseSearchRequestByYargiBirimiAndTuru(String birimTuru3, String birimTuru2,
            int dosyaDurumKod) {
        return UyapSearchCaseRequest.builder()
                .dosyaDurumKod(dosyaDurumKod)
                .pageSize(500)
                .pageNumber(1)
                .birimTuru3(birimTuru3)
                .birimTuru2(birimTuru2)
                .birimId("")
                .build();
    }

}
