package com.hukapp.service.auth.modules.app.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.common.dto.response.BaseResponse;
import com.hukapp.service.auth.modules.app.dto.request.FrequentCaseNumberRequest;
import com.hukapp.service.auth.modules.app.dto.response.FrequentCaseNumberResponse;
import com.hukapp.service.auth.modules.app.service.FrequentCaseNumberService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/frequent-case-numbers")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Frequent Case Numbers", description = "API for managing frequently used case numbers")
public class FrequentCaseNumberController {

    private final FrequentCaseNumberService frequentCaseNumberService;

    @PostMapping
    @Operation(
        summary = "Add a new case number to frequently used list",
        description = "Adds a new case number to the user's frequently used list. The case number must exist in the user's cases."
    )
    public ResponseEntity<FrequentCaseNumberResponse> addFrequentCaseNumber(
            @Valid @RequestBody FrequentCaseNumberRequest request,
            Authentication authentication) {
        
        log.debug("Adding case number to frequently used list: {}", request.getCaseNumber());
        FrequentCaseNumberResponse response = frequentCaseNumberService.addFrequentCaseNumber(
                request, authentication.getName());
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping
    @Operation(
        summary = "Get all frequently used case numbers",
        description = "Retrieves all frequently used case numbers for the authenticated user, ordered by last used timestamp."
    )
    public ResponseEntity<List<FrequentCaseNumberResponse>> getFrequentCaseNumbers(Authentication authentication) {
        
        log.debug("Retrieving frequently used case numbers for user: {}", authentication.getName());
        List<FrequentCaseNumberResponse> response = frequentCaseNumberService.getFrequentCaseNumbers(
                authentication.getName());
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get a specific frequently used case number",
        description = "Retrieves a specific frequently used case number by its ID."
    )
    public ResponseEntity<FrequentCaseNumberResponse> getFrequentCaseNumberById(
            @PathVariable Long id,
            Authentication authentication) {
        
        log.debug("Retrieving frequently used case number with ID: {}", id);
        FrequentCaseNumberResponse response = frequentCaseNumberService.getFrequentCaseNumberById(
                id, authentication.getName());
        
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a frequently used case number",
        description = "Deletes a specific frequently used case number by its ID."
    )
    public ResponseEntity<BaseResponse> deleteFrequentCaseNumber(
            @PathVariable Long id,
            Authentication authentication) {
        
        log.debug("Deleting frequently used case number with ID: {}", id);
        frequentCaseNumberService.deleteFrequentCaseNumber(id, authentication.getName());
        
        BaseResponse response = BaseResponse.builder()
                .responseMessage("Sık kullanılan dosya numarası başarıyla silindi")
                .build();
        
        return ResponseEntity.ok(response);
    }
}
