package com.hukapp.service.auth.modules.app.dto.response;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.modules.app.enums.CaseType;
import com.hukapp.service.auth.modules.app.enums.CrimeType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response object for case details operations")
public class CaseDetailsResponse {

    private Long id;
    private String caseNumber;
    private CaseType caseType;
    private CrimeType crimeType;
    private boolean derdest;
    private BigDecimal caseValue;
    private String caseReason;
    private String caseTitle;
    private Instant createdAt;
    private Instant updatedAt;
    private long ownerId;
    private String ownerName;
    private String ownerEmail;
}
