package com.hukapp.service.auth.modules.compensation.service;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.modules.compensation.dto.CompensationRequest;
import com.hukapp.service.auth.modules.compensation.dto.CompensationResponse;

@Service
public interface CompensationService {

    CompensationResponse calculateCompensation(CompensationRequest compensationRequest);

}
