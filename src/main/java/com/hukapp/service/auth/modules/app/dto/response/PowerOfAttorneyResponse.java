package com.hukapp.service.auth.modules.app.dto.response;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

import com.hukapp.service.auth.modules.office.dto.response.ClientResponseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response object for power of attorney operations")
public class PowerOfAttorneyResponse {
    
    private Long id;
    private String powerOfAttorneyNumber;
    private String notaryName;
    private List<ClientResponseDto> clients;
    private List<String> lawyerList;
    private List<String> powerList;
    private String yevmiyeNo;
    private LocalDate startDate;
    private LocalDate endDate;
    private List<String> caseNumbers;
    private Instant createdAt;
    private Instant updatedAt;
    private long ownerId;
    private String ownerName;
    private String ownerEmail;
}
