package com.hukapp.service.auth.modules.email.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import lombok.extern.slf4j.Slf4j;

/**
 * Email configuration for the application.
 * Uses Spring Boot's auto-configured JavaMailSender with properties from application.yml.
 * Supports various SMTP server configurations.
 */
@Configuration
@Slf4j
public class EmailConfig {

    private final EmailProperties emailProperties;

    public EmailConfig(EmailProperties emailProperties) {
        this.emailProperties = emailProperties;
    }

    @EventListener
    public void onApplicationEvent(ContextRefreshedEvent event) {
        log.info("Email configuration initialized with host: {}, port: {}",
                emailProperties.host(), emailProperties.port());
        log.info("Email security settings - auth: {}, SSL: {}, StartTLS: {}",
                emailProperties.auth(), emailProperties.ssl(), emailProperties.startTls());
        log.info("Email debug mode: {}", emailProperties.debug());
    }
}
