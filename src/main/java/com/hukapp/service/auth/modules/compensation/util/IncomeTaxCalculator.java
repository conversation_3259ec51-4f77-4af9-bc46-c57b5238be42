package com.hukapp.service.auth.modules.compensation.util;

import org.springframework.stereotype.Component;

/**
 * <PERSON><PERSON><PERSON> vergisi hesaplama sınıfı.
 * <PERSON><PERSON> sınıf, yeni bir gelir için vergi hesaplamak amacıyla kull<PERSON>lı<PERSON>.
 * <PERSON><PERSON><PERSON> he<PERSON>ı, gelir eşiği ve oranlarına göre yapılır.
 */
@Component
public class IncomeTaxCalculator {

    private IncomeTaxCalculator() {
        // Private constructor to prevent instantiation
    }

    /**
     * Calculates the tax for a new income given the cumulative income so far.
     *
     * @param cumulativeIncome The total income accumulated before the new income.
     * @param newIncome        The new income amount to be taxed.
     * @return The calculated tax on the new income.
     */
    public static double calculateTax(double cumulativeIncome, double newIncome, double[] thresholds, double[] rates) {
        double tax = 0.0;
        double remainingIncome = newIncome;

        // Iterate through each tax bracket
        double currentIncome = cumulativeIncome;
        for (int i = 0; i < thresholds.length && remainingIncome > 0; i++) {
            // If the cumulative income is already above the current threshold,
            // skip to the next bracket.
            if (currentIncome >= thresholds[i]) {
                continue;
            }
            // Calculate the available space in the current bracket.
            double bracketSpace = thresholds[i] - currentIncome;
            // Determine the taxable amount in this bracket.
            double taxableAmount = Math.min(remainingIncome, bracketSpace);
            tax += taxableAmount * rates[i];
            // Update the cumulative income and the remaining new income.
            currentIncome += taxableAmount;
            remainingIncome -= taxableAmount;
        }
        // If there is any remaining income beyond the last threshold,
        // tax it at the highest rate.
        if (remainingIncome > 0) {
            tax += remainingIncome * rates[rates.length - 1];
        }

        return tax;
    }

}
