package com.hukapp.service.auth.modules.app.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for Power of Attorney file download containing file content and metadata
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Power of Attorney file download response with content and metadata")
public class PoaFileDownloadDto {

    @Schema(description = "Original filename for download", example = "power_of_attorney.pdf")
    private String filename;

    @Schema(description = "File content type/MIME type", example = "application/pdf")
    private String contentType;

    @Schema(description = "File size in bytes", example = "1048576")
    private Long fileSize;

    @Schema(description = "File content as byte array")
    private byte[] content;

    @Schema(description = "MD5 hash for integrity verification", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5Hash;

    @Schema(description = "Power of Attorney ID this file belongs to", example = "1")
    private Long powerOfAttorneyId;

    @Schema(description = "Power of Attorney number", example = "POA-2023-12345")
    private String powerOfAttorneyNumber;
}
