package com.hukapp.service.auth.modules.email.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.email.dto.request.EmailRequest;
import com.hukapp.service.auth.modules.email.exception.EmailSendException;
import com.hukapp.service.auth.modules.email.service.EmailService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/email")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Email", description = "Email API")
public class EmailController {

    // Response constants
    private static final String KEY_MESSAGE = "message";
    private static final String KEY_ERROR = "error";
    private static final String KEY_OTP = "otp";

    private static final String MSG_EMAIL_SENT = "Email sent successfully";
    private static final String MSG_HTML_EMAIL_SENT = "HTML email sent successfully";
    private static final String MSG_OTP_EMAIL_SENT = "OTP email sent successfully";

    private static final String ERROR_PREFIX_EMAIL_SEND = "Failed to send email: ";
    private static final String ERROR_PREFIX_UNEXPECTED = "Unexpected error: ";

    private static final Random RANDOM = new Random();

    private final EmailService emailService;

    @Operation(summary = "Send a test email", description = "Sends a test email to the specified address")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email sent successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request"),
            @ApiResponse(responseCode = "500", description = "Failed to send email")
    })
    @PostMapping("/test")
    public ResponseEntity<Map<String, String>> sendTestEmail(@Valid @RequestBody EmailRequest request) {
        log.debug("Received request to send test email to: {}", request.getTo());

        try {
            // Log detailed information about the email being sent
            log.info("Attempting to send test email to: {}", request.getTo());

            emailService.sendSimpleEmail(
                request.getTo(),
                "Test Email from HukApp",
                "This is a test email from HukApp. If you received this, the email service is working correctly."
            );

            log.info("Successfully sent test email to: {}", request.getTo());

            Map<String, String> response = new HashMap<>();
            response.put(KEY_MESSAGE, MSG_EMAIL_SENT);
            return ResponseEntity.ok(response);
        } catch (EmailSendException e) {
            log.error("Failed to send test email to: {} - {}", request.getTo(), e.getMessage(), e);
            Map<String, String> response = new HashMap<>();
            response.put(KEY_ERROR, ERROR_PREFIX_EMAIL_SEND + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        } catch (Exception e) {
            log.error("Unexpected error when sending test email to: {} - {}", request.getTo(), e.getMessage(), e);
            Map<String, String> response = new HashMap<>();
            response.put(KEY_ERROR, ERROR_PREFIX_UNEXPECTED + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "Send a test HTML email", description = "Sends a test HTML email to the specified address")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email sent successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request"),
            @ApiResponse(responseCode = "500", description = "Failed to send email")
    })
    @PostMapping("/test-html")
    public ResponseEntity<Map<String, String>> sendTestHtmlEmail(@Valid @RequestBody EmailRequest request) {
        log.debug("Received request to send test HTML email to: {}", request.getTo());

        try {
            String htmlContent = "<html><body>" +
                    "<h1>Test HTML Email from HukApp</h1>" +
                    "<p>This is a <strong>test HTML email</strong> from HukApp.</p>" +
                    "<p>If you received this, the email service is working correctly.</p>" +
                    "</body></html>";

            emailService.sendHtmlEmail(request.getTo(), "Test HTML Email from HukApp", htmlContent);

            Map<String, String> response = new HashMap<>();
            response.put(KEY_MESSAGE, MSG_HTML_EMAIL_SENT);
            return ResponseEntity.ok(response);
        } catch (EmailSendException e) {
            log.error("Failed to send test HTML email", e);
            Map<String, String> response = new HashMap<>();
            response.put(KEY_ERROR, ERROR_PREFIX_EMAIL_SEND + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        } catch (Exception e) {
            log.error("Unexpected error when sending test HTML email", e);
            Map<String, String> response = new HashMap<>();
            response.put(KEY_ERROR, ERROR_PREFIX_UNEXPECTED + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @Operation(summary = "Send a test OTP email", description = "Sends a test OTP email to the specified address")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email sent successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request"),
            @ApiResponse(responseCode = "500", description = "Failed to send email")
    })
    @PostMapping("/test-otp")
    public ResponseEntity<Map<String, String>> sendTestOtpEmail(@Valid @RequestBody EmailRequest request) {
        log.debug("Received request to send test OTP email to: {}", request.getTo());

        try {
            // Generate a random 6-digit OTP for testing
            String testOtp = String.format("%06d", RANDOM.nextInt(1000000));

            emailService.sendOtpEmail(request.getTo(), testOtp);

            Map<String, String> response = new HashMap<>();
            response.put(KEY_MESSAGE, MSG_OTP_EMAIL_SENT);
            response.put(KEY_OTP, testOtp); // Include the OTP in the response for testing purposes
            return ResponseEntity.ok(response);
        } catch (EmailSendException e) {
            log.error("Failed to send test OTP email", e);
            Map<String, String> response = new HashMap<>();
            response.put(KEY_ERROR, ERROR_PREFIX_EMAIL_SEND + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        } catch (Exception e) {
            log.error("Unexpected error when sending test OTP email", e);
            Map<String, String> response = new HashMap<>();
            response.put(KEY_ERROR, ERROR_PREFIX_UNEXPECTED + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
