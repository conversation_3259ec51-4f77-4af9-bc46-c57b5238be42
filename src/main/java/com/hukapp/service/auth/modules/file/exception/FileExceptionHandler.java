package com.hukapp.service.auth.modules.file.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import com.hukapp.service.auth.common.dto.response.BaseResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Global exception handler for file-related exceptions
 */
@RestControllerAdvice
@Slf4j
public class FileExceptionHandler {

    @ExceptionHandler(FileValidationException.class)
    public ResponseEntity<BaseResponse> handleFileValidationException(FileValidationException e) {
        log.warn("File validation failed: {}", e.getMessage());
        
        BaseResponse response = BaseResponse.builder()
            .responseMessage(e.getMessage())
            .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(FileUploadException.class)
    public ResponseEntity<BaseResponse> handleFileUploadException(FileUploadException e) {
        log.error("File upload failed: {}", e.getMessage(), e);
        
        BaseResponse response = BaseResponse.builder()
            .responseMessage("File upload failed: " + e.getMessage())
            .build();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    @ExceptionHandler(FileAccessDeniedException.class)
    public ResponseEntity<BaseResponse> handleFileAccessDeniedException(FileAccessDeniedException e) {
        log.warn("File access denied: {}", e.getMessage());
        
        BaseResponse response = BaseResponse.builder()
            .responseMessage("Access denied: " + e.getMessage())
            .build();
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<BaseResponse> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.warn("File size exceeds maximum allowed: {}", e.getMessage());
        
        BaseResponse response = BaseResponse.builder()
            .responseMessage("File size exceeds maximum allowed limit")
            .build();
        
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
    }
}
