package com.hukapp.service.auth.modules.office.entity;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.office.enums.AccountingType;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity representing client-specific accounting records
 * Tracks three types of financial records: RECEIVED, RECEIVABLE, REFUND
 */
@Entity
@Table(name = "client_accounting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ClientAccounting extends BaseEntity {

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private AccountingType accountingType;

    @Column(nullable = false, precision = 19, scale = 2)
    private BigDecimal amount;

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String description;

    @Column(nullable = false)
    private Instant recordDate;

    private String caseNumber;

    @ManyToOne
    @JoinColumn(name = "client_id")
    private Client client;

    @ManyToOne(optional = false)
    @JoinColumn(name = "owner_id", nullable = false)
    private Person owner;

}
