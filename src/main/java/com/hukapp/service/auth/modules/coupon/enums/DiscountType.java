package com.hukapp.service.auth.modules.coupon.enums;

/**
 * Enum representing the type of discount a coupon can provide
 */
public enum DiscountType {
    /**
     * Percentage-based discount (e.g., 10% off)
     */
    PERCENTAGE("Percentage"),
    
    /**
     * Fixed amount discount (e.g., 50 TL off)
     */
    FIXED_AMOUNT("Fixed Amount");
    
    private final String displayName;
    
    DiscountType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
}
