package com.hukapp.service.auth.modules.file.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for file download containing file content and metadata
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "File download response with content and metadata")
public class FileDownloadDto {

    @Schema(description = "Original filename for download", example = "contract.pdf")
    private String filename;

    @Schema(description = "File content type/MIME type", example = "application/pdf")
    private String contentType;

    @Schema(description = "File size in bytes", example = "1048576")
    private Long fileSize;

    @Schema(description = "File content as byte array")
    private byte[] content;

    @Schema(description = "MD5 hash for integrity verification", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5Hash;

    /**
     * Constructor without content for metadata-only responses
     */
    public FileDownloadDto(String filename, String contentType, Long fileSize, String md5Hash) {
        this.filename = filename;
        this.contentType = contentType;
        this.fileSize = fileSize;
        this.md5Hash = md5Hash;
    }

    /**
     * Checks if the file content is available
     * @return true if content is not null and not empty
     */
    public boolean hasContent() {
        return content != null && content.length > 0;
    }

    /**
     * Gets the file extension from the filename
     * @return the file extension or empty string if none
     */
    public String getFileExtension() {
        if (filename == null) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * Gets a human-readable file size
     * @return formatted file size string
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "Unknown";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
