package com.hukapp.service.auth.modules.app.service;

import java.util.List;

import com.hukapp.service.auth.modules.app.dto.request.CaseDetailsRequest;
import com.hukapp.service.auth.modules.app.dto.response.CaseDetailsResponse;

public interface CaseDetailsService {
    
    // Create new case details
    CaseDetailsResponse createCaseDetails(CaseDetailsRequest caseDetailsRequest, String userEmail);
    
    // Get all case details for the authenticated user
    List<CaseDetailsResponse> getAllCaseDetails(String userEmail);
    
    // Get case details by ID
    CaseDetailsResponse getCaseDetailsById(Long id, String userEmail);
    
    // Get case details by case number
    CaseDetailsResponse getCaseDetailsByCaseNumber(String caseNumber, String userEmail);
    
    // Update case details
    CaseDetailsResponse updateCaseDetails(Long id, CaseDetailsRequest caseDetailsRequest, String userEmail);
    
    // Delete case details
    void deleteCaseDetails(Long id, String userEmail);
}
