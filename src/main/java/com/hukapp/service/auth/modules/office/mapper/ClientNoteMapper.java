package com.hukapp.service.auth.modules.office.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.hukapp.service.auth.modules.office.dto.request.ClientNoteRequest;
import com.hukapp.service.auth.modules.office.dto.response.ClientNoteResponse;
import com.hukapp.service.auth.modules.office.entity.ClientNote;

@Mapper(componentModel = "spring")
public interface ClientNoteMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "client", ignore = true)
    @Mapping(target = "owner", ignore = true)
    ClientNote toEntity(ClientNoteRequest clientNoteRequest);
    
    @Mapping(target = "clientId", expression = "java(clientNote.getClient().getId())")
    @Mapping(target = "clientName", expression = "java(clientNote.getClient().getName())")
    @Mapping(target = "ownerName", expression = "java(clientNote.getOwner().getName() + ' ' + clientNote.getOwner().getSurname())")
    @Mapping(target = "ownerEmail", expression = "java(clientNote.getOwner().getEmail())")
    @Mapping(target = "ownerId", expression = "java(clientNote.getOwner().getId())")
    ClientNoteResponse toDTO(ClientNote clientNote);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "client", ignore = true)
    @Mapping(target = "owner", ignore = true)
    void updateEntityFromDto(ClientNoteRequest clientNoteRequest, @MappingTarget ClientNote clientNote);
}
