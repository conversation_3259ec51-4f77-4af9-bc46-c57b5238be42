package com.hukapp.service.auth.modules.office.entity;

import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.office.enums.ClientType;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity representing a client (individual or corporate)
 * 
 * name and identityOrTaxNumber are unique for each user
 */
@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Table(name = "clients", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"name", "owner_id"})
})
public class Client extends BaseEntity {

    @Column(nullable = false)
    private String name;

    private Long identityOrTaxNumber;

    @Column(columnDefinition = "text")
    @Convert(converter = StringEncryptionConverter.class)
    private String address;

    @Convert(converter = StringEncryptionConverter.class)
    private String phoneNumber;

    private String email;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ClientType clientType;

    @ManyToOne(optional = false)
    @JoinColumn(name = "owner_id", nullable = false)
    private Person owner;

}
