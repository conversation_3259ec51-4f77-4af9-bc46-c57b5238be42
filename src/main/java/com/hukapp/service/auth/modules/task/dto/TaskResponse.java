package com.hukapp.service.auth.modules.task.dto;

import java.time.Instant;
import java.util.List;

import com.hukapp.service.auth.modules.task.entity.Task.TaskType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response for creating or updating a task")
public class TaskResponse extends TaskRequest {

    private Long id;
    private Instant startDate;
    private Instant dueDate;
    private Instant createdAt;
    private Instant updatedAt;
    private long reporter;

    @Schema(description = "Client ID associated with the task", example = "1")
    private Long clientId;

    private List<NoteResponse> notes;
    private TaskType taskType;
}
