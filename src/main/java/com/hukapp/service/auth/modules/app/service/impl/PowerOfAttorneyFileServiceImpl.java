package com.hukapp.service.auth.modules.app.service.impl;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.app.dto.request.PoaFileUploadDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileDownloadDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileListDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileResponseDto;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorney;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorneyFileContent;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorneyFileMetadata;
import com.hukapp.service.auth.modules.app.mapper.PowerOfAttorneyFileMapper;
import com.hukapp.service.auth.modules.app.repository.PowerOfAttorneyFileContentRepository;
import com.hukapp.service.auth.modules.app.repository.PowerOfAttorneyFileRepository;
import com.hukapp.service.auth.modules.app.repository.PowerOfAttorneyRepository;
import com.hukapp.service.auth.modules.app.service.PowerOfAttorneyFileService;
import com.hukapp.service.auth.modules.file.enums.FileType;
import com.hukapp.service.auth.modules.file.exception.FileUploadException;
import com.hukapp.service.auth.modules.file.exception.FileValidationException;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Implementation of PowerOfAttorneyFileService for file management operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PowerOfAttorneyFileServiceImpl implements PowerOfAttorneyFileService {

    private final PowerOfAttorneyFileRepository fileRepository;
    private final PowerOfAttorneyFileContentRepository fileContentRepository;
    private final PowerOfAttorneyRepository powerOfAttorneyRepository;
    private final PowerOfAttorneyFileMapper fileMapper;
    private final PersonService personService;

    // File validation constants
    private static final long MAX_FILE_SIZE = 10L * 1024 * 1024; // 10MB
    private static final String[] ALLOWED_EXTENSIONS = {"pdf", "doc", "docx", "jpg", "jpeg", "png"};
    private static final String[] ALLOWED_MIME_TYPES = {
        "application/pdf", 
        "application/msword", 
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "image/jpeg", 
        "image/jpg", 
        "image/png"
    };
    
    private static final String FILE_NOT_FOUND_MESSAGE = "Power of Attorney file not found with ID: ";
    private static final String POA_NOT_FOUND_MESSAGE = "Power of Attorney not found with ID: ";
    
    @Override
    public PoaFileResponseDto uploadFile(PoaFileUploadDto poaFileUploadDto, String uploaderEmail) {
        log.info("Uploading Power of Attorney file for POA ID: {} by user: {}", 
            poaFileUploadDto.getPowerOfAttorneyId(), uploaderEmail);
        
        MultipartFile file = poaFileUploadDto.getFile();
        validateFileUpload(file);
        
        try {
            // Get uploader
            Person uploader = personService.getPersonByEmailOrElseThrow(uploaderEmail);
            
            // Get and validate Power of Attorney
            PowerOfAttorney powerOfAttorney = powerOfAttorneyRepository
                .findByIdAndOwner(poaFileUploadDto.getPowerOfAttorneyId(), uploader)
                .orElseThrow(() -> new ResourceNotFoundException(
                    POA_NOT_FOUND_MESSAGE + poaFileUploadDto.getPowerOfAttorneyId()));
            
            // Generate file content hash
            byte[] fileContent = file.getBytes();
            String md5Hash = generateMD5Hash(fileContent);
            
            // Check for duplicate files (optional - log warning but allow)
            if (fileRepository.existsByMd5Hash(md5Hash)) {
                log.warn("Duplicate file detected with MD5: {} for POA ID: {}", 
                    md5Hash, poaFileUploadDto.getPowerOfAttorneyId());
            }
            
            // Determine file type
            FileType fileType = FileType.fromFilename(file.getOriginalFilename());
            if (fileType == FileType.OTHER) {
                fileType = FileType.fromMimeType(file.getContentType());
            }
            
            // Generate unique stored filename
            String storedFilename = generateUniqueFilename(file.getOriginalFilename());
            
            // Create file entity
            PowerOfAttorneyFileMetadata fileEntity = PowerOfAttorneyFileMetadata.builder()
                .powerOfAttorney(powerOfAttorney)
                .originalFilename(file.getOriginalFilename())
                .storedFilename(storedFilename)
                .contentType(file.getContentType())
                .fileSize(file.getSize())
                .fileType(fileType)
                .uploader(uploader)
                .description(poaFileUploadDto.getDescription())
                .tags(poaFileUploadDto.getTags())
                .md5Hash(md5Hash)
                .downloadCount(0L)
                .markedForDeletion(false)
                .build();
            
            fileRepository.save(fileEntity);

            // Create file content entity
            PowerOfAttorneyFileContent fileContentEntity = PowerOfAttorneyFileContent.builder()
                .metadata(fileEntity)
                .content(fileContent)
                .build();

            fileContentRepository.save(fileContentEntity);
            
            log.info("Power of Attorney file uploaded successfully with ID: {} for POA ID: {} by user: {}", 
                fileEntity.getId(), poaFileUploadDto.getPowerOfAttorneyId(), uploaderEmail);
            
            return fileMapper.toResponseDto(fileEntity);
            
        } catch (IOException e) {
            log.error("Error reading file content during upload: {}", e.getMessage(), e);
            throw new FileUploadException("Failed to read file content", e);
        } catch (Exception e) {
            log.error("Error during Power of Attorney file upload: {}", e.getMessage(), e);
            throw new FileUploadException("Power of Attorney file upload failed", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PoaFileDownloadDto downloadFile(Long fileId, String userEmail) {
        log.info("Downloading Power of Attorney file ID: {} for user: {}", fileId, userEmail);
        
        Person user = personService.getPersonByEmailOrElseThrow(userEmail);
        
        PowerOfAttorneyFileMetadata fileEntity = fileRepository.findByIdAndUploaderIncludingContent(fileId, user)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));
        
        // Update download statistics
        fileEntity.incrementDownloadCount();
        fileRepository.save(fileEntity);
        
        log.info("Power of Attorney file downloaded successfully: {} by user: {}", 
            fileEntity.getOriginalFilename(), userEmail);
        
        return fileMapper.toDownloadDto(fileEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public PoaFileDownloadDto getFileMetadata(Long fileId, String userEmail) {
        log.info("Getting Power of Attorney file metadata for ID: {} by user: {}", fileId, userEmail);
        
        Person user = personService.getPersonByEmailOrElseThrow(userEmail);
        
        PowerOfAttorneyFileMetadata fileEntity = fileRepository.findByIdAndUploaderExcludingContent(fileId, user)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));
        
        return fileMapper.toDownloadDtoWithoutContent(fileEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PoaFileListDto> listFilesByPowerOfAttorney(Long powerOfAttorneyId, String userEmail) {
        log.info("Listing Power of Attorney files for POA ID: {} by user: {}", powerOfAttorneyId, userEmail);
        
        Person user = personService.getPersonByEmailOrElseThrow(userEmail);
        
        // Verify the Power of Attorney belongs to the user
        powerOfAttorneyRepository.findByIdAndOwner(powerOfAttorneyId, user)
            .orElseThrow(() -> new ResourceNotFoundException(POA_NOT_FOUND_MESSAGE + powerOfAttorneyId));
        
        List<PowerOfAttorneyFileMetadata> files = fileRepository.findByPowerOfAttorneyIdAndUploader(powerOfAttorneyId, user);
        
        log.info("Found {} Power of Attorney files for POA ID: {} by user: {}", 
            files.size(), powerOfAttorneyId, userEmail);
        
        return fileMapper.toListDtoList(files);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PoaFileListDto> listUserFiles(String userEmail) {
        log.info("Listing all Power of Attorney files for user: {}", userEmail);
        
        Person user = personService.getPersonByEmailOrElseThrow(userEmail);
        
        List<PowerOfAttorneyFileMetadata> files = fileRepository.findByUploader(user);
        
        log.info("Found {} Power of Attorney files for user: {}", files.size(), userEmail);
        
        return fileMapper.toListDtoList(files);
    }

    @Override
    public void deleteFile(Long fileId, String userEmail) {
        log.info("Deleting Power of Attorney file ID: {} by user: {}", fileId, userEmail);

        Person user = personService.getPersonByEmailOrElseThrow(userEmail);

        PowerOfAttorneyFileMetadata fileEntity = fileRepository.findByIdAndUploaderExcludingContent(fileId, user)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));

        // Hard delete - remove both metadata and content
        fileContentRepository.deleteById(fileId);
        fileRepository.deleteById(fileId);

        log.info("Power of Attorney file deleted successfully: {} by user: {}",
            fileEntity.getOriginalFilename(), userEmail);
    }

    @Override
    @Transactional(readOnly = true)
    public PoaFileResponseDto getFileDetails(Long fileId, String userEmail) {
        log.info("Getting Power of Attorney file details for ID: {} by user: {}", fileId, userEmail);

        Person user = personService.getPersonByEmailOrElseThrow(userEmail);

        PowerOfAttorneyFileMetadata fileEntity = fileRepository.findByIdAndUploaderExcludingContent(fileId, user)
            .orElseThrow(() -> new ResourceNotFoundException(FILE_NOT_FOUND_MESSAGE + fileId));

        return fileMapper.toResponseDto(fileEntity);
    }

    @Override
    public void validateFileUpload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new FileValidationException("File cannot be null or empty");
        }

        // Check file size
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new FileValidationException(
                String.format("File size exceeds maximum allowed size of %d MB", MAX_FILE_SIZE / (1024 * 1024)));
        }

        // Check file extension
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new FileValidationException("File must have a valid filename");
        }

        String fileExtension = getFileExtension(originalFilename).toLowerCase();
        if (!Arrays.asList(ALLOWED_EXTENSIONS).contains(fileExtension)) {
            throw new FileValidationException(
                String.format("File type '%s' is not allowed. Allowed types: %s",
                    fileExtension, String.join(", ", ALLOWED_EXTENSIONS)));
        }

        // Check MIME type
        String contentType = file.getContentType();
        if (contentType == null || !Arrays.asList(ALLOWED_MIME_TYPES).contains(contentType)) {
            throw new FileValidationException(
                String.format("Content type '%s' is not allowed. Allowed types: %s",
                    contentType, String.join(", ", ALLOWED_MIME_TYPES)));
        }

        log.debug("File validation passed for: {}", originalFilename);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PoaFileListDto> searchFilesByFilename(String filename, String userEmail) {
        log.info("Searching Power of Attorney files by filename: '{}' for user: {}", filename, userEmail);

        Person user = personService.getPersonByEmailOrElseThrow(userEmail);

        List<PowerOfAttorneyFileMetadata> files = fileRepository
            .findByUploaderAndOriginalFilenameContainingIgnoreCase(user, filename);

        log.info("Found {} Power of Attorney files matching filename: '{}' for user: {}",
            files.size(), filename, userEmail);

        return fileMapper.toListDtoList(files);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PoaFileListDto> searchFilesByTag(String tag, String userEmail) {
        log.info("Searching Power of Attorney files by tag: '{}' for user: {}", tag, userEmail);

        Person user = personService.getPersonByEmailOrElseThrow(userEmail);

        List<PowerOfAttorneyFileMetadata> files = fileRepository
            .findByUploaderAndTagsContainingIgnoreCase(user, tag);

        log.info("Found {} Power of Attorney files matching tag: '{}' for user: {}",
            files.size(), tag, userEmail);

        return fileMapper.toListDtoList(files);
    }

    @Override
    @Transactional(readOnly = true)
    public long getFileCountByPowerOfAttorney(Long powerOfAttorneyId, String userEmail) {
        log.info("Getting file count for Power of Attorney ID: {} by user: {}", powerOfAttorneyId, userEmail);

        Person user = personService.getPersonByEmailOrElseThrow(userEmail);

        // Verify the Power of Attorney belongs to the user
        powerOfAttorneyRepository.findByIdAndOwner(powerOfAttorneyId, user)
            .orElseThrow(() -> new ResourceNotFoundException(POA_NOT_FOUND_MESSAGE + powerOfAttorneyId));

        return fileRepository.countByPowerOfAttorneyIdAndUploader(powerOfAttorneyId, user);
    }

    @Override
    @Transactional(readOnly = true)
    public long getTotalFileCount(String userEmail) {
        log.info("Getting total file count for user: {}", userEmail);

        Person user = personService.getPersonByEmailOrElseThrow(userEmail);

        return fileRepository.countByUploader(user);
    }

    /**
     * Generates MD5 hash for file content
     */
    private String generateMD5Hash(byte[] content) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(content);
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5 algorithm not available", e);
            return UUID.randomUUID().toString().replace("-", "");
        }
    }

    /**
     * Generates unique filename for storage
     */
    private String generateUniqueFilename(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String timestamp = String.valueOf(Instant.now().toEpochMilli());
        String uuid = UUID.randomUUID().toString().substring(0, 8);

        return String.format("poa_%s_%s.%s", timestamp, uuid, extension);
    }

    /**
     * Extracts file extension from filename
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }

        return filename.substring(lastDotIndex + 1);
    }
}
