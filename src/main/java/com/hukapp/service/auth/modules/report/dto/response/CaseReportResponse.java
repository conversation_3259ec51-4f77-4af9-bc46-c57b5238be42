package com.hukapp.service.auth.modules.report.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.hukapp.service.auth.modules.office.dto.TransactionResponse;
import com.hukapp.service.auth.modules.task.dto.TaskResponse;
import com.hukapp.service.auth.modules.task.entity.Task.Priority;
import com.hukapp.service.auth.modules.task.entity.Task.Status;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
@Schema(description = "Comprehensive case report containing financial data and task statistics")
public class CaseReportResponse {

    // Case information
    @Schema(description = "Case number")
    private String caseNumber;

    // Financial information
    @Schema(description = "Summary of case's financial information including income, expenses, and net income")
    private FinancialSummarySection financialSummary;

    @Schema(description = "List of income and expense transactions for the case")
    private List<TransactionResponse> transactions;

    // Task information
    @Schema(description = "Summary of case's tasks grouped by status and priority")
    private TaskSummarySection taskSummary;

    @Schema(description = "List of tasks for the case")
    private List<TaskResponse> tasks;

    // Report metadata
    @Schema(description = "Timestamp when the report was generated")
    private LocalDateTime reportGeneratedAt;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    @Schema(description = "Summary of case's financial information")
    public static class FinancialSummarySection {
        @Schema(description = "Total income amount for the case")
        private BigDecimal totalIncome;

        @Schema(description = "Total expenses amount for the case")
        private BigDecimal totalExpenses;

        @Schema(description = "Net income (income - expenses) for the case")
        private BigDecimal netIncome;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    @Schema(description = "Summary of case's tasks with counts by status and priority")
    public static class TaskSummarySection {
        @Schema(description = "Total number of tasks for the case")
        private int totalTasks;

        @Schema(description = "Count of tasks grouped by status (OPEN, IN_PROGRESS, COMPLETED, CANCELLED)")
        private Map<Status, Integer> taskCountsByStatus;

        @Schema(description = "Count of tasks grouped by priority (LOW, MEDIUM, HIGH, CRITICAL)")
        private Map<Priority, Integer> taskCountsByPriority;
    }
}
