package com.hukapp.service.auth.modules.file.dto.request;

import com.hukapp.service.auth.modules.file.enums.FileType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for file filtering parameters
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "File filtering parameters")
public class FileFilterDto {

    @Schema(description = "Sort field", example = "createdAt", defaultValue = "createdAt")
    private String sortBy = "createdAt";

    @Schema(description = "Sort direction", example = "desc", defaultValue = "desc", allowableValues = {"asc", "desc"})
    private String sortDirection = "desc";

    @Schema(description = "Filter by file type", example = "DOCUMENT")
    private FileType fileType;

    @Schema(description = "Filter by filename (partial match)", example = "contract")
    private String filename;

    @Schema(description = "Filter by content type", example = "application/pdf")
    private String contentType;

    @Schema(description = "Filter by uploader email", example = "<EMAIL>")
    private String uploaderEmail;

    @Schema(description = "Filter by tags (partial match)", example = "legal")
    private String tags;

    @Schema(description = "Filter by description (partial match)", example = "important")
    private String description;

    @Schema(description = "Filter by minimum file size in bytes", example = "1024")
    private Long minFileSize;

    @Schema(description = "Filter by maximum file size in bytes", example = "10485760")
    private Long maxFileSize;

    @Schema(description = "Filter by public accessibility", example = "false")
    private Boolean isPublic;

    /**
     * Validates that maxFileSize is greater than minFileSize if both are provided
     * @return true if validation passes
     */
    public boolean isFileSizeRangeValid() {
        if (minFileSize != null && maxFileSize != null) {
            return maxFileSize >= minFileSize;
        }
        return true;
    }

    /**
     * Checks if sort direction is valid
     * @return true if sort direction is valid
     */
    public boolean isSortDirectionValid() {
        return "asc".equalsIgnoreCase(sortDirection) || "desc".equalsIgnoreCase(sortDirection);
    }

    /**
     * Gets the normalized sort direction
     * @return "asc" or "desc"
     */
    public String getNormalizedSortDirection() {
        return "asc".equalsIgnoreCase(sortDirection) ? "asc" : "desc";
    }
}
