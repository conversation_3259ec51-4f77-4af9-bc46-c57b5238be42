package com.hukapp.service.auth.modules.person.service;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Component;

import com.hukapp.service.auth.modules.person.dto.request.PersonCreateRequest;
import com.hukapp.service.auth.modules.person.dto.request.PersonLoginRequest;
import com.hukapp.service.auth.modules.person.dto.request.VerifyEmailRequest;
import com.hukapp.service.auth.modules.person.dto.response.PersonCreateResponse;
import com.hukapp.service.auth.modules.person.dto.response.PersonLoginResponse;
import com.hukapp.service.auth.modules.person.dto.response.VerifyEmailResponse;
import com.hukapp.service.auth.modules.person.entity.Person;

@Component
public interface PersonService {
    /**
     * Initiates the person creation process by sending a verification email
     * @param personCreateRequest the person data to create
     * @return response with message indicating verification email sent
     */
    PersonCreateResponse initiatePersonCreation(PersonCreateRequest personCreateRequest);

    /**
     * Verifies the email and completes the person creation process
     * @param verifyEmailRequest the verification request containing email and OTP
     * @return response indicating verification status
     */
    VerifyEmailResponse verifyEmailAndCreatePerson(VerifyEmailRequest verifyEmailRequest);

    /**
     * Saves a person directly to the database (legacy method)
     * @param personCreateRequest the person data to create
     * @return the created person response
     */
    PersonCreateResponse savePerson(PersonCreateRequest personCreateRequest);

    List<Person> getAllPersons();
    Optional<Person> getPersonById(Long id);
    Person updatePerson(Person person);
    void deletePerson(Long id);
    Optional<Person> getPersonByIdentityNumber(Long identityNumber);
    Optional<Person> getPersonByEmail(String email);
    PersonLoginResponse login(PersonLoginRequest personLoginRequest);
    void updatePassword(String email, String otp, String newPassword);
    Person getPersonByEmailOrElseThrow(String email);
    List<String> getUserRoles(String email);
    Person getPersonByIdOrElseThrow(Long id);
}
