package com.hukapp.service.auth.modules.office.dto.response;

import java.time.Instant;

import com.hukapp.service.auth.common.dto.response.BaseResponse;
import com.hukapp.service.auth.modules.office.enums.ClientType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Client response DTO")
public class ClientResponseDto extends BaseResponse {

    @Schema(description = "Client ID", example = "1")
    private Long id;

    @Schema(description = "Client name", example = "Ahmet Yılmaz")
    private String name;

    @Schema(description = "Identity number for individuals or tax number for corporations", example = "12345678901")
    private Long identityOrTaxNumber;

    @Schema(description = "Client address", example = "Atatürk Cad. No:123 Çankaya/Ankara")
    private String address;

    @Schema(description = "Client phone number", example = "05551234567")
    private String phoneNumber;

    @Schema(description = "Client email address", example = "<EMAIL>")
    private String email;

    @Schema(description = "Client type", example = "INDIVIDUAL")
    private ClientType clientType;

    @Schema(description = "Owner ID", example = "1")
    private Long ownerId;

    @Schema(description = "Owner name", example = "Mehmet Avukat")
    private String ownerName;

    @Schema(description = "Creation timestamp")
    private Instant createdAt;

    @Schema(description = "Last update timestamp")
    private Instant updatedAt;

    @Schema(description = "Is deleted flag")
    private Boolean isDeleted;
}
