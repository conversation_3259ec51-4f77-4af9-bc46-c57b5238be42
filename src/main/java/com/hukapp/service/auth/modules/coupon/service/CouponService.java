package com.hukapp.service.auth.modules.coupon.service;

import java.math.BigDecimal;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.hukapp.service.auth.modules.coupon.dto.request.CreateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.request.UpdateCouponDto;
import com.hukapp.service.auth.modules.coupon.dto.response.CouponResponseDto;

/**
 * Service interface for coupon operations
 */
public interface CouponService {

    /**
     * Create a new coupon
     * @param createDto the coupon creation data
     * @return the created coupon response
     */
    CouponResponseDto createCoupon(CreateCouponDto createDto);

    /**
     * Get all coupons with pagination
     * @param pageable pagination information
     * @return page of coupon responses
     */
    Page<CouponResponseDto> getAllCoupons(Pageable pageable);

    /**
     * Get active coupons with pagination
     * @param pageable pagination information
     * @return page of active coupon responses
     */
    Page<CouponResponseDto> getActiveCoupons(Pageable pageable);

    /**
     * Get coupon by ID
     * @param id the coupon ID
     * @return the coupon response
     */
    CouponResponseDto getCouponById(Long id);

    /**
     * Get coupon by code
     * @param code the coupon code
     * @return the coupon response
     */
    CouponResponseDto getCouponByCode(String code);

    /**
     * Update an existing coupon
     * @param id the coupon ID
     * @param updateDto the update data
     * @return the updated coupon response
     */
    CouponResponseDto updateCoupon(Long id, UpdateCouponDto updateDto);

    /**
     * Delete/deactivate a coupon
     * @param id the coupon ID
     */
    void deleteCoupon(Long id);

    /**
     * Validate and apply a coupon to a payment amount
     * @param couponCode the coupon code
     * @param originalAmount the original payment amount
     * @return the discount amount to be applied
     */
    BigDecimal validateAndCalculateDiscount(String couponCode, BigDecimal originalAmount);

    /**
     * Apply coupon usage (increment usage count) - should be called after successful payment
     * @param couponCode the coupon code
     */
    void applyCouponUsage(String couponCode);

    /**
     * Search coupons by code containing the search term
     * @param searchTerm the search term
     * @param pageable pagination information
     * @return page of matching coupon responses
     */
    Page<CouponResponseDto> searchCouponsByCode(String searchTerm, Pageable pageable);
}
