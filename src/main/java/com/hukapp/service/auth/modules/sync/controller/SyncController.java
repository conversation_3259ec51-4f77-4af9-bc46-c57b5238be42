package com.hukapp.service.auth.modules.sync.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hukapp.service.auth.common.cache.InMemoryJsidCache;
import com.hukapp.service.auth.common.dto.response.BaseResponse;
import com.hukapp.service.auth.modules.sync.dto.request.SyncRequest;
import com.hukapp.service.auth.modules.sync.service.UyapService;
import org.springframework.web.bind.annotation.GetMapping;


@Slf4j
@RestController
@RequestMapping("api/user/sync")
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Sync", description = "API for syncing with UYAP system")
@RequiredArgsConstructor
public class SyncController {

    private final UyapService uyapService;
    private final InMemoryJsidCache jsidCache;

    @PostMapping
    @Operation(summary = "Sync UYAP", description = "Sync to UYAP system using provided JSID. Requires authentication with Bearer token")
    public BaseResponse syncToUyap(@RequestBody @Validated SyncRequest request, Authentication authentication) {

        /* Get the JWT token and subject */
        Jwt jwt = (Jwt) authentication.getPrincipal();

        /*
         * Subject icerisinde kullanici email'i bulunmaktadir.
         * TODO: Bu email ile uyap'ta kullanilan email eslesme kontrol yapilabilir.
         */
        String subject = jwt.getSubject();
        log.debug("Sync request received for user: '{}'", subject);

        return uyapService.syncWithUyap(subject, request.getJsid());

    }

    @PostMapping("jsid")
    @Operation(summary = "Save JSID to cache and check UYAP connectivity")
    public ResponseEntity<String> postJsid(@RequestBody String jsid, Authentication authentication) { 
        uyapService.checkUyapConnectivity(authentication.getName(), jsid); 
        jsidCache.put(authentication.getName(), jsid);     
        return ResponseEntity.ok("Connectivity check is successful");
    }

    @GetMapping("jsid")
    @Operation(summary = "Get JSID from cache and check UYAP connectivity")
    public String getJsidFromCache(Authentication authentication) {
        String jsid = jsidCache.get(authentication.getName());
        uyapService.checkUyapConnectivity(authentication.getName(), jsid);
        return jsid;
    }
    

}
