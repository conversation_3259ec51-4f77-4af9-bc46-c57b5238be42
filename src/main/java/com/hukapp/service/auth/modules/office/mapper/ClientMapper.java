package com.hukapp.service.auth.modules.office.mapper;

import java.util.List;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.office.dto.request.ClientCreateDto;
import com.hukapp.service.auth.modules.office.dto.request.ClientUpdateDto;
import com.hukapp.service.auth.modules.office.dto.response.ClientResponseDto;
import com.hukapp.service.auth.modules.office.entity.Client;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true)
)
public interface ClientMapper {

    /**
     * Convert ClientCreateDto to Client entity
     * @param createDto the create DTO
     * @return the Client entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "owner", ignore = true)
    Client toEntity(ClientCreateDto createDto);

    /**
     * Convert Client entity to ClientResponseDto
     * @param client the Client entity
     * @return the response DTO
     */
    @Mapping(source = "owner.id", target = "ownerId")
    @Mapping(source = "owner.name", target = "ownerName")
    ClientResponseDto toResponseDto(Client client);

    /**
     * Convert list of Client entities to list of ClientResponseDto
     * @param clients the list of Client entities
     * @return the list of response DTOs
     */
    List<ClientResponseDto> toResponseDtoList(List<Client> clients);

    /**
     * Update existing Client entity with data from ClientUpdateDto
     * @param updateDto the update DTO
     * @param client the existing Client entity to update
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "owner", ignore = true)
    void updateEntityFromDto(ClientUpdateDto updateDto, @MappingTarget Client client);
}
