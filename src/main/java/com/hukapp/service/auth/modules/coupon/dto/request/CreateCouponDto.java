package com.hukapp.service.auth.modules.coupon.dto.request;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hukapp.service.auth.modules.coupon.enums.DiscountType;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for creating a new coupon
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request DTO for creating a new coupon")
public class CreateCouponDto {

    @NotBlank(message = "Kupon kodu gereklidir")
    @Size(min = 3, max = 50, message = "Kupon kodu 3-50 karakter arasında olmalıdır")
    @Schema(description = "Unique coupon code", example = "SUMMER2024", required = true)
    private String code;

    @Size(max = 255, message = "Açıklama en fazla 255 karakter olmalıdır")
    @Schema(description = "Optional description of the coupon", example = "Summer discount coupon")
    private String description;

    @NotNull(message = "İndirim türü gereklidir")
    @Schema(description = "Type of discount", example = "PERCENTAGE", required = true)
    private DiscountType discountType;

    @NotNull(message = "İndirim değeri gereklidir")
    @DecimalMin(value = "0.01", message = "İndirim değeri 0.01'den büyük olmalıdır")
    @DecimalMax(value = "999999.99", message = "İndirim değeri çok büyük")
    @Schema(description = "Discount value (percentage or fixed amount)", example = "10.00", required = true)
    private BigDecimal discountValue;

    @NotNull(message = "Kullanım limiti gereklidir")
    @Min(value = 1, message = "Kullanım limiti en az 1 olmalıdır")
    @Schema(description = "Maximum number of times this coupon can be used", example = "100", required = true)
    private Integer usageLimit;

    @Future(message = "Son kullanma tarihi gelecekte olmalıdır")
    @Schema(description = "Optional expiration date", example = "2024-12-31T23:59:59")
    private LocalDateTime expirationDate;

    @Schema(description = "Whether the coupon is active", example = "true", defaultValue = "true")
    @Builder.Default
    private Boolean active = true;
}
