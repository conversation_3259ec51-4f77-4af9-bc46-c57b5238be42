package com.hukapp.service.auth.modules.audit.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for audit log statistics response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response DTO for audit log statistics")
public class AuditLogStatisticsResponse {

    @Schema(description = "Total number of requests", example = "1500")
    private Long totalRequests;

    @Schema(description = "Number of unique users", example = "25")
    private Long uniqueUsers;

    @Schema(description = "Average processing time in milliseconds", example = "125.5")
    private Double avgProcessingTime;

    @Schema(description = "Number of error requests (status >= 400)", example = "15")
    private Long errorCount;

    @Schema(description = "Most accessed endpoints")
    private List<EndpointStatistic> mostAccessedEndpoints;

    @Schema(description = "Most active users")
    private List<UserStatistic> mostActiveUsers;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Endpoint access statistics")
    public static class EndpointStatistic {
        @Schema(description = "Endpoint URL", example = "/api/user/profile")
        private String endpointUrl;

        @Schema(description = "Number of accesses", example = "150")
        private Long accessCount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "User activity statistics")
    public static class UserStatistic {
        @Schema(description = "User email", example = "<EMAIL>")
        private String userEmail;

        @Schema(description = "Number of requests", example = "75")
        private Long requestCount;
    }
}
