package com.hukapp.service.auth.modules.file.controller;

import java.util.List;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.file.dto.response.FileDownloadDto;
import com.hukapp.service.auth.modules.file.dto.response.FileListDto;
import com.hukapp.service.auth.modules.file.service.FileService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * User controller for file management operations
 * Subject to subscription-based access control (HTTP 402 for unauthorized users)
 */
@Slf4j
@RestController
@RequestMapping("/api/files")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "User File Management", description = "User endpoints for file access operations")
public class UserFileController {

    private final FileService fileService;

    @Operation(summary = "List available files")
    @GetMapping
    public ResponseEntity<List<FileListDto>> listFiles(Authentication authentication) {

        log.debug("User listing files request from user: {}", authentication.getName());

        // List files for the authenticated user (includes their files + public files)
        List<FileListDto> files = fileService.listFiles();

        return ResponseEntity.ok(files);
    }

    @Operation(summary = "Download file by ID")
    @GetMapping("/{id}/download")
    public ResponseEntity<byte[]> downloadFile(
            @Parameter(description = "File ID", required = true, example = "1")
            @PathVariable Long id,
            Authentication authentication) {
        
        log.info("User downloading file ID: {} by user: {}", id, authentication.getName());
        
        FileDownloadDto fileDownload = fileService.downloadFile(id, authentication.getName());
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(fileDownload.getContentType()));
        headers.setContentDispositionFormData("attachment", fileDownload.getFilename());
        headers.setContentLength(fileDownload.getFileSize());
        
        // Add security headers for file downloads
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);
        
        // Add content security headers
        headers.add("X-Content-Type-Options", "nosniff");
        headers.add("X-Frame-Options", "DENY");
        headers.add("X-XSS-Protection", "1; mode=block");
        
        log.info("File downloaded successfully: {} by user: {}", fileDownload.getFilename(), authentication.getName());
        
        return ResponseEntity.ok()
            .headers(headers)
            .body(fileDownload.getContent());
    }

}
