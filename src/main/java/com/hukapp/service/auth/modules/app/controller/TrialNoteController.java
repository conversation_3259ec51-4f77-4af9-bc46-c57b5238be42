package com.hukapp.service.auth.modules.app.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.app.dto.request.TrialNoteCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.TrialNoteUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.TrialNoteResponseDto;
import com.hukapp.service.auth.modules.app.service.TrialNoteService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/user/trial-notes")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "Trial Notes", description = "API for managing trial notes")
public class TrialNoteController {

    private final TrialNoteService trialNoteService;

    @PostMapping
    @Operation(summary = "Create a new trial note", description = "Creates a new trial note for a specific case")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Trial note created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Case not found")
    })
    public ResponseEntity<TrialNoteResponseDto> createTrialNote(
            @Valid @RequestBody TrialNoteCreateDto trialNoteCreateDto,
            Authentication authentication) {
        
        log.debug("Creating trial note for case number: {}", trialNoteCreateDto.getCaseNumber());
        TrialNoteResponseDto response = trialNoteService.createTrialNote(trialNoteCreateDto, authentication.getName());
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping
    @Operation(summary = "Get all trial notes", description = "Retrieves all trial notes for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Trial notes retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<List<TrialNoteResponseDto>> getAllTrialNotes(Authentication authentication) {
        
        log.debug("Retrieving all trial notes for user: {}", authentication.getName());
        List<TrialNoteResponseDto> notes = trialNoteService.getAllTrialNotes(authentication.getName());
        return ResponseEntity.ok(notes);
    }

    @GetMapping("/case")
    @Operation(summary = "Get trial notes for a specific case", description = "Retrieves all trial notes for a specific case number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Trial notes retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Case not found")
    })
    public ResponseEntity<List<TrialNoteResponseDto>> getTrialNotesByCaseNumber(
            @Parameter(description = "Case number to filter by", example = "2023/123")
            @RequestParam String caseNumber,
            Authentication authentication) {
        
        log.debug("Retrieving trial notes for case number: {}", caseNumber);
        List<TrialNoteResponseDto> notes = trialNoteService.getTrialNotesByCaseNumber(caseNumber, authentication.getName());
        return ResponseEntity.ok(notes);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get a specific trial note", description = "Retrieves a specific trial note by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Trial note retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Trial note not found")
    })
    public ResponseEntity<TrialNoteResponseDto> getTrialNoteById(
            @Parameter(description = "ID of the trial note", example = "1")
            @PathVariable Long id,
            Authentication authentication) {
        
        log.debug("Retrieving trial note with ID: {}", id);
        TrialNoteResponseDto note = trialNoteService.getTrialNoteById(id, authentication.getName());
        return ResponseEntity.ok(note);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update a trial note", description = "Updates an existing trial note")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Trial note updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Trial note not found")
    })
    public ResponseEntity<TrialNoteResponseDto> updateTrialNote(
            @Parameter(description = "ID of the trial note to update", example = "1")
            @PathVariable Long id,
            @Valid @RequestBody TrialNoteUpdateDto trialNoteUpdateDto,
            Authentication authentication) {
        
        log.debug("Updating trial note with ID: {}", id);
        TrialNoteResponseDto response = trialNoteService.updateTrialNote(id, trialNoteUpdateDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a trial note", description = "Deletes a trial note by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Trial note deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Trial note not found")
    })
    public ResponseEntity<Void> deleteTrialNote(
            @Parameter(description = "ID of the trial note to delete", example = "1")
            @PathVariable Long id,
            Authentication authentication) {
        
        log.debug("Deleting trial note with ID: {}", id);
        trialNoteService.deleteTrialNote(id, authentication.getName());
        return ResponseEntity.noContent().build();
    }

}
