package com.hukapp.service.auth.modules.audit.service;

import com.hukapp.service.auth.modules.audit.config.AuditLoggingProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

/**
 * Automatic cleanup of old audit logs
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(value = "audit.logging.auto-cleanup-enabled", havingValue = "true", matchIfMissing = true)
public class AuditLogCleanupJob {

    private final AuditLogService auditLogService;
    private final AuditLoggingProperties auditLoggingProperties;

    /**
     * Scheduled cleanup of old audit logs
     * Runs based on the cron expression defined in configuration
     */
    @Scheduled(cron = "${audit.logging.cleanup-cron-expression:0 0 2 * * ?}")
    public void cleanupOldAuditLogs() {
        if (!auditLoggingProperties.isAutoCleanupEnabled()) {
            log.debug("Audit log auto-cleanup is disabled, skipping cleanup");
            return;
        }

        try {
            log.info("Starting automatic cleanup of old audit logs");
            
            Instant cutoffDate = Instant.now().minus(auditLoggingProperties.getRetentionDays(), ChronoUnit.DAYS);
            log.info("Cleaning up audit logs older than: {} (retention period: {} days)", 
                    cutoffDate, auditLoggingProperties.getRetentionDays());
            
            auditLogService.cleanupOldAuditLogs(cutoffDate);
            
            log.info("Successfully completed automatic cleanup of old audit logs");
        } catch (Exception e) {
            log.error("Failed to cleanup old audit logs: {}", e.getMessage(), e);
        }
    }
}
