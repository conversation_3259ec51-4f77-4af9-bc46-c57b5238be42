package com.hukapp.service.auth.modules.office.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.office.entity.ClientNote;
import com.hukapp.service.auth.modules.person.entity.Person;

@Repository
public interface ClientNoteRepository extends JpaRepository<ClientNote, Long> {
    
    /**
     * Find all notes for a specific client and owner
     * @param client the client
     * @param owner the owner
     * @return list of client notes
     */
    List<ClientNote> findByClientAndOwner(Client client, Person owner);
    
    /**
     * Find all notes for a specific owner
     * @param owner the owner
     * @return list of client notes
     */
    List<ClientNote> findByOwner(Person owner);
    
    /**
     * Find a specific note by ID and owner
     * @param id the note ID
     * @param owner the owner
     * @return optional client note
     */
    Optional<ClientNote> findByIdAndOwner(Long id, Person owner);
    
    /**
     * Find a specific note by ID, client and owner
     * @param id the note ID
     * @param client the client
     * @param owner the owner
     * @return optional client note
     */
    Optional<ClientNote> findByIdAndClientAndOwner(Long id, Client client, Person owner);
}
