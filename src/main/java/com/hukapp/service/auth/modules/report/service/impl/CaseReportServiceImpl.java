package com.hukapp.service.auth.modules.report.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.app.repository.CaseRepository;
import com.hukapp.service.auth.modules.office.dto.TransactionResponse;
import com.hukapp.service.auth.modules.office.entity.Transaction;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;
import com.hukapp.service.auth.modules.office.mapper.TransactionMapper;
import com.hukapp.service.auth.modules.office.repository.TransactionRepository;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.report.dto.response.CaseReportResponse;
import com.hukapp.service.auth.modules.report.dto.response.CaseReportResponse.FinancialSummarySection;
import com.hukapp.service.auth.modules.report.dto.response.CaseReportResponse.TaskSummarySection;
import com.hukapp.service.auth.modules.report.service.CaseReportService;
import com.hukapp.service.auth.modules.sync.enums.DataSourceEnum;
import com.hukapp.service.auth.modules.task.dto.TaskResponse;
import com.hukapp.service.auth.modules.task.entity.Task;
import com.hukapp.service.auth.modules.task.entity.Task.Priority;
import com.hukapp.service.auth.modules.task.entity.Task.Status;
import com.hukapp.service.auth.modules.task.mapper.TaskMapper;
import com.hukapp.service.auth.modules.task.repository.TaskRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class CaseReportServiceImpl implements CaseReportService {

    private final PersonService personService;
    private final CaseRepository caseRepository;
    private final TransactionRepository transactionRepository;
    private final TaskRepository taskRepository;
    private final TransactionMapper transactionMapper;
    private final TaskMapper taskMapper;

    @Override
    public CaseReportResponse generateCaseReport(String caseNumber, String userEmail) {
        log.debug("Generating comprehensive report for case: {} requested by user: {}", caseNumber, userEmail);

        // Get the authenticated user
        Person person = personService.getPersonByEmailOrElseThrow(userEmail);

        // Verify the case exists for this user
        verifyCaseExists(caseNumber, userEmail);

        // Create the report
        CaseReportResponse report = new CaseReportResponse();
        report.setCaseNumber(caseNumber);
        report.setReportGeneratedAt(LocalDateTime.now());

        // Get case transactions
        List<Transaction> caseTransactions = getCaseTransactions(caseNumber, person);

        // Add financial summary
        try {
            FinancialSummarySection financialSummary = calculateFinancialSummary(caseTransactions);
            report.setFinancialSummary(financialSummary);

            // Add transactions list
            List<TransactionResponse> transactionResponses = caseTransactions.stream()
                    .map(transactionMapper::toDTO)
                    .toList();
            report.setTransactions(transactionResponses);
        } catch (Exception e) {
            log.warn("Could not calculate financial summary for case: {}", caseNumber, e);
        }

        // Get case tasks
        List<Task> caseTasks = getCaseTasks(caseNumber, person);

        // Add task summary
        try {
            TaskSummarySection taskSummary = calculateTaskSummary(caseTasks);
            report.setTaskSummary(taskSummary);

            // Add tasks list
            List<TaskResponse> taskResponses = caseTasks.stream()
                    .map(taskMapper::toDTO)
                    .toList();
            report.setTasks(taskResponses);
        } catch (Exception e) {
            log.warn("Could not calculate task summary for case: {}", caseNumber, e);
        }

        return report;
    }

    /**
     * Verifies that a case with the given case number exists for the user
     *
     * @param caseNumber The case number to verify
     * @param userEmail The email of the user
     * @throws ResourceNotFoundException if the case does not exist
     */
    private void verifyCaseExists(String caseNumber, String userEmail) {
        // Check if the case exists in any of the case data sources
        boolean caseExists = false;

        // Try to find the case in UYAP_CASE_TARAFLAR
        caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                userEmail, DataSourceEnum.UYAP_CASE_TARAFLAR, caseNumber).isPresent();

        if (!caseExists) {
            // Try to find the case in UYAP_CASE_HISTORY
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_HISTORY, caseNumber).isPresent();
        }

        if (!caseExists) {
            // Try to find the case in UYAP_CASE_TAHSILAT_REDDIYAT
            caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                    userEmail, DataSourceEnum.UYAP_CASE_TAHSILAT_REDDIYAT, caseNumber).isPresent();
        }

        if (!caseExists) {
            throw new ResourceNotFoundException("Dosya bulunamadı, dosya yıl/esas: " + caseNumber);
        }
    }

    /**
     * Gets all transactions for a specific case
     *
     * @param caseNumber The case number to get transactions for
     * @param person The person entity who owns the transactions
     * @return A list of transactions for the case
     */
    private List<Transaction> getCaseTransactions(String caseNumber, Person person) {
        log.debug("Getting transactions for case: {}", caseNumber);

        // Get all transactions for the user
        List<Transaction> allTransactions = transactionRepository.findAllByOwner(person);

        // Filter transactions for the specific case
        return allTransactions.stream()
                .filter(transaction -> caseNumber.equals(transaction.getCaseNumber()))
                .toList();
    }

    /**
     * Calculates financial summary for a case including total income, total expenses, and net income
     *
     * @param caseTransactions The list of transactions for the case
     * @return A FinancialSummarySection containing the calculated financial information
     */
    private FinancialSummarySection calculateFinancialSummary(List<Transaction> caseTransactions) {
        log.debug("Calculating financial summary for {} transactions", caseTransactions.size());

        // Calculate total income
        BigDecimal totalIncome = caseTransactions.stream()
                .filter(transaction -> TransactionCategory.INCOME.equals(transaction.getTransactionType().getCategory()))
                .map(Transaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Calculate total expenses
        BigDecimal totalExpenses = caseTransactions.stream()
                .filter(transaction -> TransactionCategory.EXPENSE.equals(transaction.getTransactionType().getCategory()))
                .map(Transaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Calculate net income
        BigDecimal netIncome = totalIncome.subtract(totalExpenses);

        return FinancialSummarySection.builder()
                .totalIncome(totalIncome)
                .totalExpenses(totalExpenses)
                .netIncome(netIncome)
                .build();
    }

    /**
     * Gets all tasks for a specific case
     *
     * @param caseNumber The case number to get tasks for
     * @param person The person entity who owns the tasks
     * @return A list of tasks for the case
     */
    private List<Task> getCaseTasks(String caseNumber, Person person) {
        log.debug("Getting tasks for case: {}", caseNumber);

        // Get all tasks for the user
        List<Task> allTasks = taskRepository.findAllByReporter(person);

        // Filter tasks for the specific case
        return allTasks.stream()
                .filter(task -> caseNumber.equals(task.getCaseNumber()))
                .toList();
    }

    /**
     * Calculates task summary for a case including total tasks and counts by status and priority
     *
     * @param caseTasks The list of tasks for the case
     * @return A TaskSummarySection containing the calculated task information
     */
    private TaskSummarySection calculateTaskSummary(List<Task> caseTasks) {
        log.debug("Calculating task summary for {} tasks", caseTasks.size());

        // Calculate total tasks
        int totalTasks = caseTasks.size();

        // Group tasks by status and count
        Map<Status, Integer> taskCountsByStatus = caseTasks.stream()
                .collect(Collectors.groupingBy(
                    Task::getStatus,
                    () -> new EnumMap<>(Status.class),
                    Collectors.summingInt(task -> 1)
                ));

        // Group tasks by priority and count
        Map<Priority, Integer> taskCountsByPriority = caseTasks.stream()
                .collect(Collectors.groupingBy(
                    Task::getPriority,
                    () -> new EnumMap<>(Priority.class),
                    Collectors.summingInt(task -> 1)
                ));

        return TaskSummarySection.builder()
                .totalTasks(totalTasks)
                .taskCountsByStatus(taskCountsByStatus)
                .taskCountsByPriority(taskCountsByPriority)
                .build();
    }
}
