package com.hukapp.service.auth.modules.app.service;

import java.util.List;

import com.hukapp.service.auth.modules.app.dto.request.FrequentCaseNumberRequest;
import com.hukapp.service.auth.modules.app.dto.response.FrequentCaseNumberResponse;

/**
 * Service interface for managing frequently used case numbers.
 */
public interface FrequentCaseNumberService {

    /**
     * Add a new case number to the user's frequently used list
     * 
     * @param request the request containing the case number and optional description
     * @param userEmail the email of the user
     * @return the created frequent case number response
     */
    FrequentCaseNumberResponse addFrequentCaseNumber(FrequentCaseNumberRequest request, String userEmail);
    
    /**
     * Get all frequently used case numbers for a user
     * 
     * @param userEmail the email of the user
     * @return list of frequent case number responses
     */
    List<FrequentCaseNumberResponse> getFrequentCaseNumbers(String userEmail);
    
    /**
     * Get a specific frequently used case number by ID
     * 
     * @param id the ID of the frequent case number
     * @param userEmail the email of the user
     * @return the frequent case number response
     */
    FrequentCaseNumberResponse getFrequentCaseNumberById(Long id, String userEmail);
    
    /**
     * Delete a frequently used case number
     * 
     * @param id the ID of the frequent case number to delete
     * @param userEmail the email of the user
     */
    void deleteFrequentCaseNumber(Long id, String userEmail);
    
    /**
     * Update the last used timestamp for a case number
     * 
     * @param caseNumber the case number that was used
     * @param userEmail the email of the user
     */
    void updateLastUsedTimestamp(String caseNumber, String userEmail);
}
