package com.hukapp.service.auth.modules.payment.service;

import java.util.List;

import com.hukapp.service.auth.modules.payment.dto.CreateOrderRequest;
import com.hukapp.service.auth.modules.payment.dto.PaymentResponse;
import com.hukapp.service.auth.modules.payment.dto.PaymentStatusResponse;

public interface PaymentService {

    /**
     * Create a payment with iyzico
     *
     * @param request Payment request
     * @param personEmail Email of the person making the payment
     * @return Payment response with payment page URL
     */
    PaymentResponse createPayment(CreateOrderRequest request, String personEmail);

    /**
     * Get payment by ID
     *
     * @param paymentId Payment ID
     * @param personEmail Email of the person who made the payment
     * @return Payment response
     */
    PaymentResponse getPaymentById(String paymentId, String personEmail);

    /**
     * Get all payments for a person
     *
     * @param personEmail Email of the person
     * @return List of payment responses
     */
    List<PaymentResponse> getAllPayments(String personEmail);

    /**
     * Retrieve payment status from iyzico
     *
     * @param paymentId Payment ID
     * @param personEmail Email of the person who made the payment
     * @return Payment status response
     */
    PaymentStatusResponse retrievePaymentByPaymentIdAndPerson(String paymentId, String personEmail);

    PaymentStatusResponse retrievePaymentByPaymentId(String paymentId);

    /**
     * Get all payments in the system
     *
     * @return List of payment responses
     */
    List<PaymentResponse> getAllPayments();
}
