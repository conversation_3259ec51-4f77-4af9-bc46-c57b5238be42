package com.hukapp.service.auth.modules.app.entity;

import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.file.enums.FileType;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * Entity representing metadata for Power of Attorney file uploads.
 * File content is stored separately for performance optimization with lazy loading.
 */
@Entity
@Table(name = "power_of_attorney_file_metadata")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PowerOfAttorneyFileMetadata extends BaseEntity {

    /**
     * Reference to the Power of Attorney this file belongs to
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "power_of_attorney_id", nullable = false)
    @NotNull(message = "Power of Attorney cannot be null")
    private PowerOfAttorney powerOfAttorney;

    /**
     * Original filename as uploaded by the user
     */
    @Column(nullable = false, length = 255)
    @NotBlank(message = "Original filename cannot be blank")
    @Size(max = 255, message = "Original filename cannot exceed 255 characters")
    private String originalFilename;

    /**
     * Stored filename (unique) in the system
     */
    @Column(nullable = false, unique = true, length = 255)
    @NotBlank(message = "Stored filename cannot be blank")
    @Size(max = 255, message = "Stored filename cannot exceed 255 characters")
    private String storedFilename;

    /**
     * MIME type of the file
     */
    @Column(nullable = false, length = 100)
    @NotBlank(message = "Content type cannot be blank")
    @Size(max = 100, message = "Content type cannot exceed 100 characters")
    private String contentType;

    /**
     * File size in bytes
     */
    @Column(nullable = false)
    @NotNull(message = "File size cannot be null")
    @Positive(message = "File size must be positive")
    private Long fileSize;

    /**
     * File type category for organization and validation
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    @NotNull(message = "File type cannot be null")
    private FileType fileType;

    /**
     * The actual file content stored as BLOB (lazy loaded)
     */
    @OneToOne(mappedBy = "metadata", cascade = CascadeType.REMOVE, fetch = FetchType.LAZY, optional = false, orphanRemoval = true)
    private PowerOfAttorneyFileContent content;

    /**
     * User who uploaded the file
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uploader_id", nullable = false)
    @NotNull(message = "Uploader cannot be null")
    private Person uploader;

    /**
     * Optional description for the file
     */
    @Column(length = 500)
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    /**
     * Number of times the file has been downloaded
     */
    @Column(nullable = false)
    @Builder.Default
    private Long downloadCount = 0L;

    /**
     * Last time the file was accessed/downloaded
     */
    private Instant lastAccessedAt;

    /**
     * MD5 hash of the file content for integrity checking
     */
    @Column(length = 32)
    private String md5Hash;

    /**
     * Whether the file is marked for deletion (soft delete flag)
     */
    @Column(nullable = false)
    @Builder.Default
    private Boolean markedForDeletion = false;

    /**
     * When the file was marked for deletion
     */
    private Instant deletionMarkedAt;

    /**
     * Tags associated with the file for categorization
     */
    @Column(length = 500)
    @Size(max = 500, message = "Tags cannot exceed 500 characters")
    private String tags;

    /**
     * Increments the download count and updates last accessed time
     */
    public void incrementDownloadCount() {
        this.downloadCount = (this.downloadCount == null ? 0L : this.downloadCount) + 1;
        this.lastAccessedAt = Instant.now();
    }

    /**
     * Marks the file for deletion
     */
    public void markForDeletion() {
        this.markedForDeletion = true;
        this.deletionMarkedAt = Instant.now();
    }

    /**
     * Checks if the file belongs to the specified user
     */
    public boolean belongsToUser(Person user) {
        return this.uploader != null && this.uploader.getId().equals(user.getId());
    }

    /**
     * Checks if the file is associated with the specified Power of Attorney
     */
    public boolean belongsToPowerOfAttorney(PowerOfAttorney poa) {
        return this.powerOfAttorney != null && this.powerOfAttorney.getId().equals(poa.getId());
    }
}
