package com.hukapp.service.auth.modules.office.entity;

import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Table(name = "transaction_type", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"name", "category"})
})
public class TransactionType extends BaseEntity{

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TransactionCategory category;
    
}
