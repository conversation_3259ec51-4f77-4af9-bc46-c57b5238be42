package com.hukapp.service.auth.modules.app.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request DTO for creating or updating a frequently used case number")
public class FrequentCaseNumberRequest {

    @NotBlank(message = "Dosya numarası gereklidir")
    @Size(min = 2, max = 50, message = "Dosya numarası 2-50 karakter arasında olmalıdır")
    @Schema(description = "Case number to be added to frequently used list", example = "2023/123")
    private String caseNumber;
    
    @Size(max = 255, message = "Açıklama en fazla 255 karakter olmalıdır")
    @Schema(description = "Optional description for the case number", example = "Önemli dava")
    private String description;
}
