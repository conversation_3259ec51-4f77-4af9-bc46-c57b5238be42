package com.hukapp.service.auth.modules.reminder.dto;

import java.time.Instant;

import com.hukapp.service.auth.modules.reminder.entity.Reminder.Priority;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReminderRequest {

    @NotBlank(message = "Başlık gereklidir")
    private String title;

    @NotBlank(message = "Açıklama gereklidir")
    private String description;

    @NotNull(message = "Son tarih gereklidir")
    private Instant dueDate;

    @NotNull(message = "Tekrar aralığı gereklidir")
    @Min(value = 1, message = "Tekrar aralığı en az 1 saat olmalıdır")
    private long repeatIntervalInHours;

    @NotNull(message = "Öncelik gereklidir")
    private Priority priority;

}