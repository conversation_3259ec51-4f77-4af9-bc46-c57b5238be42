package com.hukapp.service.auth.modules.office.dto;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.modules.office.enums.AccountingType;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Client Accounting Create Request")
public class ClientAccountingCreateRequest {

    @Schema(description = "Client ID", example = "1")
    private Long clientId;

    @NotNull(message = "Muhasebe tipi gereklidir")
    @Schema(description = "Accounting type", example = "RECEIVED")
    private AccountingType accountingType;

    @NotNull(message = "Tutar gereklidir")
    @Positive(message = "Tutar pozitif olmalıdır")
    @Schema(description = "Amount", example = "1500.50")
    private BigDecimal amount;

    @Schema(description = "Description", example = "Dava ücreti ödemesi")
    private String description;

    @NotNull(message = "Kayıt tarihi gereklidir")
    @Schema(description = "Record date")
    private Instant recordDate;

    @Schema(description = "Case number", example = "2024/123")
    private String caseNumber;

}
