package com.hukapp.service.auth.modules.app.dto.response;

import java.time.Instant;

import com.hukapp.service.auth.common.dto.response.BaseResponse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response DTO for a frequently used case number")
public class FrequentCaseNumberResponse extends BaseResponse {
    
    @Schema(description = "Unique identifier for the frequent case number", example = "1")
    private Long id;
    
    @Schema(description = "The case number", example = "2023/123")
    private String caseNumber;
    
    @Schema(description = "Optional description for the case number", example = "Önemli dava")
    private String description;
    
    @Schema(description = "When the case number was last used", example = "2023-06-15T10:30:45.123Z")
    private Instant lastUsedAt;
}
