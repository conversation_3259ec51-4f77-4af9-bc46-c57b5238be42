package com.hukapp.service.auth.modules.app.mapper;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.hukapp.service.auth.modules.app.dto.request.FrequentCaseNumberRequest;
import com.hukapp.service.auth.modules.app.dto.response.FrequentCaseNumberResponse;
import com.hukapp.service.auth.modules.app.entity.FrequentCaseNumber;
import com.hukapp.service.auth.modules.person.entity.Person;

@Component
public class FrequentCaseNumberMapper {

    /**
     * Convert a FrequentCaseNumber entity to a FrequentCaseNumberResponse DTO
     * 
     * @param frequentCaseNumber the entity to convert
     * @return the converted DTO
     */
    public FrequentCaseNumberResponse toDTO(FrequentCaseNumber frequentCaseNumber) {
        if (frequentCaseNumber == null) {
            return null;
        }
        
        return FrequentCaseNumberResponse.builder()
                .id(frequentCaseNumber.getId())
                .caseNumber(frequentCaseNumber.getCaseNumber())
                .description(frequentCaseNumber.getDescription())
                .lastUsedAt(frequentCaseNumber.getLastUsedAt())
                .build();
    }
    
    /**
     * Convert a list of FrequentCaseNumber entities to a list of FrequentCaseNumberResponse DTOs
     * 
     * @param frequentCaseNumbers the list of entities to convert
     * @return the list of converted DTOs
     */
    public List<FrequentCaseNumberResponse> toDTOList(List<FrequentCaseNumber> frequentCaseNumbers) {
        if (frequentCaseNumbers == null) {
            return null;
        }
        
        return frequentCaseNumbers.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Convert a FrequentCaseNumberRequest DTO to a FrequentCaseNumber entity
     * 
     * @param request the DTO to convert
     * @param owner the owner of the case number
     * @return the converted entity
     */
    public FrequentCaseNumber toEntity(FrequentCaseNumberRequest request, Person owner) {
        if (request == null) {
            return null;
        }
        
        return FrequentCaseNumber.builder()
                .caseNumber(request.getCaseNumber())
                .description(request.getDescription())
                .lastUsedAt(Instant.now())
                .owner(owner)
                .build();
    }
    
    /**
     * Update an existing FrequentCaseNumber entity with values from a FrequentCaseNumberRequest DTO
     * 
     * @param entity the entity to update
     * @param request the DTO containing the new values
     * @return the updated entity
     */
    public FrequentCaseNumber updateEntityFromDTO(FrequentCaseNumber entity, FrequentCaseNumberRequest request) {
        if (entity == null || request == null) {
            return entity;
        }
        
        entity.setCaseNumber(request.getCaseNumber());
        entity.setDescription(request.getDescription());
        entity.setLastUsedAt(Instant.now());
        
        return entity;
    }
}
