package com.hukapp.service.auth.modules.app.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Request object for updating a trial note")
public class TrialNoteUpdateDto {

    @Schema(description = "Case number to which this trial note belongs", example = "2023/123", required = true)
    @NotBlank(message = "Dosya numarası boş olamaz")
    private String caseNumber;

    @Schema(description = "Content of the trial note", example = "Updated trial note content with additional details.", required = true)
    @NotBlank(message = "Not içeriği boş olamaz")
    private String noteContent;

    @Schema(description = "Title of the trial note", example = "Updated First Hearing Notes", required = true)
    @NotBlank(message = "Not başlığı boş olamaz")
    @Size(max = 255, message = "Not başlığı 255 karakterden uzun olamaz")
    private String noteTitle;
}
