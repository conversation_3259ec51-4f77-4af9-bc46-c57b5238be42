package com.hukapp.service.auth.modules.task.dto;

import java.time.Instant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Response object for creating or updating a note")
public class NoteResponse extends NoteRequest {
    
    private Long id;
    private Instant createdAt;
    private Instant updatedAt;
}
