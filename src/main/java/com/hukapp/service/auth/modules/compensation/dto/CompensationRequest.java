package com.hukapp.service.auth.modules.compensation.dto;

import java.math.BigDecimal;
import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Request for calculating compensation")
public class CompensationRequest {

    @Schema(description = "Start date for the compensation calculation", example = "2025-01-15")
    @NotNull(message = "Başlangıç tarihi gereklidir")
    private LocalDate startDate;

    @Schema(description = "End date for the compensation calculation", example = "2025-01-15")
    @NotNull(message = "Bitiş tarihi gereklidir")
    private LocalDate endDate;

    @Schema(description = "Base salary", example = "30000")
    @NotNull(message = "<PERSON>r<PERSON>t maaş gereklidir")
    private BigDecimal grossSalary;

    @Schema(description = "Kümülatif gelir vergisi matrahı", example = "100000")
    private BigDecimal cumulativeIncomeTaxBasis;

}
