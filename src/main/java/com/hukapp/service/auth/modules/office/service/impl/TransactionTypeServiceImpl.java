package com.hukapp.service.auth.modules.office.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.office.dto.TransactionTypeRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionTypeResponse;
import com.hukapp.service.auth.modules.office.entity.TransactionType;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;
import com.hukapp.service.auth.modules.office.mapper.TransactionTypeMapper;
import com.hukapp.service.auth.modules.office.repository.TransactionTypeRepository;
import com.hukapp.service.auth.modules.office.service.TransactionTypeService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class TransactionTypeServiceImpl implements TransactionTypeService {

    private final TransactionTypeRepository transactionTypeRepository;
    private final TransactionTypeMapper transactionTypeMapper;

    @Override
    public TransactionTypeResponse createTransactionType(TransactionTypeRequest entity) {

        TransactionType transactionType = transactionTypeMapper.toEntity(entity);

        transactionTypeRepository.save(transactionType);

        return transactionTypeMapper.toDTO(transactionType);
    }

    @Override
    public TransactionTypeResponse updateTransactionType(Long id, TransactionTypeRequest entity) {

        TransactionType transactionType = transactionTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Transaction type not found"));
        transactionType.setName(entity.getName());
        transactionType.setCategory(entity.getCategory());
        transactionTypeRepository.save(transactionType);
        return transactionTypeMapper.toDTO(transactionType);

    }

    @Override
    public void deleteTransactionType(Long id) {
        TransactionType transactionType = transactionTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Transaction type not found"));
        transactionTypeRepository.delete(transactionType);
    }

    @Override
    public List<TransactionTypeResponse> getAllTransactionTypes(TransactionCategory category) {

        List<TransactionType> transactionTypes;
        if (category != null) {
            transactionTypes = transactionTypeRepository.findByCategory(category);
        } else {
            transactionTypes = transactionTypeRepository.findAll();
        }
        return transactionTypes.stream()
                .map(transactionTypeMapper::toDTO)
                .toList();
    }

}
