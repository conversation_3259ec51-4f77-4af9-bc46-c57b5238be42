package com.hukapp.service.auth.modules.payment.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.payment.dto.PaymentResponse;
import com.hukapp.service.auth.modules.payment.entity.AvasPayment;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true)
)
public interface PaymentMapper {

    PaymentResponse toDTO(AvasPayment payment);
}
