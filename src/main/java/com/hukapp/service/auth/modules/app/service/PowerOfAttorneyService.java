package com.hukapp.service.auth.modules.app.service;

import java.util.List;

import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.PowerOfAttorneyResponse;

public interface PowerOfAttorneyService {

    /**
     * Creates a new power of attorney
     *
     * @param powerOfAttorneyCreateDto The request containing power of attorney details
     * @param userEmail The email of the authenticated user
     * @return The created power of attorney
     */
    PowerOfAttorneyResponse createPowerOfAttorney(PowerOfAttorneyCreateDto powerOfAttorneyCreateDto, String userEmail);

    /**
     * Retrieves all power of attorneys for the authenticated user
     *
     * @param userEmail The email of the authenticated user
     * @return List of power of attorneys
     */
    List<PowerOfAttorneyResponse> getAllPowerOfAttorneys(String userEmail);

    /**
     * Retrieves all power of attorneys for a specific case
     *
     * @param caseNumber The case number
     * @param userEmail The email of the authenticated user
     * @return List of power of attorneys for the case
     */
    List<PowerOfAttorneyResponse> getPowerOfAttorneysByCaseNumber(String caseNumber, String userEmail);

    /**
     * Retrieves a specific power of attorney by ID
     *
     * @param id The ID of the power of attorney
     * @param userEmail The email of the authenticated user
     * @return The power of attorney
     */
    PowerOfAttorneyResponse getPowerOfAttorneyById(Long id, String userEmail);

    /**
     * Updates an existing power of attorney
     *
     * @param id The ID of the power of attorney to update
     * @param powerOfAttorneyUpdateDto The updated power of attorney details
     * @param userEmail The email of the authenticated user
     * @return The updated power of attorney
     */
    PowerOfAttorneyResponse updatePowerOfAttorney(Long id, PowerOfAttorneyUpdateDto powerOfAttorneyUpdateDto, String userEmail);

    /**
     * Deletes a power of attorney
     *
     * @param id The ID of the power of attorney to delete
     * @param userEmail The email of the authenticated user
     */
    void deletePowerOfAttorney(Long id, String userEmail);

    /**
     * Retrieves a specific power of attorney by its power of attorney number
     *
     * @param powerOfAttorneyNumber The power of attorney number
     * @param userEmail The email of the authenticated user
     * @return The power of attorney
     */
    PowerOfAttorneyResponse getPowerOfAttorneyByNumber(String powerOfAttorneyNumber, String userEmail);

    /**
     * Retrieves all power of attorneys associated with a specific client
     *
     * @param clientId The ID of the client
     * @param userEmail The email of the authenticated user
     * @return List of power of attorneys for the client
     */
    List<PowerOfAttorneyResponse> getPowerOfAttorneysByClientId(Long clientId, String userEmail);
}
