package com.hukapp.service.auth.modules.app.entity;

import java.math.BigDecimal;

import com.hukapp.service.auth.common.converter.StringEncryptionConverter;
import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.app.enums.CaseType;
import com.hukapp.service.auth.modules.app.enums.CrimeType;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Table(name = "case_details")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class CaseDetails extends BaseEntity {

    @Column(nullable = false)
    private String caseNumber;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private CaseType caseType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = true)
    private CrimeType crimeType;

    @Column(nullable = false)
    private boolean derdest;

    @Column(nullable = false)
    private BigDecimal caseValue;

    @Convert(converter = StringEncryptionConverter.class)
    @Column(columnDefinition = "text")
    private String caseReason;

    @Convert(converter = StringEncryptionConverter.class)
    @Column(nullable = false)
    private String caseTitle;

    @ManyToOne
    @JoinColumn(name = "owner_id", nullable = false)
    private Person owner;
}
