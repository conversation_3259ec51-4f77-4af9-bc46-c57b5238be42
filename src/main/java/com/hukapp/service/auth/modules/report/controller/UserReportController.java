package com.hukapp.service.auth.modules.report.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.report.dto.response.UserReportResponse;
import com.hukapp.service.auth.modules.report.service.UserReportService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("api/user/report")
@SecurityRequirement(name = "bearerAuth")
@RequiredArgsConstructor
@Tag(name = "User Report", description = "API for generating comprehensive user reports")
public class UserReportController {

    private final UserReportService userReportService;

    @GetMapping(produces = "application/json;charset=UTF-8")
    @Operation(
        summary = "Generate detailed user report",
        description = "Generates a comprehensive report containing all available information about the authenticated user, including financial data, task statistics, and optionally the user's photo"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Report successfully generated",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserReportResponse.class))
        ),
        @ApiResponse(responseCode = "401", description = "Unauthorized - User not authenticated", content = @Content),
        @ApiResponse(responseCode = "404", description = "User not found", content = @Content),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    public ResponseEntity<UserReportResponse> generateUserReport(
            @Parameter(hidden = true) Authentication authentication,
            @Parameter(
                name = "includePhoto",
                description = "Whether to include the user's photo in the report. Including the photo increases response size.",
                schema = @Schema(type = "boolean", defaultValue = "false")
            )
            @RequestParam(name = "includePhoto", defaultValue = "false") boolean includePhoto) {
        log.debug("Received request to generate user report for: {} with includePhoto={}",
                authentication.getName(), includePhoto);
        UserReportResponse report = userReportService.generateUserReport(authentication.getName(), includePhoto);
        return ResponseEntity.ok(report);
    }
}
