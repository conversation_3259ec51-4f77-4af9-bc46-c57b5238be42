package com.hukapp.service.auth.modules.app.dto.response;

import com.hukapp.service.auth.common.dto.response.BaseResponse;
import com.hukapp.service.auth.modules.file.enums.FileType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * DTO for Power of Attorney file response with complete file information
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Complete Power of Attorney file information response")
public class PoaFileResponseDto extends BaseResponse {

    @Schema(description = "File ID", example = "1")
    private Long id;

    @Schema(description = "Power of Attorney ID this file belongs to", example = "1")
    private Long powerOfAttorneyId;

    @Schema(description = "Power of Attorney number", example = "POA-2023-12345")
    private String powerOfAttorneyNumber;

    @Schema(description = "Original filename as uploaded", example = "power_of_attorney.pdf")
    private String originalFilename;

    @Schema(description = "File content type/MIME type", example = "application/pdf")
    private String contentType;

    @Schema(description = "File size in bytes", example = "1048576")
    private Long fileSize;

    @Schema(description = "File type category", example = "DOCUMENT")
    private FileType fileType;

    @Schema(description = "File description", example = "Original power of attorney document")
    private String description;

    @Schema(description = "File tags for categorization", example = "original,notarized,2024")
    private String tags;

    @Schema(description = "Number of times the file has been downloaded", example = "5")
    private Long downloadCount;

    @Schema(description = "Last time the file was accessed", example = "2024-01-15T10:30:00Z")
    private Instant lastAccessedAt;

    @Schema(description = "MD5 hash for integrity verification", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5Hash;

    @Schema(description = "File uploader information")
    private UploaderInfo uploader;

    @Schema(description = "File creation timestamp", example = "2024-01-15T10:30:00Z")
    private Instant createdAt;

    @Schema(description = "File last update timestamp", example = "2024-01-15T10:30:00Z")
    private Instant updatedAt;

    /**
     * Nested class for uploader information
     */
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Information about the file uploader")
    public static class UploaderInfo {
        
        @Schema(description = "Uploader ID", example = "1")
        private Long id;
        
        @Schema(description = "Uploader full name", example = "John Doe")
        private String name;
        
        @Schema(description = "Uploader email", example = "<EMAIL>")
        private String email;
    }

    /**
     * Gets a human-readable file size
     * @return formatted file size string
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "Unknown";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
