package com.hukapp.service.auth.modules.payment.dto;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Payment request")
public class PaymentRequest {

    @NotNull(message = "Price is required")
    @Min(value = 1, message = "Price must be at least 1")
    @Schema(description = "Payment amount", example = "100.00")
    private BigDecimal price;

    @NotBlank(message = "Currency is required")
    @Schema(description = "Currency code", example = "TRY")
    private String currency;

    @Schema(description = "Basket ID (optional)", example = "B67832")
    private String basketId;

    @Schema(description = "Callback URL for payment result", example = "https://www.merchant.com/callback")
    private String callbackUrl;
    
}
