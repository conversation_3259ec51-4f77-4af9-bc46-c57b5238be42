package com.hukapp.service.auth.modules.reminder.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.hukapp.service.auth.modules.reminder.dto.ReminderRequest;
import com.hukapp.service.auth.modules.reminder.dto.ReminderResponse;
import com.hukapp.service.auth.modules.reminder.entity.Reminder;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true)
)
public interface ReminderMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    Reminder toEntity(ReminderRequest requestDTO);

    @Mapping(target = "reporterId", source = "owner.id")
    ReminderResponse toResponseDTO(Reminder reminder);

    default void updateEntityFromDTO(ReminderRequest requestDTO, Reminder reminder) {
        if (requestDTO == null || reminder == null) {
            return;
        }
        // Map fields from requestDTO to reminder manually
        reminder.setTitle(requestDTO.getTitle());
        reminder.setDescription(requestDTO.getDescription());
        reminder.setDueDate(requestDTO.getDueDate());
        reminder.setRepeatIntervalInHours(requestDTO.getRepeatIntervalInHours());
        reminder.setPriority(requestDTO.getPriority());
        // Add other field mappings as necessary
    }

}