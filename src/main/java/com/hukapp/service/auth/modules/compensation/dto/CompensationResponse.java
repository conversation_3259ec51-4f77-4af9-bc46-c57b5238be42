package com.hukapp.service.auth.modules.compensation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Response for calculating compensation")
public class CompensationResponse {

    private int totalDays;

    private String grossSeverancePay;
    private String severancePayStampTax;
    private String netSeverancePay;

    private int noticePeriodInDays;
    private int jobSearchLeaveHours;
    private String grossNoticePay;
    private String noticePayStampTax;
    private String noticePayIncomeTax;
    private String netNoticePay;

    private String totalCompensation;
    
    private String severanceNoticeText;

}
