package com.hukapp.service.auth.modules.payment.dto;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.modules.payment.enums.PaymentStatus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Payment response")
public class PaymentResponse {

    @Schema(description = "Payment ID", example = "12345")
    private Long id;

    @Schema(description = "Conversation ID", example = "123456789")
    private String conversationId;

    @Schema(description = "Price", example = "100.00")
    private BigDecimal price;

    @Schema(description = "Paid price", example = "100.00")
    private BigDecimal paidPrice;

    @Schema(description = "Currency", example = "TRY")
    private String currency;

    @Schema(description = "Basket ID", example = "B67832")
    private String basketId;

    @Schema(description = "Payment ID from iyzico", example = "12345678")
    private String paymentId;

    @Schema(description = "Payment transaction ID", example = "12345678")
    private String paymentTransactionId;

    @Schema(description = "Payment status", example = "SUCCESS")
    private PaymentStatus status;

    @Schema(description = "Payment date")
    private Instant paymentDate;

    @Schema(description = "Error code if payment failed", example = "123")
    private String errorCode;

    @Schema(description = "Error message if payment failed", example = "Payment failed")
    private String errorMessage;

    @Schema(description = "Error group if payment failed", example = "PAYMENT")
    private String errorGroup;

    @Schema(description = "Payment page URL", example = "https://sandbox-api.iyzipay.com/payment/pay")
    private String paymentPageUrl;

    @Schema(description = "Applied coupon code", example = "SUMMER2024")
    private String appliedCouponCode;

    @Schema(description = "Original price before discount", example = "100.00")
    private BigDecimal originalPrice;

    @Schema(description = "Discount amount applied", example = "10.00")
    private BigDecimal discountAmount;

    @Schema(description = "Product ID", example = "1")
    private ProductResponse product;

    @Schema(description = "Creation date")
    private Instant createdAt;

    @Schema(description = "Last update date")
    private Instant updatedAt;

    @Schema(description = "Expiry date")
    private Instant validUntil;
}
