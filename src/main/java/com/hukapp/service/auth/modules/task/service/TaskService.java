package com.hukapp.service.auth.modules.task.service;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.common.exception.custom.TaskValidationException;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.task.dto.TaskRequest;
import com.hukapp.service.auth.modules.task.dto.TaskResponse;
import com.hukapp.service.auth.modules.task.entity.Task;
import com.hukapp.service.auth.modules.task.mapper.TaskMapper;
import com.hukapp.service.auth.modules.task.repository.TaskRepository;

import lombok.RequiredArgsConstructor;

import org.springframework.security.core.Authentication;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class TaskService {

    private final TaskRepository taskRepository;
    private final TaskMapper taskMapper;
    private final PersonService personService;

    public List<TaskResponse> getAllTasks(Authentication authentication) {

        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        List<Task> tasks = taskRepository.findAllByReporter(person);
        return tasks.stream().map(taskMapper::toDTO).toList();
    }

    public TaskResponse getTaskById(Long id, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        Optional<Task> task = taskRepository.findByIdAndReporter(id, person);
        return task.map(taskMapper::toDTO).orElseThrow(() -> new ResourceNotFoundException("Task not found for user: " + person.getEmail()));
    }

    public TaskResponse createTask(TaskRequest task, Authentication authentication) { 

        validateTaskRequest(task);
        Task taskEntity = taskMapper.toEntity(task);
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        taskEntity.setReporter(person);
        taskRepository.save(taskEntity);
        return taskMapper.toDTO(taskEntity);
    }

    public TaskResponse updateTask(Long id, TaskRequest updatedTask, Authentication authentication) {
        validateTaskRequest(updatedTask);
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());

        Optional<Task> task = taskRepository.findByIdAndReporter(id, person);

        if (task.isPresent()) {
            Task existingTask = task.get();
            existingTask.setTitle(updatedTask.getTitle());
            existingTask.setDescription(updatedTask.getDescription());
            existingTask.setPriority(updatedTask.getPriority());
            existingTask.setDueDate(updatedTask.getDueDate());
            existingTask.setCaseNumber(updatedTask.getCaseNumber());
            existingTask.setStatus(updatedTask.getStatus());
            existingTask.setStartDate(updatedTask.getStartDate());
            existingTask.setTaskType(updatedTask.getTaskType());

            // Handle clientId update
            if (updatedTask.getClientId() != null) {
                existingTask.setClient(com.hukapp.service.auth.modules.office.entity.Client.builder()
                    .id(updatedTask.getClientId()).build());
            } else {
                existingTask.setClient(null);
            }

            taskRepository.save(existingTask);
            return taskMapper.toDTO(existingTask);
        } else {
            throw new ResourceNotFoundException("Task not found for user: " + person.getEmail());
        }
    }

    public void deleteTask(Long id, Authentication authentication) {
        Person person = personService.getPersonByEmailOrElseThrow(authentication.getName());
        Optional<Task> task = taskRepository.findByIdAndReporter(id, person);
        if (task.isPresent()) {
            taskRepository.deleteById(id);
        } else {
            throw new ResourceNotFoundException("Task not found for user: " + person.getEmail());
        }
    }

    /**
     * Validates that at least one of caseNumber or clientId is provided in the task request.
     * This validation is performed at the service layer to maintain clean architecture principles.
     *
     * @param taskRequest the task request to validate
     * @throws TaskValidationException if both caseNumber and clientId are null or empty
     */
    private void validateTaskRequest(TaskRequest taskRequest) {
        boolean hasCaseNumber = taskRequest.getCaseNumber() != null && !taskRequest.getCaseNumber().trim().isEmpty();
        boolean hasClientId = taskRequest.getClientId() != null;

        if (!hasCaseNumber && !hasClientId) {
            throw new TaskValidationException("Görev oluşturmak için dosya numarası veya müvekkil ID'si belirtilmelidir");
        }
    }
}