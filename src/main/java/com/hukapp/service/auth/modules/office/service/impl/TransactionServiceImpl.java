package com.hukapp.service.auth.modules.office.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.modules.office.dto.TransactionRequest;
import com.hukapp.service.auth.modules.office.dto.TransactionResponse;
import com.hukapp.service.auth.modules.office.entity.Transaction;
import com.hukapp.service.auth.modules.office.entity.TransactionType;
import com.hukapp.service.auth.modules.office.enums.TransactionCategory;
import com.hukapp.service.auth.modules.office.mapper.TransactionMapper;
import com.hukapp.service.auth.modules.office.repository.TransactionRepository;
import com.hukapp.service.auth.modules.office.repository.TransactionTypeRepository;
import com.hukapp.service.auth.modules.office.service.TransactionService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class TransactionServiceImpl implements TransactionService {

    private final TransactionRepository transactionRepository;
    private final TransactionMapper transactionMapper;
    private final TransactionTypeRepository transactionTypeRepository;
    private final PersonService personService;

    private TransactionType getTransactionTypeById(Long id) {
        return transactionTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Transaction type not found"));
    }

    @Override
    public TransactionResponse saveTransaction(TransactionRequest entity, String personEmail) {
        
        Person person = personService.getPersonByEmailOrElseThrow(personEmail);
        
        Transaction transaction = transactionMapper.toEntity(entity);
        transaction.setOwner(person);
        TransactionType transactionType = getTransactionTypeById(entity.getTransactionTypeId());
        transaction.setTransactionType(transactionType);
        
        transactionRepository.save(transaction);
        return transactionMapper.toDTO(transaction);
    }

    @Override
    public List<TransactionResponse> getAllTransactions(String personEmail, TransactionCategory category) {
        Person person = personService.getPersonByEmailOrElseThrow(personEmail);
        List<TransactionResponse> transactions;
        if (category == null) {
            transactions = transactionRepository.findAllByOwner(person).stream()
                    .map(transactionMapper::toDTO)
                    .toList();
        } else {
            transactions = transactionRepository.findAllByOwnerAndTransactionTypeCategory(person, category).stream()
                    .map(transactionMapper::toDTO)
                    .toList();
        }
        return transactions;
    }

    @Override
    public TransactionResponse updateTransaction(Long id, TransactionRequest entity, String personEmail) {
        Person person = personService.getPersonByEmailOrElseThrow(personEmail);
        Transaction transaction = transactionRepository.findByIdAndOwner(id, person)
                .orElseThrow(() -> new ResourceNotFoundException("İşlem bulunamadı"));
        
        TransactionType transactionType = getTransactionTypeById(entity.getTransactionTypeId());
        transaction.setTransactionType(transactionType);
        transaction.setTransactionDate(entity.getTransactionDate());
        transaction.setAmount(entity.getAmount());
        transaction.setDescription(entity.getDescription());
        transaction.setCaseNumber(entity.getCaseNumber());
        transaction.setTransactionType(transactionType);
        
        transaction = transactionRepository.save(transaction);
        return transactionMapper.toDTO(transaction);
    }

    @Override
    public void deleteTransaction(Long id, String personEmail) {
        Person person = personService.getPersonByEmailOrElseThrow(personEmail);
        Transaction transaction = transactionRepository.findByIdAndOwner(id, person)
                .orElseThrow(() -> new ResourceNotFoundException("İşlem bulunamadı"));
        transactionRepository.delete(transaction);
    }
    
}
