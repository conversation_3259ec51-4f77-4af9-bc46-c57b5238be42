package com.hukapp.service.auth.modules.app.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.common.exception.custom.UnexpectedStatusException;
import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.PowerOfAttorneyUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.PowerOfAttorneyResponse;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorney;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorneyFileMetadata;
import com.hukapp.service.auth.modules.app.mapper.PowerOfAttorneyMapper;
import com.hukapp.service.auth.modules.app.repository.CaseRepository;
import com.hukapp.service.auth.modules.app.repository.PowerOfAttorneyFileRepository;
import com.hukapp.service.auth.modules.app.repository.PowerOfAttorneyRepository;
import com.hukapp.service.auth.modules.app.service.PowerOfAttorneyService;
import com.hukapp.service.auth.modules.office.entity.Client;
import com.hukapp.service.auth.modules.office.repository.ClientRepository;
import com.hukapp.service.auth.modules.office.service.ClientService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;
import com.hukapp.service.auth.modules.sync.enums.DataSourceEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class PowerOfAttorneyServiceImpl implements PowerOfAttorneyService {

    private final PowerOfAttorneyRepository powerOfAttorneyRepository;
    private final PowerOfAttorneyFileRepository powerOfAttorneyFileRepository;
    private final PowerOfAttorneyMapper powerOfAttorneyMapper;
    private final PersonService personService;
    private final ClientService clientService;
    private final ClientRepository clientRepository;
    private final CaseRepository caseRepository;

    @Override
    public PowerOfAttorneyResponse createPowerOfAttorney(PowerOfAttorneyCreateDto powerOfAttorneyCreateDto, String userEmail) {

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Throw exception if clientIds and caseNumbers are both null or both empty
        if ((powerOfAttorneyCreateDto.getClientIds() == null || powerOfAttorneyCreateDto.getClientIds().isEmpty()) &&
                (powerOfAttorneyCreateDto.getCaseNumbers() == null || powerOfAttorneyCreateDto.getCaseNumbers().isEmpty())) {
            throw new UnexpectedStatusException("Müvekkil listesi veya dosya numarası listesi gereklidir");
        }

        // Verify all cases exist
        verifyCasesExist(powerOfAttorneyCreateDto.getCaseNumbers(), userEmail);

        // Check if power of attorney number already exists for this user
        powerOfAttorneyRepository.findByPowerOfAttorneyNumberAndOwner(
                powerOfAttorneyCreateDto.getPowerOfAttorneyNumber(), owner)
                .ifPresent(poa -> {
                    throw new ResourceAlreadyExistsException("İlgili vekalet zaten mevcut. Vekalet numarası: " +
                            powerOfAttorneyCreateDto.getPowerOfAttorneyNumber());
                });

        // Validate and fetch client entities
        List<Client> clients = validateAndFetchClients(powerOfAttorneyCreateDto.getClientIds(), owner);

        // Convert request to entity
        PowerOfAttorney powerOfAttorney = powerOfAttorneyMapper.toEntity(powerOfAttorneyCreateDto);
        powerOfAttorney.setOwner(owner);
        powerOfAttorney.setClientList(clients);

        // Save the entity
        powerOfAttorney = powerOfAttorneyRepository.save(powerOfAttorney);

        // Return the response
        return powerOfAttorneyMapper.toDTO(powerOfAttorney);
    }

    @Override
    public List<PowerOfAttorneyResponse> getAllPowerOfAttorneys(String userEmail) {
        log.debug("Retrieving all power of attorneys for user: {}", userEmail);

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Get all power of attorneys for the user
        List<PowerOfAttorney> powerOfAttorneys = powerOfAttorneyRepository.findByOwner(owner);

        // Convert entities to DTOs
        return powerOfAttorneys.stream()
                .map(powerOfAttorneyMapper::toDTO)
                .toList();
    }

    @Override
    public List<PowerOfAttorneyResponse> getPowerOfAttorneysByCaseNumber(String caseNumber, String userEmail) {
        log.debug("Retrieving power of attorneys for case number: {}", caseNumber);

        // Verify the case exists
        verifyCaseExists(caseNumber, userEmail);

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Get all power of attorneys for the case
        List<PowerOfAttorney> powerOfAttorneys = powerOfAttorneyRepository.findByCaseNumbersContainingAndOwner(caseNumber, owner);

        // Convert entities to DTOs
        return powerOfAttorneys.stream()
                .map(powerOfAttorneyMapper::toDTO)
                .toList();
    }

    @Override
    public PowerOfAttorneyResponse getPowerOfAttorneyById(Long id, String userEmail) {
        log.debug("Retrieving power of attorney with ID: {}", id);

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Get the power of attorney
        PowerOfAttorney powerOfAttorney = powerOfAttorneyRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("-Power of attorney not found with ID: " + id));

        // Convert entity to DTO
        return powerOfAttorneyMapper.toDTO(powerOfAttorney);
    }

    @Override
    public PowerOfAttorneyResponse updatePowerOfAttorney(Long id, PowerOfAttorneyUpdateDto powerOfAttorneyUpdateDto, String userEmail) {
        log.debug("Updating power of attorney with ID: {}", id);

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Throw exception if clientIds and caseNumbers are both null or both empty
        if ((powerOfAttorneyUpdateDto.getClientIds() == null || powerOfAttorneyUpdateDto.getClientIds().isEmpty()) &&
                (powerOfAttorneyUpdateDto.getCaseNumbers() == null || powerOfAttorneyUpdateDto.getCaseNumbers().isEmpty())) {
            throw new UnexpectedStatusException("Müvekkil listesi veya dosya numarası listesi gereklidir");
        }

        // Verify all cases exist
        verifyCasesExist(powerOfAttorneyUpdateDto.getCaseNumbers(), userEmail);

        // Get the existing power of attorney
        PowerOfAttorney existingPowerOfAttorney = powerOfAttorneyRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Power of attorney not found with ID: " + id));

        // Check if power of attorney number already exists for another power of attorney
        powerOfAttorneyRepository.findByPowerOfAttorneyNumberAndOwner(
                powerOfAttorneyUpdateDto.getPowerOfAttorneyNumber(), owner)
                .ifPresent(poa -> {
                    if (!poa.getId().equals(id)) {
                        throw new ResourceAlreadyExistsException("Power of attorney with number " +
                                powerOfAttorneyUpdateDto.getPowerOfAttorneyNumber() + " already exists");
                    }
                });

        // Validate and fetch client entities
        List<Client> clients = validateAndFetchClients(powerOfAttorneyUpdateDto.getClientIds(), owner);

        // Update the entity with new values
        PowerOfAttorney updatedPowerOfAttorney = powerOfAttorneyMapper.toEntity(powerOfAttorneyUpdateDto);
        updatedPowerOfAttorney.setId(existingPowerOfAttorney.getId());
        updatedPowerOfAttorney.setCreatedAt(existingPowerOfAttorney.getCreatedAt());
        updatedPowerOfAttorney.setCreatedBy(existingPowerOfAttorney.getCreatedBy());
        updatedPowerOfAttorney.setOwner(owner);
        updatedPowerOfAttorney.setClientList(clients);

        // Save the updated entity
        updatedPowerOfAttorney = powerOfAttorneyRepository.save(updatedPowerOfAttorney);

        // Return the response
        return powerOfAttorneyMapper.toDTO(updatedPowerOfAttorney);
    }

    @Override
    @Transactional
    public void deletePowerOfAttorney(Long id, String userEmail) {
        log.debug("Deleting power of attorney with ID: {}", id);

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Get the power of attorney
        PowerOfAttorney powerOfAttorney = powerOfAttorneyRepository.findByIdAndOwner(id, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Power of attorney not found with ID: " + id));

        // First, delete all associated files
        List<PowerOfAttorneyFileMetadata> associatedFiles = powerOfAttorneyFileRepository.findByPowerOfAttorneyIdAndUploader(id, owner);
        if (!associatedFiles.isEmpty()) {
            log.debug("Deleting {} associated files for power of attorney ID: {}", associatedFiles.size(), id);

            // Use standard JPA delete to ensure cascade operations work properly
            powerOfAttorneyFileRepository.deleteAll(associatedFiles);

            log.debug("Successfully deleted all associated files for power of attorney ID: {}", id);
        }

        // Now delete the power of attorney
        powerOfAttorneyRepository.delete(powerOfAttorney);

        log.info("Power of attorney with ID: {} and all associated files deleted successfully", id);
    }

    @Override
    public PowerOfAttorneyResponse getPowerOfAttorneyByNumber(String powerOfAttorneyNumber, String userEmail) {
        log.debug("Retrieving power of attorney with number: {}", powerOfAttorneyNumber);

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Get the power of attorney by its number
        PowerOfAttorney powerOfAttorney = powerOfAttorneyRepository.findByPowerOfAttorneyNumberAndOwner(powerOfAttorneyNumber, owner)
                .orElseThrow(() -> new ResourceNotFoundException("Power of attorney not found with number: " + powerOfAttorneyNumber));

        // Convert entity to DTO
        return powerOfAttorneyMapper.toDTO(powerOfAttorney);
    }

    /**
     * Verifies that a case with the given case number exists for the user
     *
     * @param caseNumber The case number to verify
     * @param userEmail The email of the user
     * @throws ResourceNotFoundException if the case does not exist
     */
    private void verifyCaseExists(String caseNumber, String userEmail) {
        // Check if the case exists in any of the case data sources
        boolean caseExists = false;

        // Try to find the case in UYAP_CASE_TARAFLAR
        caseExists = caseRepository.findByEmailAndDataSourceAndCaseNumber(
                userEmail, DataSourceEnum.UYAP_CASE_TARAFLAR, caseNumber).isPresent();

        if (!caseExists) {
            throw new ResourceNotFoundException("Dosya bulunamadı, dosya yıl/esas: " + caseNumber);
        }
    }

    /**
     * Verifies that all cases with the given case numbers exist for the user
     *
     * @param caseNumbers The list of case numbers to verify
     * @param userEmail The email of the user
     * @throws ResourceNotFoundException if any case does not exist
     */
    private void verifyCasesExist(List<String> caseNumbers, String userEmail) {
        if (caseNumbers != null && !caseNumbers.isEmpty()) {
            for (String caseNumber : caseNumbers) {
                verifyCaseExists(caseNumber, userEmail);
            }
        }
    }

    /**
     * Validates and fetches client entities by their IDs for the given owner
     */
    private List<Client> validateAndFetchClients(List<Long> clientIds, Person owner) {

        // If list is null or empty, return empty list
        if (clientIds == null || clientIds.isEmpty()) {
            return List.of();
        }

        return clientIds.stream()
                .map(clientId -> clientRepository.findByIdAndOwnerId(clientId, owner.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Müvekkil bulunamadı: " + clientId)))
                .toList();
    }

    @Override
    public List<PowerOfAttorneyResponse> getPowerOfAttorneysByClientId(Long clientId, String userEmail) {
        log.debug("Retrieving power of attorneys for client ID: {}", clientId);

        // Get the authenticated user
        Person owner = personService.getPersonByEmailOrElseThrow(userEmail);

        // Validate that the client belongs to the user
        clientService.getClientById(clientId, userEmail);

        // Get all power of attorneys that include this client
        List<PowerOfAttorney> powerOfAttorneys = powerOfAttorneyRepository.findByClientListIdAndOwner(clientId, owner);

        // Throw exception if no power of attorneys found
        if (powerOfAttorneys.isEmpty()) {
            throw new ResourceNotFoundException("Vekalet bulunamadı, müvekkil ID: " + clientId);
        }

        // Convert entities to DTOs
        return powerOfAttorneys.stream()
                .map(powerOfAttorneyMapper::toDTO)
                .toList();
    }
}
