package com.hukapp.service.auth.modules.office.entity;

import java.math.BigDecimal;
import java.time.Instant;

import com.hukapp.service.auth.common.entity.BaseEntity;
import com.hukapp.service.auth.modules.person.entity.Person;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class Transaction extends BaseEntity {

    @JoinColumn(nullable = false)
    @ManyToOne(optional = false)
    private TransactionType transactionType;

    private String description;

    @Column(nullable = false)
    private BigDecimal amount;

    @Column(nullable = false)
    private Instant transactionDate;

    private String caseNumber;

    @ManyToOne(optional = false)
    @JoinColumn(name = "owner_id", nullable = false)
    private Person owner;

}
