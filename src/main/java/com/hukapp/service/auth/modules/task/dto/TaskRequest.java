package com.hukapp.service.auth.modules.task.dto;

import java.time.Instant;

import com.hukapp.service.auth.modules.task.entity.Task.Priority;
import com.hukapp.service.auth.modules.task.entity.Task.Status;
import com.hukapp.service.auth.modules.task.entity.Task.TaskType;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(description = "Request for creating or updating a task")
public class TaskRequest {

    @NotBlank(message = "Başlık boş olamaz")
    private String title;

    @NotBlank(message = "Açıklama boş olamaz")
    private String description;

    @NotNull(message = "<PERSON><PERSON><PERSON> boş olamaz")
    private Priority priority;

    private Instant startDate;

    @NotNull(message = "Son tarih boş olamaz")
    private Instant dueDate;

    @jakarta.validation.constraints.AssertTrue(message = "Başlangıç tarihi, bitiş tarihinden önce olmalıdır")
    private boolean isStartDateBeforeDueDate() {
        return startDate == null || dueDate == null || startDate.isBefore(dueDate);
    }

    @jakarta.validation.constraints.AssertTrue(message = "Dosya numarası veya müvekkil ID'si belirtilmelidir")
    private boolean isCaseNumberOrClientIdProvided() {
        return (caseNumber != null && !caseNumber.trim().isEmpty()) || clientId != null;
    }

    private String caseNumber;

    @Schema(description = "Client ID associated with the task", example = "1")
    private Long clientId;

    @NotNull(message = "Durum boş olamaz")
    private Status status;

    private TaskType taskType;

}
