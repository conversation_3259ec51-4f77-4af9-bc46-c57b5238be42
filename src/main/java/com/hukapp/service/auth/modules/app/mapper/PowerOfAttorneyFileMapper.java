package com.hukapp.service.auth.modules.app.mapper;

import com.hukapp.service.auth.modules.app.dto.response.PoaFileDownloadDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileListDto;
import com.hukapp.service.auth.modules.app.dto.response.PoaFileResponseDto;
import com.hukapp.service.auth.modules.app.entity.PowerOfAttorneyFileMetadata;
import com.hukapp.service.auth.modules.person.entity.Person;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * MapStruct mapper for PowerOfAttorneyFileMetadata entity and DTOs
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PowerOfAttorneyFileMapper {

    /**
     * Maps PowerOfAttorneyFileMetadata to PoaFileResponseDto with complete information
     */
    @Mapping(target = "powerOfAttorneyId", source = "powerOfAttorney.id")
    @Mapping(target = "powerOfAttorneyNumber", source = "powerOfAttorney.powerOfAttorneyNumber")
    @Mapping(target = "uploader", source = "uploader", qualifiedByName = "mapUploaderInfo")
    @Mapping(target = "responseMessage", constant = "Power of Attorney file retrieved successfully")
    PoaFileResponseDto toResponseDto(PowerOfAttorneyFileMetadata fileEntity);

    /**
     * Maps PowerOfAttorneyFileMetadata to PoaFileListDto for listing purposes
     */
    @Mapping(target = "powerOfAttorneyId", source = "powerOfAttorney.id")
    @Mapping(target = "powerOfAttorneyNumber", source = "powerOfAttorney.powerOfAttorneyNumber")
    @Mapping(target = "uploaderName", source = "uploader", qualifiedByName = "mapUploaderName")
    @Mapping(target = "uploaderEmail", source = "uploader.email")
    PoaFileListDto toListDto(PowerOfAttorneyFileMetadata fileEntity);

    /**
     * Maps PowerOfAttorneyFileMetadata to PoaFileDownloadDto for download purposes
     */
    @Mapping(target = "filename", source = "originalFilename")
    @Mapping(target = "content", source = "content.content")
    @Mapping(target = "powerOfAttorneyId", source = "powerOfAttorney.id")
    @Mapping(target = "powerOfAttorneyNumber", source = "powerOfAttorney.powerOfAttorneyNumber")
    PoaFileDownloadDto toDownloadDto(PowerOfAttorneyFileMetadata fileEntity);

    /**
     * Maps PowerOfAttorneyFileMetadata to PoaFileDownloadDto without content (metadata only)
     */
    @Mapping(target = "filename", source = "originalFilename")
    @Mapping(target = "content", ignore = true)
    @Mapping(target = "powerOfAttorneyId", source = "powerOfAttorney.id")
    @Mapping(target = "powerOfAttorneyNumber", source = "powerOfAttorney.powerOfAttorneyNumber")
    PoaFileDownloadDto toDownloadDtoWithoutContent(PowerOfAttorneyFileMetadata fileEntity);

    /**
     * Maps list of PowerOfAttorneyFileMetadata to list of PoaFileResponseDto
     */
    List<PoaFileResponseDto> toResponseDtoList(List<PowerOfAttorneyFileMetadata> fileEntities);

    /**
     * Maps list of PowerOfAttorneyFileMetadata to list of PoaFileListDto
     */
    List<PoaFileListDto> toListDtoList(List<PowerOfAttorneyFileMetadata> fileEntities);

    /**
     * Custom mapping method for uploader information
     */
    @Named("mapUploaderInfo")
    default PoaFileResponseDto.UploaderInfo mapUploaderInfo(Person uploader) {
        if (uploader == null) {
            return null;
        }
        
        PoaFileResponseDto.UploaderInfo uploaderInfo = new PoaFileResponseDto.UploaderInfo();
        uploaderInfo.setId(uploader.getId());
        uploaderInfo.setName(uploader.getName() + " " + uploader.getSurname());
        uploaderInfo.setEmail(uploader.getEmail());
        
        return uploaderInfo;
    }

    /**
     * Custom mapping method for uploader name
     */
    @Named("mapUploaderName")
    default String mapUploaderName(Person uploader) {
        if (uploader == null) {
            return null;
        }
        return uploader.getName() + " " + uploader.getSurname();
    }
}
