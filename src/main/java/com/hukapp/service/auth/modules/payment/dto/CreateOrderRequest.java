package com.hukapp.service.auth.modules.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Payment request")
public class CreateOrderRequest {

    @NotNull(message = "Ürün ID gereklidir")
    @Min(value = 1, message = "Geçersiz ürün ID")
    @Schema(description = "Ürün ID", example = "1")
    private Long productId;

    @Schema(description = "Optional coupon code to apply discount", example = "SUMMER2024")
    private String couponCode;

}
