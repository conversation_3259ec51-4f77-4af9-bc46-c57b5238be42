package com.hukapp.service.auth.modules.file.dto.request;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for file upload requests
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "File upload request containing the file and optional metadata")
public class FileUploadDto {

    /**
     * The file to be uploaded
     */
    @NotNull(message = "File cannot be null")
    @Schema(description = "The file to upload", required = true, type = "string", format = "binary")
    private MultipartFile file;

    /**
     * Optional description for the file
     */
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    @Schema(description = "Optional description for the file", example = "Important legal document", maxLength = 500)
    private String description;

    /**
     * Optional tags for file categorization
     */
    @Size(max = 500, message = "Tags cannot exceed 500 characters")
    @Schema(description = "Comma-separated tags for file categorization", example = "legal,contract,2024", maxLength = 500)
    private String tags;

    /**
     * Whether the file should be publicly accessible (for future use)
     */
    @Schema(description = "Whether the file should be publicly accessible", example = "false", defaultValue = "false")
    private Boolean isPublic = false;
}
