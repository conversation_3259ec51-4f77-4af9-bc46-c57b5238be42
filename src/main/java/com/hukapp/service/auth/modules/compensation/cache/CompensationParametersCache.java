package com.hukapp.service.auth.modules.compensation.cache;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hukapp.service.auth.modules.compensation.repository.CompensationParameterRepository;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class CompensationParametersCache {

    private final CompensationParameterRepository compensationParameterRepository;
    private final Map<String, Double> cachedParameters = new ConcurrentHashMap<>();
    private final Map<String, String> cachedDescriptions = new ConcurrentHashMap<>();

    @PostConstruct
    @Scheduled(fixedRate = 300000) // Refresh every 5 minutes
    public void refreshCache() {
        Map<String, Double> updatedThresholds = new HashMap<>();
        Map<String, String> updatedDescriptions = new HashMap<>();
        compensationParameterRepository.findAll().forEach(parameter -> {
            updatedThresholds.put(parameter.getParameterName(), parameter.getParameterValue());
            updatedDescriptions.put(parameter.getParameterName(),
                    Optional.ofNullable(parameter.getParameterDescription()).orElse(""));
        });
        if (!updatedThresholds.isEmpty()) {
            this.cachedParameters.clear();
            this.cachedParameters.putAll(updatedThresholds);
            log.info("Compensation parameters cache refreshed, new cache size: {}", updatedThresholds.size());
        } else {
            log.warn("Compensation parameters cache refresh failed, no parameters found");
        }
        if (updatedDescriptions != null && !updatedDescriptions.isEmpty()) {
            this.cachedDescriptions.clear();
            this.cachedDescriptions.putAll(updatedDescriptions);
            log.info("Compensation parameter descriptions cache refreshed, new cache size: {}",
                    updatedDescriptions.size());
        } else {
            log.warn("Compensation parameter descriptions cache refresh failed, no descriptions found");
        }
    }

    public Double getParameterValue(String parameterName) {
        return cachedParameters.get(parameterName);
    }

    public String getParameterDescription(String parameterName) {
        return cachedDescriptions.get(parameterName);
    }

    public Map<String, Double> getAllParameters() {
        return new HashMap<>(cachedParameters);
    }

    public Map<String, String> getAllDescriptions() {
        return new HashMap<>(cachedDescriptions);
    }

}
