package com.hukapp.service.auth.modules.app.service;

import java.util.List;

import com.hukapp.service.auth.modules.app.dto.request.TrialNoteCreateDto;
import com.hukapp.service.auth.modules.app.dto.request.TrialNoteUpdateDto;
import com.hukapp.service.auth.modules.app.dto.response.TrialNoteResponseDto;

public interface TrialNoteService {
    
    /**
     * Create a new trial note
     * @param trialNoteCreateDto the trial note data to create
     * @param userEmail the email of the authenticated user
     * @return the created trial note response
     */
    TrialNoteResponseDto createTrialNote(TrialNoteCreateDto trialNoteCreateDto, String userEmail);
    
    /**
     * Get all trial notes for a specific case number belonging to the authenticated user
     * @param caseNumber the case number to filter by
     * @param userEmail the email of the authenticated user
     * @return list of trial notes for the specified case
     */
    List<TrialNoteResponseDto> getTrialNotesByCaseNumber(String caseNumber, String userEmail);
    
    /**
     * Get all trial notes belonging to the authenticated user
     * @param userEmail the email of the authenticated user
     * @return list of all trial notes for the user
     */
    List<TrialNoteResponseDto> getAllTrialNotes(String userEmail);
    
    /**
     * Get a specific trial note by ID (with owner verification)
     * @param id the ID of the trial note
     * @param userEmail the email of the authenticated user
     * @return the trial note response if found and belongs to the user
     */
    TrialNoteResponseDto getTrialNoteById(Long id, String userEmail);
    
    /**
     * Update an existing trial note
     * @param id the ID of the trial note to update
     * @param trialNoteUpdateDto the updated trial note data
     * @param userEmail the email of the authenticated user
     * @return the updated trial note response
     */
    TrialNoteResponseDto updateTrialNote(Long id, TrialNoteUpdateDto trialNoteUpdateDto, String userEmail);
    
    /**
     * Delete a trial note by ID (with owner verification)
     * @param id the ID of the trial note to delete
     * @param userEmail the email of the authenticated user
     */
    void deleteTrialNote(Long id, String userEmail);
    
}
