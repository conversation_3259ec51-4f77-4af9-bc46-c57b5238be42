package com.hukapp.service.auth.modules.office.service;

import java.util.List;

import com.hukapp.service.auth.modules.office.dto.request.ClientNoteRequest;
import com.hukapp.service.auth.modules.office.dto.response.ClientNoteResponse;

public interface ClientNoteService {
    
    /**
     * Create a new client note
     * @param clientId the client ID
     * @param clientNoteRequest the note request data
     * @param userEmail the authenticated user's email
     * @return the created note response
     */
    ClientNoteResponse createClientNote(Long clientId, ClientNoteRequest clientNoteRequest, String userEmail);
    
    /**
     * Get all notes for a specific client
     * @param clientId the client ID
     * @param userEmail the authenticated user's email
     * @return list of client note responses
     */
    List<ClientNoteResponse> getClientNotesByClientId(Long clientId, String userEmail);
    
    /**
     * Get all notes for the authenticated user
     * @param userEmail the authenticated user's email
     * @return list of client note responses
     */
    List<ClientNoteResponse> getAllClientNotes(String userEmail);
    
    /**
     * Update an existing note
     * @param id the note ID
     * @param clientNoteRequest the updated note data
     * @param userEmail the authenticated user's email
     * @return the updated note response
     */
    ClientNoteResponse updateClientNote(Long id, ClientNoteRequest clientNoteRequest, String userEmail);
    
    /**
     * Delete a note
     * @param id the note ID
     * @param userEmail the authenticated user's email
     */
    void deleteClientNote(Long id, String userEmail);
}
