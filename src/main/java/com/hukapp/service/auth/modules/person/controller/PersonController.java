package com.hukapp.service.auth.modules.person.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hukapp.service.auth.modules.email.service.EmailService;
import com.hukapp.service.auth.modules.person.dto.request.ForgotPasswordRequest;
import com.hukapp.service.auth.modules.person.dto.request.PersonCreateRequest;
import com.hukapp.service.auth.modules.person.dto.request.PersonLoginRequest;
import com.hukapp.service.auth.modules.person.dto.request.UpdatePasswordRequest;
import com.hukapp.service.auth.modules.person.dto.request.ValidateOtpRequest;
import com.hukapp.service.auth.modules.person.dto.request.VerifyEmailRequest;
import com.hukapp.service.auth.modules.person.dto.response.OtpValidationResponse;
import com.hukapp.service.auth.modules.person.dto.response.PersonCreateResponse;
import com.hukapp.service.auth.modules.person.dto.response.PersonLoginResponse;
import com.hukapp.service.auth.modules.person.dto.response.UpdatePasswordResponse;
import com.hukapp.service.auth.modules.person.dto.response.VerifyEmailResponse;
import com.hukapp.service.auth.modules.person.service.JwtService;
import com.hukapp.service.auth.modules.person.service.OtpService;
import com.hukapp.service.auth.modules.person.service.PersonService;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import java.util.HashMap;
import java.util.Map;

/**
 * The PersonController class handles HTTP requests for the /api/persons
 * endpoint.
 * It uses the PersonService to retrieve data about persons.
 */
@RestController
@RequestMapping("/auth/user")
@RequiredArgsConstructor // Generates a constructor with required arguments (final fields).
@Slf4j
public class PersonController {

    private final PersonService personService;
    private final MessageSource messageSource;
    private final OtpService otpService; // added OTP service
    private final JwtService tokenService;
    private final EmailService emailService;

    @Value("${app.environment}")
    private String environment;

    @Operation(summary = "Initiate person creation", description = "Initiates the person creation process by sending a verification email")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Verification email sent"),
            @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content),
            @ApiResponse(responseCode = "409", description = "Email already exists", content = @Content),
            @ApiResponse(responseCode = "500", description = "Beklenmeyen bir hata oluştu, lütfen daha sonra tekrar deneyin.", content = @Content)
    })
    @PostMapping("create")
    public ResponseEntity<PersonCreateResponse> createPerson(
            @RequestBody @Valid PersonCreateRequest personCreateRequest) {

        PersonCreateResponse response = personService.initiatePersonCreation(personCreateRequest);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Verify email and complete registration", description = "Verifies the email using OTP and completes the person registration")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email verified and person created"),
            @ApiResponse(responseCode = "400", description = "Invalid OTP or expired registration", content = @Content),
            @ApiResponse(responseCode = "500", description = "Beklenmeyen bir hata oluştu, lütfen daha sonra tekrar deneyin.", content = @Content)
    })
    @PostMapping("verify-email")
    public ResponseEntity<VerifyEmailResponse> verifyEmail(
            @RequestBody @Valid VerifyEmailRequest verifyEmailRequest) {

        VerifyEmailResponse response = personService.verifyEmailAndCreatePerson(verifyEmailRequest);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Create a new person directly (legacy)", description = "Creates a new person directly without email verification")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Successfully created person"),
            @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content),
            @ApiResponse(responseCode = "409", description = "Tekrar eden kayıt.", content = @Content),
            @ApiResponse(responseCode = "500", description = "Beklenmeyen bir hata oluştu, lütfen daha sonra tekrar deneyin.", content = @Content)
    })
    @PostMapping("create-direct")
    public ResponseEntity<PersonCreateResponse> createPersonDirect(
            @RequestBody @Valid PersonCreateRequest personCreateRequest) {

        PersonCreateResponse response = personService.savePerson(personCreateRequest);
        response.setResponseMessage(
                messageSource.getMessage("person.created.success", null, LocaleContextHolder.getLocale()));
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Login a person", description = "Logs in a person with the provided credentials")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully logged in"),
            @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content),
            @ApiResponse(responseCode = "401", description = "Invalid credentials", content = @Content),
            @ApiResponse(responseCode = "500", description = "Beklenmeyen bir hata oluştu, lütfen daha sonra tekrar deneyin.", content = @Content)
    })
    @PostMapping("login")
    public ResponseEntity<PersonLoginResponse> login(@RequestBody @Valid PersonLoginRequest personLoginRequest,HttpServletRequest request) {

        PersonLoginResponse response = personService.login(personLoginRequest);

        // Fetch roles for the user (this assumes you have a method to get roles)
        List<String> roles = personService.getUserRoles(personLoginRequest.getEmail());

        // Generate JWT with roles
        String token = tokenService.generateToken(personLoginRequest.getEmail(), roles);
        response.setJwt(token);
        response.setResponseMessage(
                messageSource.getMessage("person.login.success", null, LocaleContextHolder.getLocale()));

                String ipAddress = request.getRemoteAddr();
                String userAgent = request.getHeader("User-Agent");
                String email = personLoginRequest.getEmail(); // Adjust as per your user object

                // Send login notification email asynchronously
                try {
                    emailService.sendLoginNotificationAsync(email, ipAddress, userAgent);
                } catch (Exception e) {
                    // Log the error but don't fail the login process
                    log.error("Failed to send login notification email: {}", e.getMessage(), e);
                }

        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Forgot password. Generates an OTP and sends it to the provided email", description = "If the user exists, sends a random 8-digit OTP to email with 3 minutes expiration")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OTP sent", content = @Content),
            @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content)
    })
    @PostMapping("/forgot-password/send-otp")
    public ResponseEntity<Map<String, String>> forgotPassword(@RequestBody @Valid ForgotPasswordRequest request) {
        personService.getPersonByEmail(request.getEmail());
        otpService.generateAndSendOtp(request.getEmail());
        Map<String, String> response = new HashMap<>();
        response.put("message", messageSource.getMessage("otp.sent", null, LocaleContextHolder.getLocale()));
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Validate OTP", description = "Validates the provided OTP for the given email")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OTP is valid", content = @Content),
            @ApiResponse(responseCode = "400", description = "Invalid OTP", content = @Content)
    })
    @PostMapping("forgot-password/validate-otp")
    public ResponseEntity<OtpValidationResponse> validateOtp(@RequestBody @Valid ValidateOtpRequest request) {

        return ResponseEntity.ok(otpService.validateOtp(request.getOtpEmail(), request.getOtpValue()));
    }

    @Operation(summary = "Update password", description = "Updates the password for the given email")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password updated successfully", content = @Content),
            @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content)
    })
    @PostMapping("forgot-password/update-password")
    public ResponseEntity<UpdatePasswordResponse> updatePassword(@RequestBody @Valid UpdatePasswordRequest request) {

        personService.updatePassword(request.getValidatedOtp().getOtpEmail(),
                request.getValidatedOtp().getOtpValue(), request.getPassword());
        return ResponseEntity.ok(new UpdatePasswordResponse("Password updated successfully"));
    }
}
