package com.hukapp.service.auth.modules.audit.entity;

import com.hukapp.service.auth.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * Entity for storing user activity audit logs
 * Captures comprehensive information about API operations for security and monitoring purposes
 */
@Entity
@Table(name = "audit_logs", indexes = {
    @Index(name = "idx_audit_user_email", columnList = "userEmail"),
    @Index(name = "idx_audit_endpoint", columnList = "endpointUrl"),
    @Index(name = "idx_audit_method", columnList = "httpMethod"),
    @Index(name = "idx_audit_status", columnList = "responseStatusCode"),
    @Index(name = "idx_audit_request_timestamp", columnList = "requestTimestamp"),
    @Index(name = "idx_audit_user_endpoint", columnList = "userEmail, endpointUrl"),
    @Index(name = "idx_audit_user_timestamp", columnList = "userEmail, requestTimestamp")
})
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class AuditLog extends BaseEntity {

    /**
     * Full endpoint URL that was accessed
     */
    @Column(nullable = false, length = 500)
    private String endpointUrl;

    /**
     * HTTP request method (GET, POST, PUT, DELETE, etc.)
     */
    @Column(nullable = false, length = 10)
    private String httpMethod;

    /**
     * Query parameters as JSON string
     */
    @Column(columnDefinition = "text")
    private String queryParameters;

    /**
     * Path/URL parameters as JSON string
     */
    @Column(columnDefinition = "text")
    private String pathParameters;

    /**
     * HTTP response status code
     */
    @Column(nullable = false)
    private Integer responseStatusCode;

    /**
     * Request processing time in milliseconds
     */
    @Column(nullable = false)
    private Long processingTimeMs;

    /**
     * User email from JWT token
     */
    @Column(nullable = false, length = 255)
    private String userEmail;

    /**
     * User roles from JWT token as JSON array string
     */
    @Column(columnDefinition = "text")
    private String userRoles;

    /**
     * Client IP address
     */
    @Column(length = 45) // IPv6 addresses can be up to 45 characters
    private String clientIpAddress;

    /**
     * User-Agent header information
     */
    @Column(columnDefinition = "text")
    private String userAgent;

    /**
     * Request timestamp when the request was received
     */
    @Column(nullable = false)
    private Instant requestTimestamp;

    /**
     * Response timestamp when the response was sent
     */
    @Column(nullable = false)
    private Instant responseTimestamp;

    /**
     * Additional request headers as JSON string (optional)
     */
    @Column(columnDefinition = "text")
    private String requestHeaders;

    /**
     * Request body size in bytes (for monitoring purposes)
     */
    @Column
    private Long requestBodySize;

    /**
     * Response body size in bytes (for monitoring purposes)
     */
    @Column
    private Long responseBodySize;

    /**
     * Session ID if available
     */
    @Column(length = 255)
    private String sessionId;

    /**
     * Any error message if the request failed
     */
    @Column(columnDefinition = "text")
    private String errorMessage;
}
