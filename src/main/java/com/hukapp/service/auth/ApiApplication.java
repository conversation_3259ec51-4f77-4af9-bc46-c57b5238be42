package com.hukapp.service.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.hukapp.service.auth.config.DatabaseProperties;
import com.hukapp.service.auth.config.RsaKeyProperties;
import com.hukapp.service.auth.modules.email.config.EmailProperties;
import com.hukapp.service.auth.modules.payment.config.IyzicoProperties;
import com.hukapp.service.auth.modules.audit.config.AuditLoggingProperties;

@SpringBootApplication
@EnableConfigurationProperties({DatabaseProperties.class, RsaKeyProperties.class, IyzicoProperties.class, EmailProperties.class, AuditLoggingProperties.class})
@EnableAspectJAutoProxy
@EnableScheduling // Enable scheduling for the application
@EnableAsync // Enable asynchronous processing
public class ApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(ApiApplication.class, args);
	}

}
