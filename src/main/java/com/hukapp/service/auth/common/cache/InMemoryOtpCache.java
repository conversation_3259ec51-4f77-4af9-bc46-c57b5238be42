package com.hukapp.service.auth.common.cache;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * A bean that provides a HashMap for storing email to OTP key-value pairs
 * with automatic expiration after 3 minutes.
 */
@Component
@Slf4j
public class InMemoryOtpCache {

    private static final long EXPIRATION_MINUTES = 3;
    
    // Using ConcurrentHashMap for thread safety
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    /**
     * Adds a key-value pair to the map. The entry will expire after 3 minutes.
     * 
     * @param email the email as key
     * @param otp the OTP value
     */
    public void put(String email, String otp) {
        Instant expirationTime = Instant.now().plus(EXPIRATION_MINUTES, ChronoUnit.MINUTES);
        cache.put(email, new CacheEntry(otp, expirationTime));
        log.debug("Added OTP for email '{}' to expiring map. Will expire at {}", email, expirationTime);
    }
    
    /**
     * Retrieves an OTP from the map if it exists and has not expired.
     * 
     * @param email the email key
     * @return the OTP value, or null if the key doesn't exist or has expired
     */
    public String get(String email) {
        CacheEntry entry = cache.get(email);
        if (entry == null) {
            return null;
        }
        
        // Check if the entry has expired
        if (entry.isExpired()) {
            cache.remove(email);
            log.debug("OTP for email '{}' was expired during retrieval and has been removed", email);
            return null;
        }
        
        return entry.getValue();
    }
    
    /**
     * Removes a key-value pair from the map.
     * 
     * @param email the email key to remove
     * @return the previous OTP value associated with the key, or null if there was no mapping
     */
    public String remove(String email) {
        CacheEntry entry = cache.remove(email);
        if (entry != null) {
            log.debug("Removed OTP for email '{}' from expiring map", email);
            return entry.getValue();
        }
        return null;
    }
    
    /**
     * Checks if the map contains a non-expired entry for the given key.
     * 
     * @param email the email key to check
     * @return true if the map contains a non-expired entry for the key, false otherwise
     */
    public boolean containsKey(String email) {
        CacheEntry entry = cache.get(email);
        if (entry == null) {
            return false;
        }
        
        // Check if the entry has expired
        if (entry.isExpired()) {
            cache.remove(email);
            log.debug("OTP for email '{}' was expired during containsKey check and has been removed", email);
            return false;
        }
        
        return true;
    }
    
    /**
     * Validates if the provided OTP matches the stored OTP for the given email.
     * 
     * @param email the email key
     * @param otp the OTP to validate
     * @return true if the OTP is valid, false otherwise
     */
    public boolean validateOtp(String email, String otp) {
        String storedOtp = get(email);
        return storedOtp != null && storedOtp.equals(otp);
    }
    
    /**
     * Returns the current size of the cache, including expired entries that haven't been cleaned up yet.
     * 
     * @return the number of entries in the cache
     */
    public int size() {
        return cache.size();
    }
    
    /**
     * Clears all entries from the map.
     */
    public void clear() {
        cache.clear();
        log.debug("Cleared all OTP entries from expiring map");
    }
    
    /**
     * Scheduled task that runs every minute to clean up expired entries.
     */
    @Scheduled(fixedRate = 60000) // 1 minute in milliseconds
    public void cleanupExpiredEntries() {
        int initialSize = cache.size();
        
        // Remove expired entries
        cache.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().isExpired();
            if (expired) {
                log.debug("Removing expired OTP entry for email '{}'", entry.getKey());
            }
            return expired;
        });
        
        int removedCount = initialSize - cache.size();
        if (removedCount > 0) {
            log.info("Cleaned up {} expired OTP entries from expiring map. Current size: {}", 
                    removedCount, cache.size());
        }
    }
    
    /**
     * Inner class to store the OTP value along with its expiration time.
     */
    private static class CacheEntry {
        private final String value;
        private final Instant expirationTime;
        
        public CacheEntry(String value, Instant expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }
        
        public String getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return Instant.now().isAfter(expirationTime);
        }
    }
}
