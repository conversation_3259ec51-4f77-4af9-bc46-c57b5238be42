package com.hukapp.service.auth.common.exception;

import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import com.hukapp.service.auth.common.exception.custom.AuthException;
import com.hukapp.service.auth.common.exception.custom.ResourceAlreadyExistsException;
import com.hukapp.service.auth.common.exception.custom.ResourceNotFoundException;
import com.hukapp.service.auth.common.exception.custom.SubscriptionRequiredException;
import com.hukapp.service.auth.common.exception.custom.TaskValidationException;
import com.hukapp.service.auth.common.exception.custom.UnexpectedStatusException;
import com.hukapp.service.auth.modules.coupon.exception.CouponException;
import com.hukapp.service.auth.modules.coupon.exception.CouponExpiredException;
import com.hukapp.service.auth.modules.coupon.exception.CouponUsageLimitExceededException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.FieldError;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    private final MessageSource messageSource;
    private final String errorKey;

    public GlobalExceptionHandler(MessageSource messageSource) {
        this.messageSource = messageSource;
        this.errorKey = messageSource.getMessage("response.error.key", null, LocaleContextHolder.getLocale());
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, String>> handleGenericException(Exception ex) {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, messageSource.getMessage("error.generic", null, locale));
        log.error(ex.getMessage());
        ex.printStackTrace();
        return new ResponseEntity<>(errors, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler({ NoResourceFoundException.class, NoHandlerFoundException.class,
            HttpRequestMethodNotSupportedException.class })
    public ResponseEntity<Map<String, String>> handleNoResourceFoundException(Exception ex) {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, messageSource.getMessage("error.invalid.address", null, locale));
        log.error(ex.getMessage(), ex);
        return new ResponseEntity<>(errors, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, String>> handleValidationExceptions(
            MethodArgumentNotValidException ex) {

        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        log.warn("Field validation error occured");
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<Map<String, String>> handleDataIntegrityViolationException(
            DataIntegrityViolationException ex) {
        Map<String, String> errors = new HashMap<>();
        String detailMessage = ex.getMostSpecificCause().getMessage();
        String detail = detailMessage.substring(detailMessage.indexOf("Detail:") + 8).trim().replace("\"", "===");
        detail = StringUtils.substringBetween(detail, "===", "===");
        errors.put(errorKey, "Veritabanı kural ihlali");
        errors.put("detail", detail);
        log.warn("Data integrity violation error occurred: {}", detail);
        return new ResponseEntity<>(errors, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(AuthException.class)
    public ResponseEntity<Map<String, String>> handleAuthException(AuthException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(UnexpectedStatusException.class)
    public ResponseEntity<Map<String, String>> handleUnexpectedStatusException(UnexpectedStatusException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.error("Unexpected status exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<Map<String, String>> handleHttpMessageNotReadableException(
            HttpMessageNotReadableException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, "Invalid request body");
        log.warn("Http message not readable exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<Map<String, String>> handleHttpMessageNotReadableException(
        MissingServletRequestParameterException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.warn("MissingServletRequestParameterException occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Map<String, String>> handleResourceNotFoundException(ResourceNotFoundException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.warn("Resource not found exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(ResourceAlreadyExistsException.class)
    public ResponseEntity<Map<String, String>> handleResourceAlreadyExistsException(ResourceAlreadyExistsException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.warn("Resource already exists exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(TaskValidationException.class)
    public ResponseEntity<Map<String, String>> handleTaskValidationException(TaskValidationException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.warn("Task validation exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(CouponException.class)
    public ResponseEntity<Map<String, String>> handleCouponException(CouponException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.warn("Coupon exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(CouponExpiredException.class)
    public ResponseEntity<Map<String, String>> handleCouponExpiredException(CouponExpiredException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.warn("Coupon expired exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(CouponUsageLimitExceededException.class)
    public ResponseEntity<Map<String, String>> handleCouponUsageLimitExceededException(CouponUsageLimitExceededException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());
        log.warn("Coupon usage limit exceeded exception occurred: {}", ex.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(SubscriptionRequiredException.class)
    public ResponseEntity<Map<String, String>> handleSubscriptionRequiredException(SubscriptionRequiredException ex) {
        Map<String, String> errors = new HashMap<>();
        errors.put(errorKey, ex.getMessage());

        // Add additional subscription details if available
        if (ex.getRequiredLevel() != null) {
            errors.put("requiredSubscription", ex.getRequiredLevel().getDisplayName());
        }
        if (ex.getUserLevel() != null) {
            errors.put("currentSubscription", ex.getUserLevel().getDisplayName());
        } else {
            errors.put("currentSubscription", "Aktif abonelik yok");
        }

        log.warn("Subscription required exception occurred for user {}: {}",
            ex.getUserEmail(), ex.getDetailedMessage());

        // Return HTTP 402 Payment Required
        return new ResponseEntity<>(errors, HttpStatus.PAYMENT_REQUIRED);
    }

}