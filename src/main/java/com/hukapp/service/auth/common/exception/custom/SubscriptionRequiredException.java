package com.hukapp.service.auth.common.exception.custom;

import com.hukapp.service.auth.modules.payment.enums.ProductType;

/**
 * Exception thrown when a user attempts to access a feature that requires
 * a subscription they don't have. This exception should result in an HTTP 402
 * (Payment Required) response.
 */
public class SubscriptionRequiredException extends RuntimeException {
    
    private final ProductType requiredLevel;
    private final ProductType userLevel;
    private final String userEmail;
    
    /**
     * Creates a new SubscriptionRequiredException with a custom message.
     * 
     * @param message the exception message
     */
    public SubscriptionRequiredException(String message) {
        super(message);
        this.requiredLevel = null;
        this.userLevel = null;
        this.userEmail = null;
    }
    
    /**
     * Creates a new SubscriptionRequiredException with detailed subscription information.
     * 
     * @param message the exception message
     * @param requiredLevel the subscription level required for access
     * @param userLevel the user's current subscription level (can be null)
     * @param userEmail the user's email address
     */
    public SubscriptionRequiredException(String message, ProductType requiredLevel, 
                                       ProductType userLevel, String userEmail) {
        super(message);
        this.requiredLevel = requiredLevel;
        this.userLevel = userLevel;
        this.userEmail = userEmail;
    }
    
    /**
     * Creates a new SubscriptionRequiredException with a cause.
     * 
     * @param message the exception message
     * @param cause the underlying cause
     */
    public SubscriptionRequiredException(String message, Throwable cause) {
        super(message, cause);
        this.requiredLevel = null;
        this.userLevel = null;
        this.userEmail = null;
    }
    
    /**
     * Gets the required subscription level.
     * 
     * @return the required subscription level, or null if not specified
     */
    public ProductType getRequiredLevel() {
        return requiredLevel;
    }
    
    /**
     * Gets the user's current subscription level.
     * 
     * @return the user's subscription level, or null if they have no subscription
     */
    public ProductType getUserLevel() {
        return userLevel;
    }
    
    /**
     * Gets the user's email address.
     * 
     * @return the user's email, or null if not specified
     */
    public String getUserEmail() {
        return userEmail;
    }
    
    /**
     * Creates a formatted error message with subscription details.
     * 
     * @return a detailed error message
     */
    public String getDetailedMessage() {
        if (requiredLevel == null) {
            return getMessage();
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Abonelik gerekli: ").append(requiredLevel.getDisplayName());
        
        if (userLevel != null) {
            sb.append(". Aktif abonelik seviyeniz: ").append(userLevel.getDisplayName());
        } else {
            sb.append(". Aktif abonelik yok.");
        }
        
        if (userEmail != null) {
            sb.append(" Kullanıcı: ").append(userEmail);
        }
        
        return sb.toString();
    }
}
