package com.hukapp.service.auth.common.cache;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hukapp.service.auth.common.repository.ApplicationParameterRepository;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class ApplicationParameterCache {

    private final ApplicationParameterRepository applicationParameterRepository;
    private final Map<String, String> cachedParameters = new ConcurrentHashMap<>();

    @PostConstruct
    @Scheduled(fixedRate = 300000) // Refresh every 5 minutes
    public void refreshCache() {
        Map<String, String> updatedParameters = new HashMap<>();
        applicationParameterRepository.findAll().forEach(parameter -> {
            updatedParameters.put(parameter.getParameterName(), parameter.getParameterValue());
        });
        if (!updatedParameters.isEmpty()) {
            this.cachedParameters.clear();
            this.cachedParameters.putAll(updatedParameters);
            log.info("Application parameters cache refreshed, new cache size: {}", updatedParameters.size());
        } else {
            log.warn("Application parameters cache refresh failed, no parameters found");
        }
    }

    public String getParameterValue(String parameterName) {
        return cachedParameters.get(parameterName);
    }

    public Map<String, String> getAllParameters() {
        return new HashMap<>(cachedParameters);
    }

}
