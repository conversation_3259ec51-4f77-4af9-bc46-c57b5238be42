package com.hukapp.service.auth.common.cache;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * A bean that provides a HashMap for storing String to String key-value pairs
 * with automatic expiration after 1 hour.
 */
@Component
@Slf4j
public class InMemoryJsidCache {

    private static final long EXPIRATION_MINUTES = 15;
    
    // Using ConcurrentHashMap for thread safety
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    /**
     * Adds a key-value pair to the map. The entry will expire after 1 hour.
     * 
     * @param key the key
     * @param value the value
     */
    public void put(String key, String value) {
        Instant expirationTime = Instant.now().plus(EXPIRATION_MINUTES, ChronoUnit.MINUTES);
        cache.put(key, new CacheEntry(value, expirationTime));
        log.debug("Added entry with key '{}' to expiring map. Will expire at {}", key, expirationTime);
    }
    
    /**
     * Retrieves a value from the map if it exists and has not expired.
     * 
     * @param key the key
     * @return the value, or null if the key doesn't exist or has expired
     */
    public String get(String key) {
        CacheEntry entry = cache.get(key);
        if (entry == null) {
            return null;
        }
        
        // Check if the entry has expired
        if (entry.isExpired()) {
            cache.remove(key);
            log.debug("Entry with key '{}' was expired during retrieval and has been removed", key);
            return null;
        }
        
        return entry.getValue();
    }
    
    /**
     * Removes a key-value pair from the map.
     * 
     * @param key the key to remove
     * @return the previous value associated with the key, or null if there was no mapping
     */
    public String remove(String key) {
        CacheEntry entry = cache.remove(key);
        if (entry != null) {
            log.debug("Removed entry with key '{}' from expiring map", key);
            return entry.getValue();
        }
        return null;
    }
    
    /**
     * Checks if the map contains a non-expired entry for the given key.
     * 
     * @param key the key to check
     * @return true if the map contains a non-expired entry for the key, false otherwise
     */
    public boolean containsKey(String key) {
        CacheEntry entry = cache.get(key);
        if (entry == null) {
            return false;
        }
        
        // Check if the entry has expired
        if (entry.isExpired()) {
            cache.remove(key);
            log.debug("Entry with key '{}' was expired during containsKey check and has been removed", key);
            return false;
        }
        
        return true;
    }
    
    /**
     * Returns the current size of the cache, including expired entries that haven't been cleaned up yet.
     * 
     * @return the number of entries in the cache
     */
    public int size() {
        return cache.size();
    }
    
    /**
     * Clears all entries from the map.
     */
    public void clear() {
        cache.clear();
        log.debug("Cleared all entries from expiring map");
    }
    
    /**
     * Scheduled task that runs every 10 minutes to clean up expired entries.
     */
    @Scheduled(fixedRate = 600000) // 10 minutes in milliseconds
    public void cleanupExpiredEntries() {
        int initialSize = cache.size();
        
        // Remove expired entries
        cache.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().isExpired();
            if (expired) {
                log.debug("Removing expired entry with key '{}'", entry.getKey());
            }
            return expired;
        });
        
        int removedCount = initialSize - cache.size();
        if (removedCount > 0) {
            log.info("Cleaned up {} expired entries from expiring map. Current size: {}", 
                    removedCount, cache.size());
        }
    }
    
    /**
     * Inner class to store the value along with its expiration time.
     */
    private static class CacheEntry {
        private final String value;
        private final Instant expirationTime;
        
        public CacheEntry(String value, Instant expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }
        
        public String getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return Instant.now().isAfter(expirationTime);
        }
    }
}
