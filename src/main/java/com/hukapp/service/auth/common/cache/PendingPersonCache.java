package com.hukapp.service.auth.common.cache;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hukapp.service.auth.modules.person.dto.request.PersonCreateRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * A bean that provides a HashMap for storing pending person registrations
 * with automatic expiration after 10 minutes.
 */
@Component
@Slf4j
public class PendingPersonCache {

    private static final long EXPIRATION_MINUTES = 4;
    
    // Using ConcurrentHashMap for thread safety
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    /**
     * Adds a pending person registration to the map. The entry will expire after 10 minutes.
     * 
     * @param email the email as key
     * @param personCreateRequest the person registration data
     */
    public void put(String email, PersonCreateRequest personCreateRequest) {
        Instant expirationTime = Instant.now().plus(EXPIRATION_MINUTES, ChronoUnit.MINUTES);
        cache.put(email, new CacheEntry(personCreateRequest, expirationTime));
        log.debug("Added pending registration for email '{}' to expiring map. Will expire at {}", email, expirationTime);
    }
    
    /**
     * Retrieves a pending person registration from the map if it exists and has not expired.
     * 
     * @param email the email key
     * @return the PersonCreateRequest, or null if the key doesn't exist or has expired
     */
    public PersonCreateRequest get(String email) {
        CacheEntry entry = cache.get(email);
        if (entry == null) {
            return null;
        }
        
        // Check if the entry has expired
        if (entry.isExpired()) {
            cache.remove(email);
            log.debug("Pending registration for email '{}' was expired during retrieval and has been removed", email);
            return null;
        }
        
        return entry.getValue();
    }
    
    /**
     * Removes a pending person registration from the map.
     * 
     * @param email the email key to remove
     * @return the previous PersonCreateRequest associated with the key, or null if there was no mapping
     */
    public PersonCreateRequest remove(String email) {
        CacheEntry entry = cache.remove(email);
        if (entry != null) {
            log.debug("Removed pending registration for email '{}' from expiring map", email);
            return entry.getValue();
        }
        return null;
    }
    
    /**
     * Checks if the map contains a non-expired entry for the given key.
     * 
     * @param email the email key to check
     * @return true if the map contains a non-expired entry for the key, false otherwise
     */
    public boolean containsKey(String email) {
        CacheEntry entry = cache.get(email);
        if (entry == null) {
            return false;
        }
        
        // Check if the entry has expired
        if (entry.isExpired()) {
            cache.remove(email);
            log.debug("Pending registration for email '{}' was expired during containsKey check and has been removed", email);
            return false;
        }
        
        return true;
    }
    
    /**
     * Returns the current size of the cache, including expired entries that haven't been cleaned up yet.
     * 
     * @return the number of entries in the cache
     */
    public int size() {
        return cache.size();
    }
    
    /**
     * Clears all entries from the map.
     */
    public void clear() {
        cache.clear();
        log.debug("Cleared all pending registration entries from expiring map");
    }
    
    /**
     * Scheduled task that runs every 5 minutes to clean up expired entries.
     */
    @Scheduled(fixedRate = 300000) // 5 minutes in milliseconds
    public void cleanupExpiredEntries() {
        int initialSize = cache.size();
        
        // Remove expired entries
        cache.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().isExpired();
            if (expired) {
                log.debug("Removing expired pending registration entry for email '{}'", entry.getKey());
            }
            return expired;
        });
        
        int removedCount = initialSize - cache.size();
        if (removedCount > 0) {
            log.info("Cleaned up {} expired pending registration entries from expiring map. Current size: {}", 
                    removedCount, cache.size());
        }
    }
    
    /**
     * Inner class to store the PersonCreateRequest along with its expiration time.
     */
    private static class CacheEntry {
        private final PersonCreateRequest value;
        private final Instant expirationTime;
        
        public CacheEntry(PersonCreateRequest value, Instant expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }
        
        public PersonCreateRequest getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return Instant.now().isAfter(expirationTime);
        }
    }
}
