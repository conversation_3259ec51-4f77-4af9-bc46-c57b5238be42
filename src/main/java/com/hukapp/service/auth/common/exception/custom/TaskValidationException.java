package com.hukapp.service.auth.common.exception.custom;

/**
 * Exception thrown when task validation fails in the service layer.
 * This exception is used for business logic validation errors related to tasks.
 */
public class TaskValidationException extends RuntimeException {

    public TaskValidationException(String message) {
        super(message);
    }

    public TaskValidationException(String message, Throwable cause) {
        super(message, cause);
    }
    
}
