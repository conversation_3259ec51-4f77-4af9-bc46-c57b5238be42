package com.hukapp.service.auth.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.hukapp.service.auth.modules.payment.enums.ProductType;

/**
 * Annotation to mark methods or classes that require a specific subscription level.
 * This annotation is used by the SubscriptionValidationAspect to enforce
 * subscription-based access control on API endpoints.
 * 
 * <p>Usage examples:
 * <pre>
 * {@code
 * @RequireSubscription(ProductType.BASIC)
 * @GetMapping("/api/basic-feature")
 * public ResponseEntity<?> basicFeature() { ... }
 * 
 * @RequireSubscription(ProductType.PREMIUM)
 * @GetMapping("/api/premium-feature")
 * public ResponseEntity<?> premiumFeature() { ... }
 * }
 * </pre>
 * 
 * <p>The annotation can be applied to:
 * <ul>
 * <li>Methods - to protect individual endpoints</li>
 * <li>Classes - to protect all endpoints in a controller</li>
 * </ul>
 * 
 * <p>When applied to both class and method, the method-level annotation takes precedence.
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireSubscription {
    
    /**
     * The minimum subscription level required to access the annotated endpoint.
     * Users with this subscription level or higher will be granted access.
     * 
     * @return the required subscription type
     */
    ProductType value() default ProductType.BASIC;
    
    /**
     * Optional message to include in the error response when access is denied.
     * If not specified, a default message will be used.
     * 
     * @return custom error message
     */
    String message() default "";
    
}
