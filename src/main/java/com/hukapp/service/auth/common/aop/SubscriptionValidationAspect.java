package com.hukapp.service.auth.common.aop;

import java.lang.reflect.Method;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.hukapp.service.auth.common.annotation.RequireSubscription;
import com.hukapp.service.auth.common.exception.custom.SubscriptionRequiredException;
import com.hukapp.service.auth.modules.payment.enums.ProductType;
import com.hukapp.service.auth.modules.payment.service.SubscriptionValidationService;
import com.hukapp.service.auth.modules.person.entity.Person;
import com.hukapp.service.auth.modules.person.service.PersonService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AOP Aspect that intercepts method calls annotated with @RequireSubscription
 * and validates that the authenticated user has the required subscription level.
 * 
 * This aspect applies to:
 * - Methods annotated with @RequireSubscription
 * - Methods in classes annotated with @RequireSubscription
 * - All /api/** endpoints (except /api/admin/** and /auth/user/**)
 * 
 * The aspect will throw SubscriptionRequiredException if the user doesn't have
 * the required subscription, which will be handled by GlobalExceptionHandler
 * to return HTTP 402 (Payment Required).
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class SubscriptionValidationAspect {
    
    private final SubscriptionValidationService subscriptionValidationService;
    private final PersonService personService;
    
    /**
     * Pointcut for methods annotated with @RequireSubscription
     */
    @Pointcut("@annotation(com.hukapp.service.auth.common.annotation.RequireSubscription)")
    public void methodWithRequireSubscription() {}
    
    /**
     * Pointcut for classes annotated with @RequireSubscription
     */
    @Pointcut("@within(com.hukapp.service.auth.common.annotation.RequireSubscription)")
    public void classWithRequireSubscription() {}
    
    /**
     * Pointcut for API endpoints that should be protected by subscription validation.
     * Includes all /api/** endpoints except /api/admin/** and /auth/user/**
     */
    @Pointcut("execution(* com.hukapp.service.auth.modules.*.controller.*.*(..))" +
              "&& !execution(* com.hukapp.service.auth.modules.person.controller.*.*(..))" +
              "&& !execution(* com.hukapp.service.auth.modules.payment.controller.*.*(..))" +
              "&& !execution(* com.hukapp.service.auth.modules.coupon.controller.*.*(..))")
    public void apiEndpoints() {}
    
    /**
     * Around advice that validates subscription requirements before method execution.
     * This applies to methods or classes annotated with @RequireSubscription.
     */
    @Around("methodWithRequireSubscription() || classWithRequireSubscription() || apiEndpoints()")
    public Object validateSubscriptionAnnotation(ProceedingJoinPoint joinPoint) throws Throwable {
        validateSubscription(getRequireSubscriptionAnnotation(joinPoint));
        return joinPoint.proceed();
    }
    
    /**
     * Core subscription validation logic.
     */
    private void validateSubscription(RequireSubscription annotation) throws Throwable {
        
        // Get the authenticated user
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("No authenticated user found, skipping subscription validation");
            return;
        }
        
        /*
         * Get the required subscription level from the annotation.
         * If no annotation is present, default to BASIC subscription.
         * 
         * Set default values for other annotation properties.
         */
        String userEmail = authentication.getName();
        ProductType requiredLevel = annotation == null ? ProductType.BASIC : annotation.value();
        String customMessage = annotation != null ? annotation.message() : "";
        
        log.debug("Validating subscription for user {} with required level {}", userEmail, requiredLevel);
        
        try {
            // Check for admin bypass
            if (hasAdminRole(authentication)) {
                log.debug("User has admin role, skipping subscription validation");
                return;
            }
            
            // Get user entity for detailed validation
            Person person = personService.getPersonByEmailOrElseThrow(userEmail);
            
            // Validate subscription
            if (!subscriptionValidationService.hasValidSubscription(person, requiredLevel)) {
                ProductType userLevel = subscriptionValidationService.getHighestSubscriptionLevel(person);
                
                String message = customMessage.isEmpty() 
                    ? subscriptionValidationService.getSubscriptionRequirementMessage(requiredLevel)
                    : customMessage;
                
                log.warn("Subscription validation failed for user {}: required={}, current={}", 
                    userEmail, requiredLevel, userLevel);
                
                throw new SubscriptionRequiredException(message, requiredLevel, userLevel, userEmail);
            }
            
            log.debug("Subscription validation passed for user {}", userEmail);
            
        } catch (SubscriptionRequiredException e) {
            // Re-throw subscription exceptions
            throw e;
        } catch (Exception e) {
            log.error("Error during subscription validation for user {}: {}", userEmail, e.getMessage(), e);
            // On validation error, deny access for security
            throw new SubscriptionRequiredException(
                "Unable to validate subscription. Please try again later.", e);
        }
    }
    
    /**
     * Gets the @RequireSubscription annotation from the method or class.
     * Method-level annotation takes precedence over class-level.
     */
    private RequireSubscription getRequireSubscriptionAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // Check method-level annotation first
        RequireSubscription methodAnnotation = AnnotationUtils.findAnnotation(method, RequireSubscription.class);
        if (methodAnnotation != null) {
            return methodAnnotation;
        }
        
        // Check class-level annotation
        Class<?> targetClass = joinPoint.getTarget().getClass();
        return AnnotationUtils.findAnnotation(targetClass, RequireSubscription.class);
    }
    
    /**
     * Checks if the authenticated user has admin role.
     */
    private boolean hasAdminRole(Authentication authentication) {
        return authentication.getAuthorities().stream()
            .anyMatch(authority -> "ROLE_ADMIN".equals(authority.getAuthority()));
    }
}
