package com.hukapp.service.auth.common.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class ApplicationParameter extends BaseEntity {

    @Column(nullable = false, unique = true)
    private String parameterName;

    @Column(nullable = false)
    private String parameterValue;

}
