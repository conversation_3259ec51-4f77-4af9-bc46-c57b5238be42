{"properties": [{"name": "app.environment", "type": "java.lang.String", "description": "A description for 'app.environment'"}, {"name": "rsa.public-key", "type": "java.lang.String", "description": "RSA public key PEM file"}, {"name": "rsa.private-key", "type": "java.lang.String", "description": "RSA private key PEM file"}, {"name": "iyzico.api-key", "type": "java.lang.String", "description": "iyzico API key"}, {"name": "iyzico.secret-key", "type": "java.lang.String", "description": "iyzico Secret key"}, {"name": "iyzico.base-url", "type": "java.lang.String", "description": "iyzico API base URL"}, {"name": "email.host", "type": "java.lang.String", "description": "SMTP server host"}, {"name": "email.port", "type": "java.lang.Integer", "description": "SMTP server port"}, {"name": "email.username", "type": "java.lang.String", "description": "Email username for authentication"}, {"name": "email.password", "type": "java.lang.String", "description": "Email password for authentication"}, {"name": "email.from", "type": "java.lang.String", "description": "Default from address for emails"}, {"name": "email.auth", "type": "java.lang.Bo<PERSON>an", "description": "Enable SMTP authentication"}, {"name": "email.start-tls", "type": "java.lang.Bo<PERSON>an", "description": "Enable STARTTLS for SMTP connection"}, {"name": "email.ssl", "type": "java.lang.Bo<PERSON>an", "description": "Enable SSL for SMTP connection"}, {"name": "email.connection-timeout", "type": "java.lang.Integer", "description": "Connection timeout in milliseconds"}, {"name": "email.timeout", "type": "java.lang.Integer", "description": "Socket read timeout in milliseconds"}, {"name": "email.write-timeout", "type": "java.lang.Integer", "description": "Socket write timeout in milliseconds"}, {"name": "email.debug", "type": "java.lang.Bo<PERSON>an", "description": "Enable debug mode for troubleshooting"}, {"name": "audit.logging.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Enable or disable audit logging globally"}, {"name": "audit.logging.excluded-endpoints", "type": "java.util.List<java.lang.String>", "description": "List of endpoint patterns to exclude from audit logging"}, {"name": "audit.logging.log-request-headers", "type": "java.lang.Bo<PERSON>an", "description": "Enable logging of request headers (excluding sensitive ones)"}, {"name": "audit.logging.log-body-sizes", "type": "java.lang.Bo<PERSON>an", "description": "Enable calculation and logging of request/response body sizes"}, {"name": "audit.logging.retention-days", "type": "java.lang.Integer", "description": "Maximum number of days to retain audit logs"}, {"name": "audit.logging.auto-cleanup-enabled", "type": "java.lang.Bo<PERSON>an", "description": "Enable automatic cleanup of old audit logs"}, {"name": "audit.logging.cleanup-cron-expression", "type": "java.lang.String", "description": "Cron expression for automatic cleanup job"}, {"name": "audit.logging.slow-request-threshold-ms", "type": "java.lang.Long", "description": "<PERSON><PERSON><PERSON><PERSON> in milliseconds to consider a request as slow"}, {"name": "audit.logging.log-error-details", "type": "java.lang.Bo<PERSON>an", "description": "Enable detailed error logging"}, {"name": "audit.logging.max-error-message-length", "type": "java.lang.Integer", "description": "Maximum length for error messages to store"}, {"name": "audit.logging.log-session-id", "type": "java.lang.Bo<PERSON>an", "description": "Enable session ID logging"}, {"name": "audit.logging.log-anonymous-requests", "type": "java.lang.Bo<PERSON>an", "description": "Enable logging for anonymous/unauthenticated requests"}, {"name": "audit.logging.max-results-limit", "type": "java.lang.Integer", "description": "Maximum number of audit logs to return in admin queries"}]}