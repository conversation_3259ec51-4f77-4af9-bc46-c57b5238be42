<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme Durumu - AVAS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 30px;
        }
        
        .status-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }
        
        .success {
            color: #28a745;
        }
        
        .failure {
            color: #dc3545;
        }
        
        .pending {
            color: #ffc107;
        }
        
        .canceled {
            color: #6c757d;
        }
        
        .status-title {
            font-size: 2em;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .status-message {
            font-size: 1.2em;
            margin-bottom: 30px;
            color: #666;
            line-height: 1.5;
        }
        
        .payment-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        
        .detail-value {
            color: #212529;
            font-weight: 500;
        }
        
        .error-details {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #721c24;
        }
        
        .error-title {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .action-buttons {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
            }
            
            .logo {
                font-size: 2em;
            }
            
            .status-icon {
                font-size: 3em;
            }
            
            .status-title {
                font-size: 1.5em;
            }
            
            .btn {
                display: block;
                margin: 10px 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">AVAS</div>
        
        <!-- Success Status -->
        <div th:if="${paymentStatus.successful}">
            <div class="status-icon success">✓</div>
            <h1 class="status-title success">Ödeme Başarılı!</h1>
            <p class="status-message">Ödemeniz başarıyla tamamlandı. Hizmetlerimizi kullanmaya başlayabilirsiniz.</p>
        </div>
        
        <!-- Failure Status -->
        <div th:if="${!paymentStatus.successful and paymentStatus.status.name() == 'FAILURE'}">
            <div class="status-icon failure">✗</div>
            <h1 class="status-title failure">Ödeme Başarısız</h1>
            <p class="status-message">Ödemeniz tamamlanamadı. Lütfen tekrar deneyiniz veya farklı bir ödeme yöntemi kullanınız.</p>
        </div>
        
        <!-- Pending Status -->
        <div th:if="${paymentStatus.status.name() == 'PENDING'}">
            <div class="status-icon pending">⏳</div>
            <h1 class="status-title pending">Ödeme Bekleniyor</h1>
            <p class="status-message">Ödemeniz işleniyor. Lütfen bekleyiniz...</p>
        </div>
        
        <!-- Canceled Status -->
        <div th:if="${paymentStatus.status.name() == 'CANCELED'}">
            <div class="status-icon canceled">⊘</div>
            <h1 class="status-title canceled">Ödeme İptal Edildi</h1>
            <p class="status-message">Ödeme işlemi iptal edildi.</p>
        </div>
        
        <!-- Payment Details -->
        <div class="payment-details">
            <div class="detail-row">
                <span class="detail-label">Ödeme ID:</span>
                <span class="detail-value" th:text="${paymentStatus.paymentId}">-</span>
            </div>
            <div class="detail-row" th:if="${paymentStatus.successful}">
                <span class="detail-label">Başarılı:</span>
                <span class="detail-value success">✓ Evet</span>
            </div>
            <div class="detail-row" th:if="${!paymentStatus.successful}">
                <span class="detail-label">Başarılı:</span>
                <span class="detail-value failure">✗ Hayır</span>
            </div>
        </div>
        
        <!-- Error Details (if payment failed) -->
        <div class="error-details" th:if="${!paymentStatus.successful and (paymentStatus.errorCode != null or paymentStatus.errorMessage != null)}">
            <div class="error-title">Hata Detayları:</div>
            <div th:if="${paymentStatus.errorCode != null}">
                <strong>Hata Kodu:</strong> <span th:text="${paymentStatus.errorCode}"></span>
            </div>
            <div th:if="${paymentStatus.errorMessage != null}">
                <strong>Hata Mesajı:</strong> <span th:text="${paymentStatus.errorMessage}"></span>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <a th:href="${avasDashboard}" class="btn btn-primary" th:if="${paymentStatus.successful}">Hesabıma Git</a>
            <a th:href="${retryPaymentUrl}" class="btn btn-primary" th:if="${!paymentStatus.successful}">Tekrar Dene</a>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>Bu sayfa otomatik olarak oluşturulmuştur.</p>
        </div>
    </div>
</body>
</html>
