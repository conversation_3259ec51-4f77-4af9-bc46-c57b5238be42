app:
  environment: dev
spring:
  application:
    name: hukapp-auth-service
  datasource:
    url: *********************************************
    username: postgres  # TODO: Store in a secure place as encrypted
    password: Huk-app.41@ # TODO: Store in a secure place as encrypted
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-timeout: 5000
      maximum-pool-size: 150
      minimum-idle: 1
      idle-timeout: 120000
      max-lifetime: 300000
      pool-name: hukapp-db-connection-pool
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  mail:
    host: ${email.host}
    port: ${email.port}
    username: ${email.username}
    password: ${email.password}
    properties:
      mail:
        smtp:
          auth: ${email.auth}
          "[ssl.enable]": ${email.ssl}
          "[starttls.enable]": ${email.start-tls}
          "[socketFactory.port]": ${email.port}
          "[socketFactory.class]": javax.net.ssl.SSLSocketFactory
          "[socketFactory.fallback]": false
          "[connectiontimeout]": ${email.connection-timeout}
          "[timeout]": ${email.timeout}
          "[writetimeout]": ${email.write-timeout}
          debug: ${email.debug}
server:
  port: 4244
  error:
    whitelabel:
      enabled: false
    include-stacktrace: never
    include-message: never
logging:
  level:
    "[org.springframework]": ERROR
    "[com.hukapp]": DEBUG
    "[com.zaxxer.hikari]": DEBUG
    "[org.springframework.security]": DEBUG
springdoc:
  api-docs:
    version: OPENAPI_3_0
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    display-request-duration: true
    urls-primary-name: Local Development Server
    disable-swagger-default-url: true
rsa:
  public-key: classpath:jwtresource/public.pem
  private-key: classpath:jwtresource/private.pem
iyzico:
  api-key: sandbox-******************************** # Replace with actual API key in production
  secret-key: sandbox-******************************** # Replace with actual secret key in production
  base-url: https://sandbox-api.iyzipay.com # Use https://api.iyzipay.com for production
management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"

# Audit Logging Configuration
audit:
  logging:
    enabled: true
    excluded-endpoints:
      - "/auth/**"
    log-request-headers: true
    log-body-sizes: true
    retention-days: 365
    auto-cleanup-enabled: true
    cleanup-cron-expression: "0 0 2 * * ?"
    slow-request-threshold-ms: 1000
    log-error-details: true
    max-error-message-length: 1000
    log-session-id: true
    log-anonymous-requests: false
    max-results-limit: 1000
# Email configuration
email:
  # Basic SMTP settings
  host: mail.api4j.com.tr # SMTP server host
  port: 465 # SMTP server port
  username: <EMAIL> # Replace with actual email in production
  password: P3zVG0+ub7M:1h # Replace with actual app password in production
  from: AVAS <<EMAIL>> # Replace with actual email in production

  # Security settings
  auth: true # Enable authentication
  start-tls: false # Enable STARTTLS (for port 587)
  ssl: true # Enable SSL (for port 465)

  # Connection settings
  connection-timeout: 5000 # Connection timeout in milliseconds
  timeout: 5000 # Socket read timeout in milliseconds
  write-timeout: 5000 # Socket write timeout in milliseconds

  # Debug settings
  debug: true # Enable debug mode for troubleshooting

# Example configurations for different email providers:

# Gmail:
# email:
#   host: smtp.gmail.com
#   port: 587
#   username: <EMAIL>
#   password: your-app-password
# <AUTHOR> <EMAIL>
#   auth: true
#   start-tls: true
#   ssl: false

# Office 365:
# email:
#   host: smtp.office365.com
#   port: 587
#   username: <EMAIL>
#   password: your-password
# <AUTHOR> <EMAIL>
#   auth: true
#   start-tls: true
#   ssl: false

# Amazon SES:
# email:
#   host: email-smtp.us-east-1.amazonaws.com
#   port: 587
#   username: your-ses-smtp-username
#   password: your-ses-smtp-password
# <AUTHOR> <EMAIL>
#   auth: true
#   start-tls: true
#   ssl: false

# SSL Configuration (for port 465):
# email:
#   host: smtp.gmail.com
#   port: 465
#   username: <EMAIL>
#   password: your-password
# <AUTHOR> <EMAIL>
#   auth: true
#   start-tls: false
#   ssl: true