# User Activity Audit Logging System

## Overview

The User Activity Audit Logging System is a comprehensive solution for capturing and monitoring all API operations in the application. It uses Spring Boot's AOP (Aspect-Oriented Programming) to intercept controller methods and asynchronously log detailed information about user activities.

## Features

- **Comprehensive Data Capture**: Logs endpoint URLs, HTTP methods, query/path parameters, response status codes, processing times, user information, client IP addresses, and more
- **Asynchronous Processing**: Uses `@Async` annotation to ensure zero impact on API response times
- **Clean Architecture**: Follows the project's established patterns with proper service layer separation
- **Configurable**: Easily enable/disable logging, exclude endpoints, and configure retention policies
- **Admin Interface**: Comprehensive admin endpoints for viewing and analyzing audit logs
- **Database Optimized**: Proper indexing for efficient querying and reporting
- **Automatic Cleanup**: Scheduled cleanup of old audit logs based on retention policies

## Architecture

### Components

1. **AuditLog Entity**: Database entity storing all audit information
2. **AuditLogRepository**: JPA repository with custom queries for efficient data retrieval
3. **AuditLogService**: Service layer with async methods for saving and retrieving audit logs
4. **UserActivityLoggingAspect**: AOP aspect that intercepts all controller methods
5. **AdminAuditLogController**: Admin endpoints for viewing and managing audit logs
6. **AuditLoggingProperties**: Configuration properties for customizing behavior
7. **AuditLogCleanupService**: Scheduled service for automatic cleanup of old logs

### Data Captured

- **Request Information**: Endpoint URL, HTTP method, query parameters, path parameters
- **Response Information**: Status code, processing time, response body size
- **User Information**: Email from JWT token, user roles
- **Client Information**: IP address, User-Agent header
- **Timing Information**: Request and response timestamps
- **Additional Data**: Session ID, request headers, error messages

## Configuration

### Application Properties

```yaml
audit:
  logging:
    enabled: true                           # Enable/disable audit logging
    excluded-endpoints:                     # Endpoints to exclude from logging
      - "/api/admin/**"
      - "/auth/**"
      - "/actuator/**"
      - "/swagger-ui/**"
      - "/v3/api-docs/**"
      - "/favicon.ico"
    log-request-headers: true              # Log request headers (excluding sensitive ones)
    log-body-sizes: true                   # Log request/response body sizes
    retention-days: 90                     # Number of days to retain audit logs
    auto-cleanup-enabled: true             # Enable automatic cleanup
    cleanup-cron-expression: "0 0 2 * * ?" # Cleanup schedule (daily at 2 AM)
    slow-request-threshold-ms: 1000        # Threshold for slow request detection
    log-error-details: true               # Log detailed error information
    max-error-message-length: 1000        # Maximum error message length
    log-session-id: true                  # Log session IDs
    log-anonymous-requests: false         # Log unauthenticated requests
    max-results-limit: 1000               # Maximum results in admin queries
```

## Admin Endpoints

### Base URL: `/api/admin/audit-logs`

- `GET /` - Get all audit logs
- `GET /user/{userEmail}` - Get audit logs by user email
- `GET /user/{userEmail}/date-range` - Get audit logs by user and date range
- `GET /endpoint` - Get audit logs by endpoint URL
- `GET /failed-requests` - Get failed requests (status >= 400)
- `GET /slow-requests` - Get slow requests above threshold
- `GET /statistics` - Get comprehensive audit statistics
- `GET /recent` - Get recent audit logs
- `GET /errors` - Get audit logs with error messages
- `GET /client-ip/{clientIp}` - Get audit logs by client IP
- `DELETE /cleanup` - Manually trigger cleanup of old logs

## Database Schema

### AuditLog Table

```sql
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    endpoint_url VARCHAR(500) NOT NULL,
    http_method VARCHAR(10) NOT NULL,
    query_parameters TEXT,
    path_parameters TEXT,
    response_status_code INTEGER NOT NULL,
    processing_time_ms BIGINT NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    user_roles TEXT,
    client_ip_address VARCHAR(45),
    user_agent TEXT,
    request_timestamp TIMESTAMP NOT NULL,
    response_timestamp TIMESTAMP NOT NULL,
    request_headers TEXT,
    request_body_size BIGINT,
    response_body_size BIGINT,
    session_id VARCHAR(255),
    error_message TEXT,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 0
);
```

### Indexes

- `idx_audit_user_email` on `user_email`
- `idx_audit_endpoint` on `endpoint_url`
- `idx_audit_method` on `http_method`
- `idx_audit_status` on `response_status_code`
- `idx_audit_request_timestamp` on `request_timestamp`
- `idx_audit_user_endpoint` on `user_email, endpoint_url`
- `idx_audit_user_timestamp` on `user_email, request_timestamp`

## Security Considerations

- **Sensitive Data**: Authorization headers, cookies, and passwords are excluded from logging
- **Admin Access**: All admin endpoints require ADMIN role
- **Data Retention**: Automatic cleanup prevents indefinite data accumulation
- **Async Processing**: Logging failures don't affect main API operations

## Performance Impact

- **Zero Impact**: Asynchronous processing ensures no performance degradation
- **Optimized Queries**: Database indexes for efficient data retrieval
- **Configurable**: Can be disabled or configured to exclude high-traffic endpoints
- **Cleanup**: Automatic removal of old data maintains performance

## Monitoring and Alerting

The audit logging system can be used for:

- **Security Monitoring**: Track failed login attempts, suspicious IP addresses
- **Performance Monitoring**: Identify slow endpoints and performance bottlenecks
- **Usage Analytics**: Understand API usage patterns and user behavior
- **Compliance**: Maintain audit trails for regulatory requirements
- **Troubleshooting**: Debug issues with detailed request/response information

## Troubleshooting

### Common Issues

1. **Audit Logs Not Created**
   - Verify `audit.logging.enabled=true`
   - Check if endpoint is in excluded list
   - Ensure user is authenticated (anonymous requests excluded by default)

2. **Performance Issues**
   - Review database indexes
   - Check async configuration
   - Consider excluding high-traffic endpoints

3. **Storage Issues**
   - Verify automatic cleanup is enabled
   - Adjust retention period
   - Monitor disk space usage

### Debug Logging

Enable debug logging for detailed information:

```yaml
logging:
  level:
    com.hukapp.service.auth.modules.audit: DEBUG
```

## Future Enhancements

- **Real-time Monitoring**: WebSocket-based real-time audit log streaming
- **Advanced Analytics**: Machine learning-based anomaly detection
- **Export Functionality**: Export audit logs to various formats (CSV, JSON, PDF)
- **Dashboard**: Web-based dashboard for audit log visualization
- **Alerting**: Configurable alerts for suspicious activities
