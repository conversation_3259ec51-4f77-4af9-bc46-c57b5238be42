# Subscription-Based Access Control System

## Overview

This document describes the subscription-based access control system implemented for the AVAS API. The system uses Aspect-Oriented Programming (AOP) to enforce subscription requirements on API endpoints, ensuring that users have valid subscriptions before accessing premium features.

## Architecture

### Components

1. **ProductType Enum** - Defines subscription levels with hierarchy
2. **@RequireSubscription Annotation** - Marks endpoints requiring subscriptions
3. **SubscriptionValidationService** - Core business logic for subscription validation
4. **SubscriptionValidationAspect** - AOP aspect that intercepts API calls
5. **SubscriptionRequiredException** - Custom exception for access denial
6. **GlobalExceptionHandler** - Handles subscription exceptions with HTTP 402

### Subscription Levels

The system supports three subscription levels with hierarchical access:

| Level | Name | Access |
|-------|------|--------|
| 1 | BASIC | Entry-level features |
| 2 | PREMIUM | Basic + enhanced features |
| 3 | ENTERPRISE | Premium + advanced features |

Higher subscription levels automatically include access to lower-level features.

## Usage

### Applying Subscription Requirements

#### Class-Level Protection
```java
@RestController
@RequestMapping("api/user/reports")
@RequireSubscription(ProductType.BASIC)
public class ReportController {
    // All endpoints in this controller require Basic subscription
}
```

#### Method-Level Protection
```java
@RestController
public class FeatureController {
    
    @GetMapping("/basic-feature")
    @RequireSubscription(ProductType.BASIC)
    public ResponseEntity<?> basicFeature() {
        // Requires Basic subscription
    }
    
    @GetMapping("/premium-feature")
    @RequireSubscription(ProductType.PREMIUM)
    public ResponseEntity<?> premiumFeature() {
        // Requires Premium subscription
    }
}
```

### Custom Error Messages
```java
@RequireSubscription(
    value = ProductType.PREMIUM,
    message = "This advanced feature requires a Premium subscription"
)
public ResponseEntity<?> customMessageFeature() {
    // Custom error message when access is denied
}
```

## Scope and Exclusions

### Protected Endpoints
- All `/api/**` endpoints (when annotated)
- Automatically enforced through AOP pointcuts

### Excluded Endpoints
- `/api/admin/**` - Admin endpoints which are coupon and payment related (unless explicitly annotated)
- `/auth/user/**` - Authentication endpoints
- Endpoints without `@RequireSubscription` annotation

## Validation Logic

### Subscription Validation Process

1. **Authentication Check** - Verify user is authenticated
2. **Admin Bypass** - Check if admin bypass is enabled and user has admin role
3. **Subscription Query** - Find active payments for the user
4. **Level Validation** - Verify subscription level meets requirement
5. **Expiry Check** - Ensure subscription hasn't expired

### Database Queries

The system uses optimized queries to check subscription status:

```sql
-- Find active payments for user
SELECT p FROM AvasPayment p 
WHERE p.owner = :owner 
AND p.status = 'SUCCESS' 
AND p.validUntil > :currentTime

-- Get highest subscription level
SELECT MAX(p.product.type) FROM AvasPayment p 
WHERE p.owner = :owner 
AND p.status = 'SUCCESS' 
AND p.validUntil > :currentTime
```

## Error Handling

### HTTP 402 Response

When subscription validation fails, the system returns HTTP 402 (Payment Required):

```json
{
  "error": "This feature requires a Premium subscription or higher",
  "requiredSubscription": "Premium Subscription",
  "currentSubscription": "Basic Subscription"
}
```

### Exception Hierarchy

- `SubscriptionRequiredException` - Thrown when subscription is insufficient
- Handled by `GlobalExceptionHandler` - Converts to HTTP 402 response
- Includes detailed subscription information for client handling

## Configuration

### Spring Configuration

The system is automatically configured through:
- `@EnableAspectJAutoProxy` - Enables AOP
- Component scanning picks up the aspect
- No additional configuration required

### Database Schema

Ensure the following entities are properly configured:
- `AvasPayment` - Payment records with validity periods
- `Product` - Subscription products with types
- `Person` - User entities linked to payments

## Testing

### Unit Tests

- `SubscriptionValidationServiceImplTest` - Service layer tests
- `SubscriptionValidationAspectTest` - AOP aspect tests
- `ProductTypeTest` - Enum functionality tests

### Integration Tests

Test subscription validation in real controller scenarios:

```java
@Test
void shouldReturn402WhenSubscriptionRequired() {
    // Test that endpoints return 402 for insufficient subscription
}
```

## Performance Considerations

### Caching
- Consider implementing subscription status caching for high-traffic endpoints
- Cache invalidation on payment status changes

### Database Optimization
- Index on `owner_id`, `status`, and `validUntil` columns
- Consider read replicas for subscription queries

### AOP Performance
- Minimal overhead due to targeted pointcuts
- Efficient annotation-based filtering

## Security Considerations

### Fail-Safe Design
- Denies access on validation errors
- Logs security events for monitoring
- No sensitive information in error responses

### Admin Bypass
- Only available when explicitly enabled
- Requires proper role-based authentication
- Logged for audit purposes

## Monitoring and Logging

### Key Metrics
- Subscription validation failures
- Performance of validation queries
- Admin bypass usage

### Log Events
- Successful validations (DEBUG level)
- Failed validations (WARN level)
- Validation errors (ERROR level)

## Future Enhancements

### Planned Features
1. **Feature-Specific Subscriptions** - Different features requiring different subscription types
2. **Time-Based Access** - Temporary access grants
3. **Usage Quotas** - Limit feature usage per subscription level
4. **Subscription Analytics** - Detailed usage reporting

### Extensibility
The system is designed for easy extension:
- Add new `ProductType` values
- Implement custom validation logic
- Add new annotation parameters
- Integrate with external subscription services

## Troubleshooting

### Common Issues

1. **Aspect Not Working**
   - Verify `@EnableAspectJAutoProxy` is present
   - Check component scanning includes aspect package

2. **Validation Always Fails**
   - Verify payment records have correct status and validity
   - Check system clock synchronization

3. **Performance Issues**
   - Review database indexes
   - Consider subscription caching
   - Monitor query execution times

### Debug Mode

Enable debug logging for detailed validation information:
```yaml
logging:
  level:
    com.hukapp.service.auth.common.aop: DEBUG
    com.hukapp.service.auth.modules.payment.service: DEBUG
```
