

















# Architecture
- User prefers clean architecture principles for implementing features.
- User wants OpenAPI documentation added to controllers.
- User wants OpenAPI/Swagger configuration to support sending requests to external servers, not just localhost.
- User wants all requests to /api/** to be filtered with security checks that return 402 HTTP code for unauthorized requests.
- User wants to implement a bean with a hashmap for String to String key-value pairs that expire after 1 hour.
- User wants new person creation to first send an OTP email for validation before saving to database, with OTP stored in memory cache with 3-minute expiration.
- After sync operations complete successfully, the isNewUser column of the user should be updated to false.

# Features
- User wants to implement features that generate detailed reports for authenticated users.
- User wants a case report endpoint that includes financial information (income, expenses, net income) and task statistics grouped by status and priority.
- User wants financial information (income, expense, and net income) included in user reports.
- User wants task information grouped by status included in user reports.
- User wants task information grouped by priority included in user reports.
- User wants the ability to include photos in responses if requested, potentially fetched from URLs.
- User wants a case details controller with CRUD operations for fields: case type, crime type, derdest (boolean), case value (BigDecimal), case reason, and case title.
- User prefers to implement caseType and crimeType as enums rather than strings.
- User wants to implement a payment module with iyzico integration.
- User wants to implement a feature to check payment status using the iyzico API.
- User wants to implement an email sending module with actual email sending functionality with support for any SMTP server, not just specific providers.
- User wants a module for frequently used case numbers management with add, delete, and get operations.
- User wants a module to calculate expenses for 2025 based on the 2024_2025_tablo_karsilastirma.pdf document.